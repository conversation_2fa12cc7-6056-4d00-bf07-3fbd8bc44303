<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<el-form-item label="机器人">
					<el-select
						v-model="searchOptions.filter.deviceId"
						placeholder="请选择机器人"
						style="width: 100%"
						:fit-input-width="true"
						:teleported="false"
						placement="bottom-start"
						clearable
					>
						<el-option
							v-for="(key, index) in robotsInfo.keys()"
              :key="index"
							:label="robotsInfo.get(key)?.robotName"
							:value="key"
						/>
					</el-select>
				</el-form-item>
			</template>

			<template #searchBtns>
				<el-button @click="onSynchronous" :loading="syncLoading">
					<template #icon>
						<el-icon><ele-Refresh /></el-icon>
					</template>
					同步
				</el-button>
			</template>
		</ViewSearch>

		<Table v-bind="tableOptions" ref="tableRef" @pageChange="onPageChange">
			<template #deviceId="{ row }">
				{{ robotsInfo.get(row.deviceId)?.robotName }}
			</template>
			<template #weekly="{ row }">
				{{ convertWeekdays(row.weekly) }}
			</template>
		</Table>
	</div>
</template>

<script setup lang="ts" name="AiModels">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getPatrolPlans } from '/@/api/home';
import { initPatrolPlans } from '/@/api/patrol';
import { liveInfo } from '/@/stores/fullScreen';
import { storeToRefs } from 'pinia';
const liveStore = liveInfo();
const { robotsInfo } = storeToRefs(liveStore);

// 引入异步组件
const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));

const tableRef = ref<InstanceType<typeof Table>>();
const searchOptions = reactive({
	filter: {
		deviceId: null,
	},
});
const tableOptions: GlobalTableOptions<AiModelRow> = reactive({
	data: [],
	header: [
		{
			title: '计划名称',
			key: 'planName',
			isCheck: true,
		},
		{
			title: '机器人名称',
			key: 'deviceId',
			isCheck: true,
		},
		{
			title: '路线名称',
			key: 'lineName',
			isCheck: true,
		},
		{
			title: '执行日期',
			key: 'weekly',
			isCheck: true,
		},
		{
			title: '开始时间',
			key: 'beginTime',
			isCheck: true,
		},
		{
			title: '结束时间',
			key: 'endTime',
			isCheck: true,
		},
	],
	config: {
		loading: true,
		isSelection: false,
		isSerialNo: true,
		isOperate: false,
		operateWidth: 150, // 操作列宽
		total: 0, // 总条数
	},
	pageParams: {
		page: 1,
		size: 10,
	},
});
function convertWeekdays(weekString: string) {
	const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
	const numberArray = weekString.replace(/\s+/g, '').split(',');
	const result = numberArray.map((weekday: string) => weekdays[parseInt(weekday) - 1]).join('、');

	return result;
}
onMounted(() => {
	initTableData();
});

const initTableData = async () => {
	tableOptions.config.loading = true;
	try {
		const query: any = {
			page: tableOptions.pageParams?.page && tableOptions.pageParams.page - 1,
			size: tableOptions.pageParams?.size,
			...searchOptions.filter,
		};
		const { payload } = await getPatrolPlans(query);
		tableOptions.config.loading = false;
		tableOptions.data = payload.content;
		tableOptions.config.total = payload.totalElements;
	} catch (e) {
		tableOptions.config.loading = false;
		tableOptions.config.total = 0;
		tableOptions.data = [];
	}
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetFilter) {
		searchOptions.filter.deviceId = null;
	}
	if (resetPage) {
		tableRef.value?.pageReset();
	} else {
		initTableData();
	}
	tableRef.value?.clearSelection();
};

const onPageChange = async (page: { pageNum: number; pageSize: number }) => {
	if (tableOptions.pageParams) {
		tableOptions.pageParams.page = page.pageNum;
		tableOptions.pageParams.size = page.pageSize;
	}
	initTableData();
};
const syncLoading = ref(false);
const onSynchronous = () => {
	syncLoading.value = true;
	initPatrolPlans()
		.then(() => {
			ElMessage.success('同步成功');
		})
		.catch(() => {
			ElMessage.error('同步失败');
		})
		.finally(() => {
			syncLoading.value = false;
		});
};
</script>

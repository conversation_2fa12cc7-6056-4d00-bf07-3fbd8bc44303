<template>
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		width="680px"
		align-center
		class="assign-dialog"
	>
		<div class="assign-dialog-container">
			<ul class="assigned-list">
      <li>
        <p>已分配权限</p>
        <p class="short">删除</p>
      </li>
      <li v-for="item in state.assignedPermissions" :key="item.id">
        <p>{{ item.title }}</p>
        <p class="short">
          <el-button size="small" type="danger" circle @click="onRemovePermission(item)">
						<el-icon><ele-Delete /></el-icon>
					</el-button>
        </p>
      </li>
    </ul>
    <ul class="unassigned-list">
      <li>
        <p class="short">分配</p>
        <p>待分配权限</p>
      </li>
      <li v-for="item in unassignedList" :key="item.id">
        <p class="short">
          <el-button size="small" type="primary" circle @click="onAssignPermission(item)">
						<el-icon><ele-Back /></el-icon>
					</el-button>
        </p>
        <p>{{ item.title }}</p>
      </li>
    </ul>
		</div>
		
	</el-dialog>
</template>

<script setup lang="ts">
import { reactive, computed, onMounted } from 'vue';
import { getPermissions, assignRolePermission, unassignRolePermission } from '/@/api/auth';
import { ElMessage } from 'element-plus';

const emits = defineEmits(['refresh']);

const state: AssignDialogState = reactive({
	dialog: {
		isShowDialog: false,
		title: '分配权限',
	},
	roleId: 0,
	assignedPermissions: [], // 角色已分配权限
	permissions: [], // 系统所有权限
});
onMounted(() => {
	initPermissions();
});
const unassignedList = computed(() => {
	const ids = state.assignedPermissions.map((item) => item.name);
	const arr = state.permissions.filter((item) => !ids.includes(item.name));
	return arr;
});
const openDialog = (row: AuthRow) => {
	state.roleId = row.id;
	state.assignedPermissions = row.permissions;
	state.dialog.isShowDialog = true;
};
const initPermissions = async () => {
	const { payload } = await getPermissions();
	state.permissions = payload;
};
const onAssignPermission = async (perm: Permission) => {
	const data = {
		roleId: state.roleId,
		permNames: [perm.name],
	};
	await assignRolePermission(data);
	state.assignedPermissions.push(perm)
	ElMessage.success('权限分配成功');
	emits('refresh');
};
const onRemovePermission = async (perm: Permission) => {
	const data = {
		roleId: state.roleId,
		permNames: [perm.name],
	};
	await unassignRolePermission(data);
	ElMessage.success('权限移除成功');
	const index = state.assignedPermissions.findIndex((p: Permission) => p.name === perm.name)
  state.assignedPermissions.splice(index, 1)
	emits('refresh');
};
defineExpose({
	openDialog,
});
</script>

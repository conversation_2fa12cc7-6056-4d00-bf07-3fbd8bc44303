export default `data:application/javascript,` + encodeURI(`
class RandomNoiseProcessor extends AudioWorkletProcessor {
  count = 0
  buffer = []
  size = 0

  // eslint-disable-next-line no-useless-constructor
  constructor() {
    super()
  }
  //
  static get parameterDescriptors() {
    return [
      {
        name: 'pcmSrc',
        defaultValue: 0,
        zzz: this.pcmSrc
      }
    ]
  }

  process(inputs, outputs, parameters) {
    // 处理左声道
    const [[input]] = inputs
    if (input) {
      // this.buffer.push(new Int8Array([-1, 2, 3, 4]))
      this.buffer.push(new Float32Array(input))
      this.size += input.length

      // count 发送的频率,大概一秒一次 66, 目前设置为2
      const everyTimeSendCount = 2
      this.count++
      if (this.count > everyTimeSendCount) {
        // debugger
        this.port.postMessage({ buffer: this.buffer, size: this.size })
        this.count = 0
        this.buffer = []
        this.size = 0
      }
    }
    return true
  }
}
registerProcessor('audio-processor', RandomNoiseProcessor) `)

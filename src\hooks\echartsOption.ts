import * as echarts from 'echarts';
export default function () {
  let bgColor = 'transparent';
  const axisLine = {
    show: true,
    lineStyle: {
      width: 2,
      color: 'rgba(90, 202, 176, 1)',
    },
    onZero: true,
  };
  const durationOption = (xData: string[]) => {
    return {
      // 通用文本样式
      textStyle: {
        fontSize: 14,
        color: 'rgba(255, 255, 255, .5)',
      },
      legend: {
        show: true,
        icon: 'roundRect',
        itemWidth: 22,
        itemHeight: 11,
        right: '3%',
        textStyle: {
          fontSize: 14,
          color: '#fff',
        },
        itemStyle: {
          borderRadius: 20, // 设置圆角矩形的圆角大小
          borderWidth: 1, // 图例项边框宽度\
          // color: 'rgba(104, 207, 173, 1)',
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1, // 渐变方向，从左上到右下
            [
              { offset: 0, color: 'rgba(104, 207, 173, 1)' },
              { offset: 1, color: 'rgba(104, 207, 173, 0.3)' },
            ]
          )
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        textStyle: {
          color: '#fff'
        },
        backgroundColor: 'rgba(50,50,50,0.7)',
        borderColor: 'rgba(104, 207, 173, 1)',
        formatter: (params: any) => {
          if (!params[0]) return
          var axisValueLabel = params[0].axisValueLabel;
          var seriesName = params[0].seriesName;
          var value = params[0].value.toFixed(2);
          return `<div>
                    <h3>${axisValueLabel}</h3>
                    <p>${value}${seriesName}</p>
                  </div>`;
        }
      },
      grid: {
        top: '9%',
        left: '3%',
        right: 0,
        bottom: 0,
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        axisTick: {
          show: false,
        },
        axisLine,
        axisLabel: {
          margin: 10,
        },
        data: xData
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: true, // 表示显示网格线
          lineStyle: {
            width: 1,
            type: 'dashed',
            color: 'rgba(90, 202, 176, 0.5)',
          },
        },
        axisLine,
        axisTick: {
          show: false,
        },
      },
      series: [
        {
          // 背景
          type: 'pictorialBar',
          name: '小时',
          tooltip: { show: false },
          zlevel: 0,
          barWidth: 20,
          symbol: 'roundRect',
          symbolSize: [20, 10],
          symbolMargin: 2,
          symbolRepeat: 'fixed',
          silent: true,
          itemStyle: {
            color: 'rgba(45, 106, 91, 0.5)',
          },
          seriesLayoutBy: 'column',
          data: []
        },
        {
          // 前景
          type: 'pictorialBar',
          name: '小时',
          zlevel: 1,
          barWidth: 20,
          symbol: 'roundRect',
          symbolSize: [20, 10],
          symbolMargin: 2,
          symbolRepeat: true,
          symbolClip: true,
          tooltip: { show: false },
          itemStyle: {
            color: 'rgba(104, 207, 173, 1)',
          },
          seriesLayoutBy: 'column',
          data: []
        },
        {
          // 渐变
          type: 'bar',
          name: '小时',
          zlevel: 2,
          barWidth: 20,
          tooltip: { show: true },
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'transparent',
                },
                {
                  offset: 1,
                  color: 'rgba(0,0,0,0.9)',
                },
              ],
              global: false,
            },
          },
          seriesLayoutBy: 'column',
          data: []
        },
      ],
    }
  }



  const mileageOption = (xData: string[], sData: number[]) => {
    return {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: "axis",
        axisPointer: {
          lineStyle: {
            color: "rgba(90, 202, 176, 0.5)",
          },
        },
        textStyle: {
          color: '#fff'
        },
        backgroundColor: 'rgba(50,50,50,0.7)',
        borderColor: 'rgba(104, 207, 173, 1)',
        formatter: (params: any) => {
          if (!params[0]) return
          var axisValueLabel = params[0].axisValueLabel;
          var seriesName = params[0].seriesName;
          var value = params[0].value;
          return `<div>
                    <h3>${axisValueLabel}</h3>
                    <p>${value}${seriesName}</p>
                  </div>`;
        }

      },
      legend: {
        show: true,
        icon: 'roundRect',
        right: 0,
        itemWidth: 20,
        itemHeight: 3,
        textStyle: {
          fontSize: 14,
          color: '#fff',
        },
        itemStyle: {
          color: 'rgba(90, 202, 176, 1)',
        },
      },
      grid: {
        top: '9%',
        left: '3%',
        right: 0,
        bottom: 0,
        containLabel: true,
      },
      textStyle: {
        fontSize: 14,
        color: 'rgba(255, 255, 255, .5)',
      },
      xAxis: {
        type: 'category',
        axisLine,
        axisLabel: {
          margin: 10,
        },
        axisTick: { show: false },
        data: xData,
      },
      yAxis: {
        type: 'value',
        axisLine,
        splitLine: {
          show: true, // 表示显示网格线
          lineStyle: {
            type: 'dashed',
            color: 'rgba(90, 202, 176, 0.5)',
          },
        },
        axisTick: { show: false },
      },
      series: {
        name: '公里',
        type: 'line',
        smooth: false, //是否平滑曲线显示
        symbolSize: 0,
        lineStyle: {
          width: 2,
          color: 'rgba(90, 202, 176, 1)',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              { offset: 0, color: 'rgba(104, 207, 173, 1)' },
              { offset: 0.95, color: 'rgba(35, 147, 200, 0)' },
            ],
            false
          ),
          // shadowColor: 'rgba(53,142,215, 0.9)', //阴影颜色
          // shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
        },
        data: sData,
      },

    };
  }
  const taskOption = (xData: string[], sData: number[]) => {

    return {
      backgroundColor: bgColor,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        textStyle: {
          color: '#fff'
        },
        backgroundColor: 'rgba(50,50,50,0.7)',
        borderColor: 'rgba(104, 207, 173, 1)',
        formatter: (params: any) => {
          if (!params[0]) return
          var axisValueLabel = params[0].axisValueLabel;
          var seriesName = params[0].seriesName;
          var value = params[0].value;
          return `<div>
                    <h3>${axisValueLabel}</h3>
                    <p>${value}${seriesName}</p>
                  </div>`;
        }
      },
      legend: {
        show: true,
        icon: 'rect',
        right: 0,
        itemWidth: 20,
        itemHeight: 10,
        textStyle: {
          fontSize: 14,
          color: '#fff',
        },
      },
      textStyle: {
        fontSize: 14,
        color: 'rgba(255, 255, 255, .5)',
      },
      grid: {
        top: '9%',
        left: '3%',
        right: 0,
        bottom: 0,
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: xData,
        offset: 1,
        axisLine,
        axisTick: {
          show: false,
        },
        axisLabel: {
          margin: 10,
        },
      },
      yAxis: {
        axisTick: {
          show: false,
        },
        axisLine,
        splitLine: {
          show: true, // 表示显示网格线
          lineStyle: {
            type: 'dashed',
            color: 'rgba(90, 202, 176, 0.5)',
          },
        },
      },
      series: {
        type: 'bar',
        name: '次',
        data: sData,
        barWidth: '20px',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1,
            [
              {
                offset: 0,
                color: 'rgba(104, 207, 173, 1)', // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(35, 147, 200, 0)', // 100% 处的颜色
              },
            ],
            false
          ),
        },
      },

    }
  }


  return { durationOption, mileageOption, taskOption };
}
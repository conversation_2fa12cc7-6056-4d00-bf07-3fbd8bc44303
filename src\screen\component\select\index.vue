<template>
	<el-select
		v-model="innerValue"
		:multiple="multiple"
		:clearable="clearable"
		collapse-tags
		:teleported="false"
		fit-input-width
		:placeholder="placeholder"
		class="data-screen-select"
		@change="handleChange"
		@clear="handleClear"
		:filterable="filterable"
		remote
		:remote-method="remoteSearch"
		:loading="loading"
		:filter-method="filterMethod"
		reserve-keyword
	>
		<el-option
			v-for="item in searchOptions"
			:key="item[keyField]"
			:label="item[labelField]"
			:value="item[valueField]"
		>
			<div class="flex items-center">
				{{ item[labelField] }}
				<Icon :model-value="isOptionSelected(item)" />
			</div>
		</el-option>
		<template #tag>
			<el-tag v-for="color in innerValue" :key="color" :color="color" />
		</template>
	</el-select>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted } from 'vue';
import Icon from './icon.vue';
import { debounce } from 'lodash-es';

// 定义 props
interface Props {
	options: Record<string, any>[];
	keyField?: string;
	labelField?: string;
	valueField?: string;
	modelValue?: any;
	placeholder?: string;
	multiple?: boolean;
	clearable?: boolean;
	filterable?: boolean;
	onSearch?: (query: string) => Promise<any[]>;
}

const props = withDefaults(defineProps<Props>(), {
	options: () => [],
	keyField: 'value',
	labelField: 'label',
	valueField: 'value',
	modelValue: () => [],
	placeholder: '请选择',
	multiple: false,
	clearable: true,
	onSearch: undefined,
	filterable: false,
});

// 定义 emit
const emit = defineEmits(['update:modelValue', 'change', 'clear', 'search']);

// 内部状态
const innerValue = ref(props.modelValue);
const loading = ref(false);
const searchOptions = ref<any[]>(props.options);

// 检查选项是否被选中
const isOptionSelected = (item: any) => {
	const itemValue = item[props.valueField];
	if (props.multiple && Array.isArray(innerValue.value)) {
		return innerValue.value.includes(itemValue);
	}
	return innerValue.value === itemValue;
};

// 初始化
onMounted(() => {
	searchOptions.value = props.options;
});

// 远程搜索方法
const remoteSearch = debounce(async (query: string) => {
	if (query === '') {
		searchOptions.value = props.options;
		return;
	}

	loading.value = true;
	try {
		if (props.onSearch) {
			// 如果提供了自定义搜索方法，使用它
			searchOptions.value = await props.onSearch(query);
		} else {
			// 否则使用本地过滤
			searchOptions.value = props.options.filter((item) => {
				const label = String(item[props.labelField]).toLowerCase();
				return label.includes(query.toLowerCase());
			});
		}
	} catch (error) {
		console.error('搜索失败:', error);
		searchOptions.value = [];
	} finally {
		loading.value = false;
	}
}, 300);

// 本地过滤方法
const filterMethod = (query: string) => {
	const searchValue = query.toLowerCase();
	return props.options.filter((item) => {
		const label = String(item[props.labelField]).toLowerCase();
		return label.includes(searchValue);
	});
};

// 处理选择变化
const handleChange = (val: any) => {
	innerValue.value = val;
	emit('update:modelValue', val);
	emit('change', val);
};

// 处理清除事件
const handleClear = () => {
	innerValue.value = props.multiple ? [] : undefined;
	emit('update:modelValue', innerValue.value);
	emit('change', innerValue.value);
	emit('clear');
};

// 监听 options 变化
watch(
	() => props.options,
	(newOptions) => {
		if (!loading.value) {
			searchOptions.value = newOptions;
		}
	}
);

// 监听 modelValue 变化
watch(
	() => props.modelValue,
	(newValue) => {
		innerValue.value = newValue;
	},
	{ deep: true }
);
</script>

<style lang="scss" scoped>
.items-center {
	align-items: center;
	justify-content: space-between;
	color: #fff;
}

.bg {
	background: linear-gradient(
		to right,
		rgba(22, 226, 231, 0),
		rgba(33, 227, 221, 0.3),
		rgba(79, 229, 181, 1),
		rgba(118, 231, 146, 0.3),
		rgba(136, 232, 130, 0)
	);
}

:deep(.el-input__wrapper) {
	color: #fff;
	height: 100%;
	padding: 0 vw(20);
	box-shadow: none;
	border-radius: 0;
	@extend .bg;
	&:hover {
		box-shadow: none;
	}
	.el-input__inner {
		height: 100%;
		color: #fff;
	}
}
:deep(.el-select-tags-wrapper .el-tag) {
	background-color: transparent;
	color: #fff;
	.el-tag__close {
		color: #ccc;
		&:hover {
			background-color: transparent;
			color: #fff;
		}
	}
}
:deep(.select-trigger) {
	height: 100%;
}
:deep(.el-input) {
	height: 100%;
}
// Select
.el-select {
	width: vw(253);
	height: vw(40);
	padding: vw(5) 0;
	.select-trigger {
		height: 100%;
	}
	// 优化背景渐变效果，增加更多的颜色停止点并调整透明度过渡
	background: linear-gradient(
		to right,
		rgba(22, 226, 231, 0),
		rgba(33, 227, 221, 0.08) 20%,
		rgba(33, 227, 221, 0.15) 30%,
		rgba(14, 55, 57, 0.15) 70%,
		rgba(136, 232, 130, 0.08) 80%,
		rgba(136, 232, 130, 0) 100%
	);

	// 添加上下边框渐变效果
	&::before,
	&::after {
		content: '';
		position: absolute;
		left: 0;
		right: 0;
		height: 1px;
	}

	&::before {
		top: 0;
	}

	&::after {
		bottom: 0;
	}

	// 添加左右边框
	box-shadow: 1px 0 0 0 rgba(14, 55, 57, 1), -1px 0 0 0 rgba(14, 55, 57, 1);

	.el-select__suffix .el-icon {
		color: #fff;
	}

	:deep(.el-popper) {
		border: none;
		border-radius: 0;
		padding: 0 vw(5);
		.el-select-dropdown__item {
			padding: 0 20px;
		}
		.el-select-dropdown__item::after {
			display: none !important;
		}
		.el-popper__arrow {
			display: none;
		}
		.el-select-dropdown__item {
			padding: 0 vw(10) 0 vw(20);
			margin-bottom: vw(5);
			background: linear-gradient(
				to right,
				rgba(22, 226, 231, 0),
				rgba(33, 227, 221, 0.3),
				rgba(79, 229, 181, 0.2),
				rgba(136, 232, 130, 0)
			);
			.custom-option {
				display: flex;
				justify-content: space-between;
				color: #fff;
				.el-radio .el-radio__label {
					display: none;
				}
			}
		}
		.el-select-dropdown__item.selected {
			@extend .bg;
			&::after {
				display: none;
			}
		}
	}

	:deep(.el-input__inner) {
		color: #fff;
		background: transparent;
		&::placeholder {
			color: rgba(255, 255, 255, 0.7);
		}
	}

	:deep(.el-select__tags) {
		background: transparent;
	}

	:deep(.el-select-dropdown__empty) {
		color: #fff;
		padding: vw(10);
	}

	:deep(.el-select-dropdown__loading) {
		color: #fff;
	}
}
</style>

<template>
	<div class="left-container">
		<!-- stat -->
		<div class="module stat">
			<div class="module-title">
				<span
					><img class="trend" src="/src/assets/sd/data-screen/trend.png" alt="" />多样性指数
				</span>
			</div>
			<div class="module-cont stat-cont">
				<div class="icon"><img src="/src/assets/sd/data-screen/dyxzs.png" alt="" /></div>
				<div class="text">
					<div>发现物种种类</div>
					<div class="count text-sd">{{ stat }}</div>
				</div>
			</div>
		</div>
		<!-- type monitor trend -->
		<div class="module monitor-trend">
			<div class="module-title">
				<span
					><img class="trend" src="/src/assets/sd/data-screen/trend.png" alt="" />多样性趋势
				</span>
			</div>

			<div class="monitor-trend-cont">
				<Echarts id="typeMonitorTrendChart" ref="typeMonitorTrendChartRef"></Echarts>
			</div>
		</div>

		<div class="module recent-monitor">
			<div class="module-title">
				<span
					><img class="device" src="/src/assets/sd/data-screen/device.png" alt="" />最近发现
				</span>
				<img @click="onToMore" src="/src/assets/sd/data-screen/more.png" alt="" />
			</div>
			<div class="module-cont recent-monitor-cont">
				<RecentMonitor
					:recentMonitorEvents="recentMonitorEvents"
					animationName="animate__fadeInLeft"
				></RecentMonitor>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import {
	mergeLineOptions,
	ECOption,
	chartLinearColors,
	transDate,
} from '/@/components/echarts/mergeOptions';
const router = useRouter();
import {
	getSpeciesStat,
	getRecentDiscovered,
	getSpeciesTypeTrend,
} from '/@/api/sd/dataScreen/bird';

import { useInterval, markNewData } from '/@/hooks/useInterval';
import RecentMonitor from '../component/recentMonitor/index.vue';
import Echarts from '/@/components/echarts/echarts.vue';
type Chart = InstanceType<typeof Echarts>;
const props = defineProps<{
	timeType: number | null;
}>();

// 多样性指数
const stat = ref(0); // 总发现物种数量
const getStat = async () => {
	const query = {
		timeType: props.timeType,
	};
	const { payload } = await getSpeciesStat(query);
	stat.value = payload.speciesType || 0;
};

// 多样性趋势
const typeMonitorTrendChartRef = ref<Chart>();
const getTypeMonitorTrend = async () => {
	const query = {
		timeType: props.timeType,
	};
	const { payload } = await getSpeciesTypeTrend(query);
	const chartData = payload.map((et: SpeciesEvent) => {
		const date = transDate(props.timeType, et.date);
		return [date, et.speciesType];
	});
	const options: ECOption = {
		dataset: {
			source: chartData,
		},
		color: [chartLinearColors[1]],
		series: [
			{
				type: 'line',
				name: '发现物种种类',
				symbol: 'circle',
				symbolSize: 6,
				showSymbol: false,
				areaStyle: {
					color: chartLinearColors[3],
				},
			},
		],
	};
	typeMonitorTrendChartRef.value?.resetOption(mergeLineOptions(options));
};

// 最近发现
const onToMore = () => {
	router.push({ name: 'DataScreenAtlas', query: { pagePath: 'bird' } });
};
const recentMonitorEvents = ref<RecentMonitorEvent[]>([]);
const getRecentMonitorEvents = async () => {
	const query = {
		timeType: props.timeType,
		rankCount: 2,
	};
	const { payload } = await getRecentDiscovered(query);

	recentMonitorEvents.value = markNewData(payload.content, recentMonitorEvents.value, 'eventId');
};
useInterval(getRecentMonitorEvents);
watch(
	() => props.timeType,
	() => {
		recentMonitorEvents.value = [];
		getRecentMonitorEvents();
		getStat();
		getTypeMonitorTrend();
	},
	{ immediate: true }
);
</script>

<style lang="scss" scoped>
.stat .stat-cont {
	width: vw(367);
	height: vw(123);
	vertical-align: bottom;
	display: flex;
	align-items: center;
	.icon {
		margin: 0 vw(20) 0 vw(25);
		img {
			width: vw(100);
			height: vw(100);
		}
	}
	.text {
		color: $sd-screen-primary;
		font-size: vw(14);
		line-height: vw(36);
		.count {
			color: #fff;
			font-size: vw(38);
			font-family: 'ALiMaMaShuHeiTi';
		}
	}
}
.species-frequency .species-frequency-cont {
	overflow: visible;
	.rank-list {
		width: calc(100% + vw(40));
		position: relative;
		left: vw(-20);
		min-height: vw(330);
		max-height: vw(330);
		background: url('/@/assets/sd/data-screen/rank-bg.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		// background: linear-gradient(to bottom, transparent, rgba(33, 227, 221, 0.2) 40%, rgba(79, 229, 181, 0.2) 60%, transparent);
		.rank-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 vw(36) vw(12);
			.name {
				flex: 1;
				font-size: vw(16);
				.tag-tooltips {
					display: inline-block;
					width: vw(16);
					height: vw(16);
					vertical-align: text-top;
					margin-left: vw(4);
					color: $sd-screen-primary;
					cursor: pointer;
				}
			}
			// .tag {
			//   border: 1px solid $sd-screen-primary;
			//   color: $sd-screen-primary;
			//   font-size: vw(12);
			//   padding: 0 vw(8);
			//   border-radius: vw(8);
			//   margin-left: vw(10);
			// }
		}
	}
}
.monitor-trend .monitor-trend-cont {
	width: 100%;
	height: vh(260);
}
</style>

import request from '/@/utils/request';

/**
 * @method getAiModels 获取模型列表
 * @method createAiModel 新增
 * @method updateAiModel 修改
 * @method deleteAiModel 删除
 */

export function getAiModels(params?: Object) {
  return request({
    url: `/ai-models`,
    method: 'get',
    params,
  });
}

export function createAiModel(params: Object) {
  return request({
    url: `/ai-models/create`,
    method: 'post',
    data: params,
  });
}

export function updateAiModel(params: Object) {
  return request({
    url: `/ai-models/update`,
    method: 'post',
    data: params,
  });
}

export function deleteAiModel(id: string) {
  return request({
    url: `/ai-models/${id}`,
    method: 'delete',
  });
}


export function batchDeleteAiModel(ids: string[]) {
  return request({
    url: `/ai-models/del`,
    method: 'delete',
    data: ids
  });
}



<template>
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		width="580px"
		align-center
	>
		<el-form ref="dialogFormRef" :model="state.ruleForm" label-width="80px" size="large" label-position="right">
			<el-form-item
				label="设备类型"
				prop="type"
				:rules="[{ required: true, message: '识别类型不能为空', trigger: 'blur' }]"
			>
				<el-radio-group v-model="state.ruleForm.type">
					<el-radio :label="1">巡护设备</el-radio>
					<!-- <el-radio :label="2">摄像头</el-radio> -->
				</el-radio-group>
			</el-form-item>
			<el-form-item
				v-if="state.ruleForm.type === 1"
				label="设备"
				prop="robotId"
				:rules="[{ required: true, message: '设备不能为空', trigger: 'blur' }]"
			>
				<el-select v-model="state.ruleForm.robotId" placeholder="请选择设备" clearable style="width: 100%;">
					<el-option
						v-for="item in state.robutOptions"
						:key="item.deviceId"
						:label="item.deviceId + '-' + item.robotName"
						:value="item.deviceId"
					/>
				</el-select>
			</el-form-item>
			<el-form-item
				v-if="state.ruleForm.type === 2"
				label="设备"
				prop="deviceId"
				:rules="[{ required: true, message: '设备不能为空', trigger: 'blur' }]"
			>
				<el-select v-model="state.ruleForm.deviceId" placeholder="请选择" clearable style="width: 100%;">
					<el-option
						v-for="item in devices.data.value"
						:key="item.id"
						:label="`设备编号：${item.num}，名称：${item.channelName || item.name}`"
						:value="item.id"
					/>
				</el-select>
			</el-form-item>

			<el-form-item
				label="名称"
				prop="name"
				:rules="[{ required: true, message: '名称不能为空', trigger: 'blur' }]"
			>
				<el-input v-model="state.ruleForm.name" placeholder="请输入" clearable></el-input>
			</el-form-item>

			<el-form-item
				label="时间"
				prop="timePeriod"
				:rules="[{ required: true, message: '起止时间不能为空', trigger: 'blur' }]"
			>
				<el-date-picker
					v-model="state.ruleForm.timePeriod"
					type="datetimerange"
					range-separator="-"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					value-format="YYYY-MM-DD HH:mm:ss"
				/>
			</el-form-item>
			<el-form-item label="代表图" prop="picId">
				<el-upload
					action="#"
					:multiple="false"
					list-type="picture-card"
					accept="image/jpg,image/png,image/jpeg"
					:show-file-list="false"
					:http-request="onUploadHttpRequest"
				>
					<el-image v-if="state.uploadedPicUrl" :src="state.uploadedPicUrl"></el-image>
					<el-icon v-else><ele-Plus /></el-icon>
					<template #tip>
						<div class="el-upload__tip mt0">
							图片格式仅支持jpg/jpeg/png，且大小不能超过50M。
						</div>
					</template>
				</el-upload>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="onCancel">取 消</el-button>
				<el-button type="primary" @click="onSubmit">{{ state.dialog.submitTxt }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import type { FormInstance, UploadRequestOptions } from 'element-plus';
import { createLive, updateLive, getRobots } from '/@/api/lives';
import { ElMessage } from 'element-plus';
import { useDevices } from '/@/hooks/useDevices';
import { uploadFile, readerFile } from '/@/api/upload';

const emits = defineEmits(['refresh', 'resetAiModels']);

const devices = useDevices();
const dialogFormRef = ref<FormInstance>();
const state: LiveDialogState = reactive({
	ruleForm: {
		type: 1,
		name: '',
		timePeriod: [],
		picId: '',
	},
	uploadedPicUrl: '', // 已上传图片url
	robutOptions: [],
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
});

const openDialog = (row?: LiveRow) => {
	state.dialog.isShowDialog = true;
	getRobutOptions();
	nextTick(() => {
		dialogFormRef.value?.resetFields();
		if (row && row.id) {
			state.dialog.type = 'edit';
			state.dialog.title = '修改直播';
			state.dialog.submitTxt = '修 改';
			state.ruleForm = {
				id: row.id,
				type: row.type,
				robotId: row.type === 1 ? row.robotId : null,
				deviceId: row.type === 2 ? row.deviceId : null,
				name: row.name,
				timePeriod: [row.startTime, row.endTime],
				picId: row.picId,
			};
			state.uploadedPicUrl = row.picUrl;
		} else {
			state.dialog.type = 'add';
			state.dialog.title = '新增直播';
			state.dialog.submitTxt = '新 增';
			state.ruleForm = {
				type: 1,
				robotId: null,
				deviceId: null,
				name: '',
				timePeriod: [],
				picId: '',
			};
			state.uploadedPicUrl = '';
		}
	})
};

const getRobutOptions = async () => {
	const { payload } = await getRobots();
	state.robutOptions = payload;
};

// 上传代表图
const onUploadHttpRequest = async (options: UploadRequestOptions) => {
	const formData = new FormData();
	formData.append('file', options.file);
	state.uploadedPicUrl = await readerFile(options.file) as string;
	try {
		const { payload } = await uploadFile(formData);
		state.ruleForm['picId'] = payload && payload.length > 0 ? payload[0].id : '';
	} catch {
		state.ruleForm['picId'] = '';
		state.uploadedPicUrl = '';
	}
};

const onCancel = () => {
	state.dialog.isShowDialog = false;
};

const onSubmit = () => {
	dialogFormRef.value?.validate((valid: boolean, ) => {
		if (!valid) return;
		const data: LiveForm = {
			type: state.ruleForm.type,
			name: state.ruleForm.name,
			startTime: state.ruleForm.timePeriod && state.ruleForm.timePeriod[0],
			endTime: state.ruleForm.timePeriod &&  state.ruleForm.timePeriod[1],
			picId: state.ruleForm.picId,
		}
		if (data.type === 1) {
			data.robotId = <number>state.ruleForm.robotId;
		} else {
			data.deviceId = <string>state.ruleForm.deviceId;
		}
		if (state.dialog.type === 'add') {
			createLive(data).then(() => {
				ElMessage.success('新增成功');
				onCancel();
				emits('refresh', true, true);
			})
			return;
		}
		data.id = state.ruleForm.id;
		updateLive(state.ruleForm).then(() => {
			ElMessage.success('修改成功');
			onCancel();
			emits('refresh');
		})
	})
};

defineExpose({
	openDialog,
});
</script>

<template>
  <div class="layout-pd">
    <ViewSearch @onRefreshData="onRefresh">
      <template #searchFilters>
        <el-form-item label="设备">
          <el-select v-model="searchOptions.filter.deviceId" placeholder="请选择" clearable>
            <el-option
              v-for="item in devices.data.value" :key="item.id"
              :label="`设备编号：${item.num}，名称：${item.name}${item.channelName ? `[${item.channelName}]` : ''}`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模型">
          <el-select v-model="searchOptions.filter.aiModelId" placeholder="请选择" clearable>
            <el-option v-for="item in aiModels.data.value" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchOptions.filter.monitorStatus" placeholder="请选择" clearable>
            <el-option v-for="item in searchOptions.monitorStatusOptions" :key="item.value" :label="item.label"
              :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="监测任务名称">
          <el-input v-model="searchOptions.filter.name" placeholder="请输入监测任务名称" clearable>
            <template #suffix>
							<el-icon><ele-Search /></el-icon>
						</template>
          </el-input>
        </el-form-item>
      </template>

      <template #searchBtns>
        <div v-auths="['*:*:*', 'monitors:*:*']">
          <el-button  type="primary" @click="onCreate">
            <template #icon>
              <el-icon><ele-Plus /></el-icon>
            </template>
            新增
          </el-button>
          <el-button type="success" :disabled="tableRef?.selectRows.length !== 1" @click="onUpdateRow(null)">
            <template #icon>
              <el-icon><ele-Edit /></el-icon>
            </template>
            修改
          </el-button>
          <el-button type="danger" :disabled="tableRef?.selectRows.length === 0" @click="onBatchDelete">
            <template #icon>
              <el-icon><ele-Delete /></el-icon>
            </template>
            删除
          </el-button>
          <el-button type="primary" @click="onCheckStatus">
            <template #icon>
              <el-icon><ele-Warning /></el-icon>
            </template>
            检查任务状态
          </el-button>
        </div>
      </template>
    </ViewSearch>

    <Table v-bind="tableOptions" ref="tableRef" @pageChange="onPageChange">
      <template #deviceId="{ row }">
        {{ (row.deviceModel &&
          `设备编号：${row.deviceModel.num}，名称：${row.deviceModel.name}${row.deviceModel.channelName ? `[${row.deviceModel.channelName}]`: '' }`)
          || '-' }}
      </template>

      <template #aiModelId="{ row }">
        {{ (row.aiModel && row.aiModel.name) || '-' }}
      </template>

      <template #monitorRunTime="{ row }">
        {{
          row.monitorRunTime ?
          row.monitorRunStatus === 0 ?
            formatDuration(row.monitorRunTime, row.monitorStopTime)
            : formatDuration(row.monitorRunTime)
          : '-'
        }}
      </template>

      <template #monitorStopTime="{ row }">
        {{ (row.monitorRunStatus === 0 && row.monitorStopTime) ? row.monitorStopTime : '-' }}
      </template>

      <template #monitorStatus="{ row }">
        <div v-if="row.monitorRunStatus === 1" class="custom-badge"
          :class="row.monitorStatus === 0 ? 'success' : 'danger'">
          {{ row.monitorStatus === 0 ? '运行中' : row.monitorStatus === 1 ? '模型异常' : '设备异常' }}
        </div>
        <div v-else class="custom-badge info">未启动</div>
      </template>

      <template #operate="{ row }">
        <!-- monitorRunStatus: 0未启动 1启动中 -->
        <el-button v-if="row.monitorRunStatus === 1" size="small" text type="primary" @click="onStartStop(row, 'stop')">
          <el-icon><ele-Pointer /></el-icon>
          停止
        </el-button>
        <el-button v-else size="small" text type="primary" @click="onStartStop(row, 'start')">
          <el-icon><ele-Pointer /></el-icon>
          启动
        </el-button>
        <el-button size="small" text type="primary" @click="onCopyRow(row)">
          <el-icon><ele-CopyDocument /></el-icon>
          复制
        </el-button>
        <el-button size="small" text type="primary" @click="onUpdateRow(row)">
          <el-icon><ele-EditPen /></el-icon>
          修改
        </el-button>
        <el-button size="small" text type="primary" @click="onDelRow(row.id)">
          <el-icon><ele-Delete /></el-icon>
          删除
        </el-button>
      </template>
    </Table>

    <CreateDialog
      ref="createDialogRef"
      :devices="devices.data.value"
      :aiModels="aiModels.data.value"
      @refresh="onRefresh"
    ></CreateDialog>
  </div>
</template>

<script setup lang="ts" name="Monitors">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getMonitors, deleteMonitor, batchDeleteMonitor, startMonitor, stopMonitor, checkStatus } from '/@/api/monitors';
import { formatDuration } from '/@/utils/formatTime';
import { AUTHS } from '/@/directive/authDirective';
import { useDevices } from '/@/hooks/useDevices';
import { useAiModels } from '/@/hooks/useAiModels';

// 引入异步组件
const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));
const CreateDialog = defineAsyncComponent(() => import('./dialog.vue'));

const tableRef = ref<InstanceType<typeof Table>>();
const devices = useDevices();
const aiModels = useAiModels();
const searchOptions = reactive({
  filter: {
    deviceId: '',
    aiModelId: '',
    monitorStatus: '',
    name: '',
    sort: 'createTime,desc',
  },
  monitorStatusOptions: [
    { label: '未启动', value: 3 },
    { label: '运行中', value: 0 },
    { label: '模型异常', value: 1 },
    { label: '设备异常', value: 2 },
  ] as SelectOptions[],
});
const tableOptions: GlobalTableOptions<MonitorRow> = reactive({
  data: [],
  header: [
    {
      title: '监测任务名称',
      key: 'name',
      isCheck: true,
      colWidth: 200,
    },
    {
      title: '关联设备',
      key: 'deviceId',
      isCheck: true,
    },
    {
      title: '关联模型',
      key: 'aiModelId',
      isCheck: true,
    },
    {
      title: '监测运行时长',
      key: 'monitorRunTime',
      isCheck: true,
      colWidth: 160,
    },
    {
      title: '最近停止时间',
      key: 'monitorStopTime',
      isCheck: true,
      colWidth: 160,
      // isDate: true,
      // format: 'YYYY-mm-dd HH:MM:SS',
    },
    {
      title: '状态',
      key: 'monitorStatus',
      isCheck: true,
      colWidth: 100,
    },
  ],
  config: {
    loading: true,
    isSelection: true,
    isSerialNo: true,
    isOperate: AUTHS(['*:*:*', 'monitors:*:*']), // 是否显示操作列
    operateWidth: 240, // 操作列宽
    total: 0, // 总条数
  },
  pageParams: {
    page: 1,
    size: 10,
  },
});

onMounted(() => {
  initTableData();
});

const initTableData = async () => {
  tableOptions.config.loading = true;
  try {
    const query: any = {
      page: tableOptions.pageParams?.page && tableOptions.pageParams.page - 1,
      size: tableOptions.pageParams?.size,
      ...searchOptions.filter,
    };
    if (typeof query.monitorStatus === 'number') {
      if (query.monitorStatus === 3) {
        query.monitorRunStatus = 0;
        delete query.monitorStatus;
      } else {
        query.monitorRunStatus = 1;
      }
    }
    const { payload } = await getMonitors(query)
    tableOptions.config.loading = false;
    tableOptions.data = payload.content;
    tableOptions.config.total = payload.totalElements;
  } catch (e) {
    tableOptions.config.loading = false;
    tableOptions.config.total = 0;
    tableOptions.data = [];
  }
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
  if (resetFilter) {
    searchOptions.filter.deviceId = '';
    searchOptions.filter.aiModelId = '';
    searchOptions.filter.monitorStatus = '';
    searchOptions.filter.name = '';
  }
  if (resetPage) {
    tableRef.value?.pageReset();
  } else {
    initTableData();
  }
  tableRef.value?.clearSelection();
};

const onPageChange = async (page: { pageNum: number; pageSize: number; }) => {
  if (tableOptions.pageParams) {
    tableOptions.pageParams.page = page.pageNum;
    tableOptions.pageParams.size = page.pageSize;
  }
  initTableData();
};

const onStartStop = async (row: MonitorRow, type: string) => {
  if (type === 'start') {
    await startMonitor({ id: row.id as string });
    ElMessage.success('启动成功');
    onRefresh();
  } else if (type === 'stop') {
    await stopMonitor({ id: row.id as string });
    onRefresh();
    ElMessage.success('停止成功');
  }
}

const onBatchDelete = () => {
  ElMessageBox({
    title: '提示',
    message: '此操作将永久删除，是否继续?',
    type: 'warning',
    showCancelButton: true,
  }).then(async () => {
    const ids = tableRef?.value?.selectRows.map((item) => item.id) as string[];
    await batchDeleteMonitor(ids);
    ElMessage.success('删除成功');
    onRefresh();
  })
    .catch(() => { })
}

const onDelRow = (rowId: string) => {
  ElMessageBox({
    title: '提示',
    message: '此操作将永久删除，是否继续?',
    type: 'warning',
    showCancelButton: true,
  }).then(async () => {
    await deleteMonitor(rowId);
    ElMessage.success('删除成功');
    onRefresh();
  })
    .catch(() => { })
};

const onCheckStatus = async () => {
  ElMessage.warning('开始检查');
	await checkStatus();
	ElMessage.success('监测任务检查完成，状态已更新');
	onRefresh();
};

const createDialogRef = ref<InstanceType<typeof CreateDialog>>()
const onCreate = () => {
  createDialogRef.value?.openDialog('add');
};
const onUpdateRow = (row: MonitorRow | null) => {
  if (!row) {
    row = tableOptions.data.find((item) => item.id === tableRef?.value?.selectRows[0].id) as MonitorRow;
  }
  createDialogRef.value?.openDialog('edit', row);
};
const onCopyRow = (row: MonitorRow) => {
  createDialogRef.value?.openDialog('copy', row);
};
</script>

import request from '/@/utils/request';

/**
 * @method getRawMaterials 获取 `1红外感知抓拍、3监控巡航抓拍、5巡护感知抓拍、6测试数据` 等原始素材
 * @method getSnapRawMaterials 获取 `2监控定时抓拍、4巡护定时抓拍` 等原始素材 
 * @method downloadFile 下载文件
 * 
 * # 防火素材
 * @method getFireMaterials 分页查询防火素材
 */
export function getRawMaterials(data: Object) {
	return request({
		url: '/images',
		method: 'get',
		params: data,
	});
}

export function getSnapRawMaterials(data: Object) {
	return request({
    url: '/monitor-event-files/source/material',
		method: 'get',
		params: data,
	});
}

export function downloadFile(url: string) {
	return request({
		url,
		method: 'get',
		responseType: 'blob',
	});
}

/**
 * 以下接口，测试时访问`http://**************/`（黑龙江网络），已在vite.config中配置
 * 开发环境前缀为 `/my-test`，生产环境前缀为 `/nvr`
 */
// 防火素材（NVR设备）回放列表
export function getFireMaterials(params: Object) {
	return request({
		url: '/nvr/playback/list',
		method: 'get',
    params,
	});
}

// 防火素材播放地址`{项目ip}/nvr/playback/play.flv?fileName={文件名}`
export function generatPlayUrl(fileName: string) {
  return (import.meta.env.MODE === 'development' ? `http://**************/my-test` : `${window.location.origin}`) + `/nvr/playback/play.flv?fileName=${fileName}`
}

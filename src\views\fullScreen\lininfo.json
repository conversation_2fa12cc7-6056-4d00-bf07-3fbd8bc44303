{"data": {"picHeightAerial": 0, "picWidth": 604, "picHeight": 399, "linePicUrl": "/robotservice/minioservice/robotv2/brush/20240430/575_43dddfef5408430e96b9b2509554dd1c.png", "pointActions": [{"pointCode": "301", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 248, "angle": 0, "y": 213, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "302", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 263, "angle": 0, "y": 182, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "303", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 279, "angle": 0, "y": 184, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "304", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 296, "angle": 0, "y": 186, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "305", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 309, "angle": 0, "y": 188, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "306", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 328, "angle": 0, "y": 191, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "307", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 347, "angle": 0, "y": 195, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "308", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 364, "angle": 0, "y": 199, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "309", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 388, "angle": 0, "y": 204, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "310", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 406, "angle": 0, "y": 208, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "311", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 424, "angle": 0, "y": 211, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "312", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 441, "angle": 0, "y": 215, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "313", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 467, "angle": 0, "y": 221, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "314", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 478, "angle": 0, "y": 230, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "315", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 471, "angle": 0, "y": 252, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "316", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 466, "angle": 0, "y": 271, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "317", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 463, "angle": 0, "y": 288, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "318", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 460, "angle": 0, "y": 300, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "319", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 451, "angle": 0, "y": 300, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "320", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 434, "angle": 0, "y": 297, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "321", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 413, "angle": 0, "y": 293, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "322", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 393, "angle": 0, "y": 289, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "323", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 374, "angle": 0, "y": 285, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "324", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 344, "angle": 0, "y": 278, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "325", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 315, "angle": 0, "y": 273, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "326", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 297, "angle": 0, "y": 270, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "327", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 276, "angle": 0, "y": 263, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "328", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 260, "angle": 0, "y": 256, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "329", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 246, "angle": 0, "y": 248, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "330", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 241, "angle": 0, "y": 241, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "331", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 243, "angle": 0, "y": 231, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}, {"pointCode": "332", "isReverse": 0, "originPointId": 0, "center_y_a": 0, "center_x_a": 0, "x": 248, "angle": 0, "y": 213, "y_a": 0, "x_a": 0, "radius": 0.0, "actions": [], "center_y": 0, "center_x": 0}], "robotConfigs": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n\n<Root>\n <Header>\n  <CmdType>NB_RobotPatrol_Laser_Set</CmdType>\n  <To>575</To>\n  <From>SYAPP</From>\n  <CmdAttribute>Post</CmdAttribute>\n </Header>\n <PatrolList>\n  <PatrolCount>1</PatrolCount>\n  <PatrolInfo>\n   <PatrolID>3</PatrolID>\n   <Type>1</Type>\n   <PointNum>32</PointNum>\n   <MapName>xl27</MapName>\n   <GraphName>sss</GraphName>\n   <Point>\n    <PointID>301</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>302</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>303</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>304</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>305</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>306</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>307</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>308</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>309</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>310</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>311</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>312</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>313</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>314</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>315</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>316</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>317</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>318</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>319</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>320</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>321</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>322</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>323</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>324</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>325</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>326</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>327</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>328</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>329</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>330</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>331</PointID>\n    <ActionList/>\n   </Point>\n   <Point>\n    <PointID>332</PointID>\n    <ActionList/>\n   </Point>\n  </PatrolInfo>\n  <PathInfo>\n   <MapName>xl27</MapName>\n   <Graph_Name>sss</Graph_Name>\n   <PointList>\n    <Point>\n     <Name>301</Name>\n     <Action/>\n     <Position>\n      <X>248.0</X>\n      <Y>213.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>302</Name>\n     <Action/>\n     <Position>\n      <X>263.0</X>\n      <Y>182.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>303</Name>\n     <Action/>\n     <Position>\n      <X>279.0</X>\n      <Y>184.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>304</Name>\n     <Action/>\n     <Position>\n      <X>296.0</X>\n      <Y>186.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>305</Name>\n     <Action/>\n     <Position>\n      <X>309.0</X>\n      <Y>188.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>306</Name>\n     <Action/>\n     <Position>\n      <X>328.0</X>\n      <Y>191.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>307</Name>\n     <Action/>\n     <Position>\n      <X>347.0</X>\n      <Y>195.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>308</Name>\n     <Action/>\n     <Position>\n      <X>364.0</X>\n      <Y>199.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>309</Name>\n     <Action/>\n     <Position>\n      <X>388.0</X>\n      <Y>204.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>310</Name>\n     <Action/>\n     <Position>\n      <X>406.0</X>\n      <Y>208.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>311</Name>\n     <Action/>\n     <Position>\n      <X>424.0</X>\n      <Y>211.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>312</Name>\n     <Action/>\n     <Position>\n      <X>441.0</X>\n      <Y>215.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>313</Name>\n     <Action/>\n     <Position>\n      <X>467.0</X>\n      <Y>221.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>314</Name>\n     <Action/>\n     <Position>\n      <X>478.0</X>\n      <Y>230.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>315</Name>\n     <Action/>\n     <Position>\n      <X>471.0</X>\n      <Y>252.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>316</Name>\n     <Action/>\n     <Position>\n      <X>466.0</X>\n      <Y>271.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>317</Name>\n     <Action/>\n     <Position>\n      <X>463.0</X>\n      <Y>288.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>318</Name>\n     <Action/>\n     <Position>\n      <X>460.0</X>\n      <Y>300.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>319</Name>\n     <Action/>\n     <Position>\n      <X>451.0</X>\n      <Y>300.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>320</Name>\n     <Action/>\n     <Position>\n      <X>434.0</X>\n      <Y>297.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>321</Name>\n     <Action/>\n     <Position>\n      <X>413.0</X>\n      <Y>293.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>322</Name>\n     <Action/>\n     <Position>\n      <X>393.0</X>\n      <Y>289.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>323</Name>\n     <Action/>\n     <Position>\n      <X>374.0</X>\n      <Y>285.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>324</Name>\n     <Action/>\n     <Position>\n      <X>344.0</X>\n      <Y>278.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>325</Name>\n     <Action/>\n     <Position>\n      <X>315.0</X>\n      <Y>273.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>326</Name>\n     <Action/>\n     <Position>\n      <X>297.0</X>\n      <Y>270.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>327</Name>\n     <Action/>\n     <Position>\n      <X>276.0</X>\n      <Y>263.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>328</Name>\n     <Action/>\n     <Position>\n      <X>260.0</X>\n      <Y>256.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>329</Name>\n     <Action/>\n     <Position>\n      <X>246.0</X>\n      <Y>248.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>330</Name>\n     <Action/>\n     <Position>\n      <X>241.0</X>\n      <Y>241.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>331</Name>\n     <Action/>\n     <Position>\n      <X>243.0</X>\n      <Y>231.0</Y>\n     </Position>\n    </Point>\n    <Point>\n     <Name>332</Name>\n     <Action/>\n     <Position>\n      <X>248.0</X>\n      <Y>213.0</Y>\n     </Position>\n    </Point>\n   </PointList>\n   <LineList>\n    <Line>\n     <Name>302</Name>\n     <Begin>301</Begin>\n     <End>302</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>303</Name>\n     <Begin>302</Begin>\n     <End>303</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>304</Name>\n     <Begin>303</Begin>\n     <End>304</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>305</Name>\n     <Begin>304</Begin>\n     <End>305</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>306</Name>\n     <Begin>305</Begin>\n     <End>306</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>307</Name>\n     <Begin>306</Begin>\n     <End>307</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>308</Name>\n     <Begin>307</Begin>\n     <End>308</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>309</Name>\n     <Begin>308</Begin>\n     <End>309</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>310</Name>\n     <Begin>309</Begin>\n     <End>310</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>311</Name>\n     <Begin>310</Begin>\n     <End>311</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>312</Name>\n     <Begin>311</Begin>\n     <End>312</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>313</Name>\n     <Begin>312</Begin>\n     <End>313</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>314</Name>\n     <Begin>313</Begin>\n     <End>314</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>315</Name>\n     <Begin>314</Begin>\n     <End>315</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>316</Name>\n     <Begin>315</Begin>\n     <End>316</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>317</Name>\n     <Begin>316</Begin>\n     <End>317</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>318</Name>\n     <Begin>317</Begin>\n     <End>318</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>319</Name>\n     <Begin>318</Begin>\n     <End>319</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>320</Name>\n     <Begin>319</Begin>\n     <End>320</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>321</Name>\n     <Begin>320</Begin>\n     <End>321</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>322</Name>\n     <Begin>321</Begin>\n     <End>322</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>323</Name>\n     <Begin>322</Begin>\n     <End>323</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>324</Name>\n     <Begin>323</Begin>\n     <End>324</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>325</Name>\n     <Begin>324</Begin>\n     <End>325</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>326</Name>\n     <Begin>325</Begin>\n     <End>326</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>327</Name>\n     <Begin>326</Begin>\n     <End>327</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>328</Name>\n     <Begin>327</Begin>\n     <End>328</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>329</Name>\n     <Begin>328</Begin>\n     <End>329</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>330</Name>\n     <Begin>329</Begin>\n     <End>330</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>331</Name>\n     <Begin>330</Begin>\n     <End>331</End>\n     <Radius>0.0</Radius>\n    </Line>\n    <Line>\n     <Name>332</Name>\n     <Begin>331</Begin>\n     <End>332</End>\n     <Radius>0.0</Radius>\n    </Line>\n   </LineList>\n   <PathList>\n    <Path>\n     <Name>302</Name>\n     <Lines>\n      <Line>302</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>301</Name>\n       <Action></Action>\n       <Position>\n        <X>248.0</X>\n        <Y>213.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>302</Name>\n       <Action></Action>\n       <Position>\n        <X>263.0</X>\n        <Y>182.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>303</Name>\n     <Lines>\n      <Line>303</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>302</Name>\n       <Action></Action>\n       <Position>\n        <X>263.0</X>\n        <Y>182.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>303</Name>\n       <Action></Action>\n       <Position>\n        <X>279.0</X>\n        <Y>184.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>304</Name>\n     <Lines>\n      <Line>304</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>303</Name>\n       <Action></Action>\n       <Position>\n        <X>279.0</X>\n        <Y>184.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>304</Name>\n       <Action></Action>\n       <Position>\n        <X>296.0</X>\n        <Y>186.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>305</Name>\n     <Lines>\n      <Line>305</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>304</Name>\n       <Action></Action>\n       <Position>\n        <X>296.0</X>\n        <Y>186.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>305</Name>\n       <Action></Action>\n       <Position>\n        <X>309.0</X>\n        <Y>188.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>306</Name>\n     <Lines>\n      <Line>306</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>305</Name>\n       <Action></Action>\n       <Position>\n        <X>309.0</X>\n        <Y>188.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>306</Name>\n       <Action></Action>\n       <Position>\n        <X>328.0</X>\n        <Y>191.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>307</Name>\n     <Lines>\n      <Line>307</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>306</Name>\n       <Action></Action>\n       <Position>\n        <X>328.0</X>\n        <Y>191.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>307</Name>\n       <Action></Action>\n       <Position>\n        <X>347.0</X>\n        <Y>195.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>308</Name>\n     <Lines>\n      <Line>308</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>307</Name>\n       <Action></Action>\n       <Position>\n        <X>347.0</X>\n        <Y>195.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>308</Name>\n       <Action></Action>\n       <Position>\n        <X>364.0</X>\n        <Y>199.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>309</Name>\n     <Lines>\n      <Line>309</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>308</Name>\n       <Action></Action>\n       <Position>\n        <X>364.0</X>\n        <Y>199.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>309</Name>\n       <Action></Action>\n       <Position>\n        <X>388.0</X>\n        <Y>204.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>310</Name>\n     <Lines>\n      <Line>310</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>309</Name>\n       <Action></Action>\n       <Position>\n        <X>388.0</X>\n        <Y>204.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>310</Name>\n       <Action></Action>\n       <Position>\n        <X>406.0</X>\n        <Y>208.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>311</Name>\n     <Lines>\n      <Line>311</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>310</Name>\n       <Action></Action>\n       <Position>\n        <X>406.0</X>\n        <Y>208.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>311</Name>\n       <Action></Action>\n       <Position>\n        <X>424.0</X>\n        <Y>211.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>312</Name>\n     <Lines>\n      <Line>312</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>311</Name>\n       <Action></Action>\n       <Position>\n        <X>424.0</X>\n        <Y>211.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>312</Name>\n       <Action></Action>\n       <Position>\n        <X>441.0</X>\n        <Y>215.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>313</Name>\n     <Lines>\n      <Line>313</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>312</Name>\n       <Action></Action>\n       <Position>\n        <X>441.0</X>\n        <Y>215.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>313</Name>\n       <Action></Action>\n       <Position>\n        <X>467.0</X>\n        <Y>221.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>314</Name>\n     <Lines>\n      <Line>314</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>313</Name>\n       <Action></Action>\n       <Position>\n        <X>467.0</X>\n        <Y>221.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>314</Name>\n       <Action></Action>\n       <Position>\n        <X>478.0</X>\n        <Y>230.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>315</Name>\n     <Lines>\n      <Line>315</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>314</Name>\n       <Action></Action>\n       <Position>\n        <X>478.0</X>\n        <Y>230.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>315</Name>\n       <Action></Action>\n       <Position>\n        <X>471.0</X>\n        <Y>252.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>316</Name>\n     <Lines>\n      <Line>316</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>315</Name>\n       <Action></Action>\n       <Position>\n        <X>471.0</X>\n        <Y>252.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>316</Name>\n       <Action></Action>\n       <Position>\n        <X>466.0</X>\n        <Y>271.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>317</Name>\n     <Lines>\n      <Line>317</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>316</Name>\n       <Action></Action>\n       <Position>\n        <X>466.0</X>\n        <Y>271.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>317</Name>\n       <Action></Action>\n       <Position>\n        <X>463.0</X>\n        <Y>288.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>318</Name>\n     <Lines>\n      <Line>318</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>317</Name>\n       <Action></Action>\n       <Position>\n        <X>463.0</X>\n        <Y>288.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>318</Name>\n       <Action></Action>\n       <Position>\n        <X>460.0</X>\n        <Y>300.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>319</Name>\n     <Lines>\n      <Line>319</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>318</Name>\n       <Action></Action>\n       <Position>\n        <X>460.0</X>\n        <Y>300.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>319</Name>\n       <Action></Action>\n       <Position>\n        <X>451.0</X>\n        <Y>300.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>320</Name>\n     <Lines>\n      <Line>320</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>319</Name>\n       <Action></Action>\n       <Position>\n        <X>451.0</X>\n        <Y>300.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>320</Name>\n       <Action></Action>\n       <Position>\n        <X>434.0</X>\n        <Y>297.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>321</Name>\n     <Lines>\n      <Line>321</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>320</Name>\n       <Action></Action>\n       <Position>\n        <X>434.0</X>\n        <Y>297.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>321</Name>\n       <Action></Action>\n       <Position>\n        <X>413.0</X>\n        <Y>293.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>322</Name>\n     <Lines>\n      <Line>322</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>321</Name>\n       <Action></Action>\n       <Position>\n        <X>413.0</X>\n        <Y>293.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>322</Name>\n       <Action></Action>\n       <Position>\n        <X>393.0</X>\n        <Y>289.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>323</Name>\n     <Lines>\n      <Line>323</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>322</Name>\n       <Action></Action>\n       <Position>\n        <X>393.0</X>\n        <Y>289.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>323</Name>\n       <Action></Action>\n       <Position>\n        <X>374.0</X>\n        <Y>285.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>324</Name>\n     <Lines>\n      <Line>324</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>323</Name>\n       <Action></Action>\n       <Position>\n        <X>374.0</X>\n        <Y>285.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>324</Name>\n       <Action></Action>\n       <Position>\n        <X>344.0</X>\n        <Y>278.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>325</Name>\n     <Lines>\n      <Line>325</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>324</Name>\n       <Action></Action>\n       <Position>\n        <X>344.0</X>\n        <Y>278.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>325</Name>\n       <Action></Action>\n       <Position>\n        <X>315.0</X>\n        <Y>273.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>326</Name>\n     <Lines>\n      <Line>326</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>325</Name>\n       <Action></Action>\n       <Position>\n        <X>315.0</X>\n        <Y>273.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>326</Name>\n       <Action></Action>\n       <Position>\n        <X>297.0</X>\n        <Y>270.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>327</Name>\n     <Lines>\n      <Line>327</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>326</Name>\n       <Action></Action>\n       <Position>\n        <X>297.0</X>\n        <Y>270.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>327</Name>\n       <Action></Action>\n       <Position>\n        <X>276.0</X>\n        <Y>263.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>328</Name>\n     <Lines>\n      <Line>328</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>327</Name>\n       <Action></Action>\n       <Position>\n        <X>276.0</X>\n        <Y>263.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>328</Name>\n       <Action></Action>\n       <Position>\n        <X>260.0</X>\n        <Y>256.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>329</Name>\n     <Lines>\n      <Line>329</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>328</Name>\n       <Action></Action>\n       <Position>\n        <X>260.0</X>\n        <Y>256.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>329</Name>\n       <Action></Action>\n       <Position>\n        <X>246.0</X>\n        <Y>248.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>330</Name>\n     <Lines>\n      <Line>330</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>329</Name>\n       <Action></Action>\n       <Position>\n        <X>246.0</X>\n        <Y>248.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>330</Name>\n       <Action></Action>\n       <Position>\n        <X>241.0</X>\n        <Y>241.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>331</Name>\n     <Lines>\n      <Line>331</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>330</Name>\n       <Action></Action>\n       <Position>\n        <X>241.0</X>\n        <Y>241.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>331</Name>\n       <Action></Action>\n       <Position>\n        <X>243.0</X>\n        <Y>231.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n    <Path>\n     <Name>332</Name>\n     <Lines>\n      <Line>332</Line>\n      <Reverse>0</Reverse>\n     </Lines>\n     <Points>\n      <Point>\n       <Name>331</Name>\n       <Action></Action>\n       <Position>\n        <X>243.0</X>\n        <Y>231.0</Y>\n       </Position>\n      </Point>\n      <Point>\n       <Name>332</Name>\n       <Action></Action>\n       <Position>\n        <X>248.0</X>\n        <Y>213.0</Y>\n       </Position>\n      </Point>\n     </Points>\n    </Path>\n   </PathList>\n   <PathGroup>\n    <Group>\n     <Name>sss</Name>\n     <Paths>\n      <PathName>302</PathName>\n      <PathName>303</PathName>\n      <PathName>304</PathName>\n      <PathName>305</PathName>\n      <PathName>306</PathName>\n      <PathName>307</PathName>\n      <PathName>308</PathName>\n      <PathName>309</PathName>\n      <PathName>310</PathName>\n      <PathName>311</PathName>\n      <PathName>312</PathName>\n      <PathName>313</PathName>\n      <PathName>314</PathName>\n      <PathName>315</PathName>\n      <PathName>316</PathName>\n      <PathName>317</PathName>\n      <PathName>318</PathName>\n      <PathName>319</PathName>\n      <PathName>320</PathName>\n      <PathName>321</PathName>\n      <PathName>322</PathName>\n      <PathName>323</PathName>\n      <PathName>324</PathName>\n      <PathName>325</PathName>\n      <PathName>326</PathName>\n      <PathName>327</PathName>\n      <PathName>328</PathName>\n      <PathName>329</PathName>\n      <PathName>330</PathName>\n      <PathName>331</PathName>\n      <PathName>332</PathName>\n     </Paths>\n    </Group>\n   </PathGroup>\n  </PathInfo>\n </PatrolList>\n</Root>\n", "lineName": "sss", "deviceId": 575, "linePicUrl2": "", "patrolLinePointList": [{"centerY": 0, "pointCode": "301", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 10000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 248, "angle": 0, "y": 213, "id": 116143, "radius": 0.0}, {"centerY": 0, "pointCode": "302", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 20000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 263, "angle": 0, "y": 182, "id": 116144, "radius": 0.0}, {"centerY": 0, "pointCode": "303", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 30000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 279, "angle": 0, "y": 184, "id": 116145, "radius": 0.0}, {"centerY": 0, "pointCode": "304", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 40000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 296, "angle": 0, "y": 186, "id": 116146, "radius": 0.0}, {"centerY": 0, "pointCode": "305", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 50000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 309, "angle": 0, "y": 188, "id": 116147, "radius": 0.0}, {"centerY": 0, "pointCode": "306", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 60000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 328, "angle": 0, "y": 191, "id": 116148, "radius": 0.0}, {"centerY": 0, "pointCode": "307", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 70000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 347, "angle": 0, "y": 195, "id": 116149, "radius": 0.0}, {"centerY": 0, "pointCode": "308", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 80000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 364, "angle": 0, "y": 199, "id": 116150, "radius": 0.0}, {"centerY": 0, "pointCode": "309", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 90000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 388, "angle": 0, "y": 204, "id": 116151, "radius": 0.0}, {"centerY": 0, "pointCode": "310", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 100000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 406, "angle": 0, "y": 208, "id": 116152, "radius": 0.0}, {"centerY": 0, "pointCode": "311", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 110000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 424, "angle": 0, "y": 211, "id": 116153, "radius": 0.0}, {"centerY": 0, "pointCode": "312", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 120000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 441, "angle": 0, "y": 215, "id": 116154, "radius": 0.0}, {"centerY": 0, "pointCode": "313", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 130000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 467, "angle": 0, "y": 221, "id": 116155, "radius": 0.0}, {"centerY": 0, "pointCode": "314", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 140000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 478, "angle": 0, "y": 230, "id": 116156, "radius": 0.0}, {"centerY": 0, "pointCode": "315", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 150000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 471, "angle": 0, "y": 252, "id": 116157, "radius": 0.0}, {"centerY": 0, "pointCode": "316", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 160000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 466, "angle": 0, "y": 271, "id": 116158, "radius": 0.0}, {"centerY": 0, "pointCode": "317", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 170000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 463, "angle": 0, "y": 288, "id": 116159, "radius": 0.0}, {"centerY": 0, "pointCode": "318", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 180000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 460, "angle": 0, "y": 300, "id": 116160, "radius": 0.0}, {"centerY": 0, "pointCode": "319", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 190000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 451, "angle": 0, "y": 300, "id": 116161, "radius": 0.0}, {"centerY": 0, "pointCode": "320", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 200000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 434, "angle": 0, "y": 297, "id": 116162, "radius": 0.0}, {"centerY": 0, "pointCode": "321", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 210000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 413, "angle": 0, "y": 293, "id": 116163, "radius": 0.0}, {"centerY": 0, "pointCode": "322", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 220000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 393, "angle": 0, "y": 289, "id": 116164, "radius": 0.0}, {"centerY": 0, "pointCode": "323", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 230000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 374, "angle": 0, "y": 285, "id": 116165, "radius": 0.0}, {"centerY": 0, "pointCode": "324", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 240000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 344, "angle": 0, "y": 278, "id": 116166, "radius": 0.0}, {"centerY": 0, "pointCode": "325", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 250000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 315, "angle": 0, "y": 273, "id": 116167, "radius": 0.0}, {"centerY": 0, "pointCode": "326", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 260000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 297, "angle": 0, "y": 270, "id": 116168, "radius": 0.0}, {"centerY": 0, "pointCode": "327", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 270000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 276, "angle": 0, "y": 263, "id": 116169, "radius": 0.0}, {"centerY": 0, "pointCode": "328", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 280000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 260, "angle": 0, "y": 256, "id": 116170, "radius": 0.0}, {"centerY": 0, "pointCode": "329", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 290000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 246, "angle": 0, "y": 248, "id": 116171, "radius": 0.0}, {"centerY": 0, "pointCode": "330", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 300000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 241, "angle": 0, "y": 241, "id": 116172, "radius": 0.0}, {"centerY": 0, "pointCode": "331", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 310000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 243, "angle": 0, "y": 231, "id": 116173, "radius": 0.0}, {"centerY": 0, "pointCode": "332", "centerX": 0, "isReverse": 0, "originPointId": 0, "sort": 320000, "type": 1, "isActionPoint": 0, "patrolLineId": 1231, "x": 248, "angle": 0, "y": 213, "id": 116174, "radius": 0.0}], "linePicUrl3": "", "picWidthAerial": 0, "lineCode": "3", "lineSpeed": 1, "lineType": 1, "clientConfigs": {"PointList": [{"X": 248.0, "IsReverse": 0, "Y": 213.0, "id": "301", "radius": 0.0}, {"X": 263.0, "IsReverse": 0, "Y": 182.0, "id": "302", "radius": 0.0}, {"X": 279.0, "IsReverse": 0, "Y": 184.0, "id": "303", "radius": 0.0}, {"X": 296.0, "IsReverse": 0, "Y": 186.0, "id": "304", "radius": 0.0}, {"X": 309.0, "IsReverse": 0, "Y": 188.0, "id": "305", "radius": 0.0}, {"X": 328.0, "IsReverse": 0, "Y": 191.0, "id": "306", "radius": 0.0}, {"X": 347.0, "IsReverse": 0, "Y": 195.0, "id": "307", "radius": 0.0}, {"X": 364.0, "IsReverse": 0, "Y": 199.0, "id": "308", "radius": 0.0}, {"X": 388.0, "IsReverse": 0, "Y": 204.0, "id": "309", "radius": 0.0}, {"X": 406.0, "IsReverse": 0, "Y": 208.0, "id": "310", "radius": 0.0}, {"X": 424.0, "IsReverse": 0, "Y": 211.0, "id": "311", "radius": 0.0}, {"X": 441.0, "IsReverse": 0, "Y": 215.0, "id": "312", "radius": 0.0}, {"X": 467.0, "IsReverse": 0, "Y": 221.0, "id": "313", "radius": 0.0}, {"X": 478.0, "IsReverse": 0, "Y": 230.0, "id": "314", "radius": 0.0}, {"X": 471.0, "IsReverse": 0, "Y": 252.0, "id": "315", "radius": 0.0}, {"X": 466.0, "IsReverse": 0, "Y": 271.0, "id": "316", "radius": 0.0}, {"X": 463.0, "IsReverse": 0, "Y": 288.0, "id": "317", "radius": 0.0}, {"X": 460.0, "IsReverse": 0, "Y": 300.0, "id": "318", "radius": 0.0}, {"X": 451.0, "IsReverse": 0, "Y": 300.0, "id": "319", "radius": 0.0}, {"X": 434.0, "IsReverse": 0, "Y": 297.0, "id": "320", "radius": 0.0}, {"X": 413.0, "IsReverse": 0, "Y": 293.0, "id": "321", "radius": 0.0}, {"X": 393.0, "IsReverse": 0, "Y": 289.0, "id": "322", "radius": 0.0}, {"X": 374.0, "IsReverse": 0, "Y": 285.0, "id": "323", "radius": 0.0}, {"X": 344.0, "IsReverse": 0, "Y": 278.0, "id": "324", "radius": 0.0}, {"X": 315.0, "IsReverse": 0, "Y": 273.0, "id": "325", "radius": 0.0}, {"X": 297.0, "IsReverse": 0, "Y": 270.0, "id": "326", "radius": 0.0}, {"X": 276.0, "IsReverse": 0, "Y": 263.0, "id": "327", "radius": 0.0}, {"X": 260.0, "IsReverse": 0, "Y": 256.0, "id": "328", "radius": 0.0}, {"X": 246.0, "IsReverse": 0, "Y": 248.0, "id": "329", "radius": 0.0}, {"X": 241.0, "IsReverse": 0, "Y": 241.0, "id": "330", "radius": 0.0}, {"X": 243.0, "IsReverse": 0, "Y": 231.0, "id": "331", "radius": 0.0}, {"X": 248.0, "IsReverse": 0, "Y": 213.0, "id": "332", "radius": 0.0}]}, "id": 1231, "mapName": "xl27", "map": {"mapPicUrl2": "", "picWidth": 604, "pic3Height": 0, "mapPicUrl3": "", "picHeight": 399, "mapPicUrl": "/robotservice/minioservice/robotv2/brush/20240430/575_43dddfef5408430e96b9b2509554dd1c.png", "mapType": "", "pic3Width": 0, "id": 1418, "mapName": "xl27", "deviceId": 575, "localConfigs": ""}, "wall": {"long_corridor": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "disable_camera_barrier": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "obstacles": {"polylines": [[{"x": 269, "y": 248}, {"x": 280, "y": 196}, {"x": 295, "y": 198}, {"x": 293, "y": 203}], [{"x": 269, "y": 249}, {"x": 283, "y": 252}], [{"x": 233, "y": 167}, {"x": 254, "y": 171}, {"x": 266, "y": 173}, {"x": 273, "y": 173}, {"x": 278, "y": 174}, {"x": 285, "y": 175}, {"x": 292, "y": 176}, {"x": 311, "y": 180}, {"x": 331, "y": 185}, {"x": 346, "y": 188}, {"x": 364, "y": 192}, {"x": 367, "y": 182}, {"x": 395, "y": 187}, {"x": 397, "y": 193}, {"x": 399, "y": 200}, {"x": 410, "y": 202}, {"x": 424, "y": 205}, {"x": 433, "y": 206}, {"x": 435, "y": 200}, {"x": 441, "y": 202}, {"x": 465, "y": 207}, {"x": 467, "y": 208}, {"x": 468, "y": 213}, {"x": 468, "y": 215}, {"x": 474, "y": 215}, {"x": 484, "y": 217}, {"x": 492, "y": 218}, {"x": 496, "y": 220}, {"x": 499, "y": 230}, {"x": 493, "y": 237}, {"x": 491, "y": 244}, {"x": 488, "y": 257}, {"x": 483, "y": 278}, {"x": 480, "y": 294}, {"x": 487, "y": 298}, {"x": 476, "y": 312}, {"x": 471, "y": 314}, {"x": 468, "y": 310}, {"x": 458, "y": 308}, {"x": 445, "y": 305}, {"x": 428, "y": 302}, {"x": 409, "y": 298}, {"x": 395, "y": 295}, {"x": 380, "y": 292}, {"x": 376, "y": 296}, {"x": 368, "y": 300}, {"x": 354, "y": 297}, {"x": 341, "y": 296}, {"x": 343, "y": 292}, {"x": 344, "y": 285}, {"x": 333, "y": 283}, {"x": 322, "y": 280}, {"x": 319, "y": 283}, {"x": 318, "y": 290}, {"x": 305, "y": 288}, {"x": 288, "y": 285}, {"x": 288, "y": 279}, {"x": 281, "y": 277}, {"x": 275, "y": 278}, {"x": 269, "y": 279}, {"x": 266, "y": 279}, {"x": 265, "y": 276}, {"x": 266, "y": 273}, {"x": 266, "y": 269}, {"x": 264, "y": 268}, {"x": 261, "y": 267}, {"x": 250, "y": 265}, {"x": 240, "y": 261}, {"x": 222, "y": 257}, {"x": 215, "y": 255}, {"x": 214, "y": 254}, {"x": 215, "y": 247}, {"x": 217, "y": 236}, {"x": 221, "y": 218}, {"x": 225, "y": 196}, {"x": 227, "y": 187}, {"x": 225, "y": 177}, {"x": 227, "y": 169}, {"x": 233, "y": 167}]], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "narrow_gate": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "slopes": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "displays": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "disable_camera": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "induction_gate": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "decelerations": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "highlight": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "box_area": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "disable_path_plan": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "elevators": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}, "mapName": "", "one_way_street": {"polylines": [], "rectangles": [], "polygons": [], "circles": [], "lines": []}}}, "type": "LineInfo"}
<template>
	<div class="checkbox-wrapper-12">
		<div class="cbx">
			<input
				:id="checkboxId"
				type="checkbox"
				:checked="modelValue"
				@change="$emit('update:modelValue', ($event.target as HTMLInputElement).checked)"
			/>
			<label :for="checkboxId"></label>
			<svg width="15" height="14" viewBox="0 0 15 14" fill="none">
				<path d="M2 8.36364L6.23077 12L13 2"></path>
			</svg>
		</div>

		<svg xmlns="http://www.w3.org/2000/svg" version="1.1">
			<defs>
				<filter id="goo-12">
					<feGaussianBlur in="SourceGraphic" stdDeviation="4" result="blur"></feGaussianBlur>
					<feColorMatrix
						in="blur"
						mode="matrix"
						values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 22 -7"
						result="goo-12"
					></feColorMatrix>
					<feBlend in="SourceGraphic" in2="goo-12"></feBlend>
				</filter>
			</defs>
		</svg>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

defineProps<{
	modelValue: boolean;
}>();

// 生成唯一ID
const checkboxId = computed(() => `cbx-${Math.random().toString(36).substr(2, 9)}`);
</script>

<style scoped lang="scss">
// 尺寸变量
$checkbox-size: 18px;
$border-width: 2px;
$icon-size: 12px;

// 颜色变量
$border-color: #bfbfc0;
$check-color: #fff;
$active-color: $sd-screen-success;
$stroke-width: 3;

.checkbox-wrapper-12 {
	position: relative;
}

.checkbox-wrapper-12 > svg {
	position: absolute;
	top: -130%;
	left: -170%;
	width: 110px;
	pointer-events: none;
}

.checkbox-wrapper-12 * {
	box-sizing: border-box;
}

.checkbox-wrapper-12 input[type='checkbox'] {
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	-webkit-tap-highlight-color: transparent;
	cursor: pointer;
	margin: 0;
}

.checkbox-wrapper-12 input[type='checkbox']:focus {
	outline: 0;
}

.checkbox-wrapper-12 .cbx {
	width: $checkbox-size;
	height: $checkbox-size;
	top: calc(100px - #{$checkbox-size/2});
	left: calc(100px - #{$checkbox-size/2});
}

.checkbox-wrapper-12 .cbx input {
	position: absolute;
	top: 0;
	left: 0;
	width: $checkbox-size;
	height: $checkbox-size;
	border: $border-width solid $border-color;
	border-radius: 50%;
}

.checkbox-wrapper-12 .cbx label {
	width: $checkbox-size;
	height: $checkbox-size;
	background: none;
	border-radius: 50%;
	position: absolute;
	top: 0;
	left: 0;
	transform: trasnlate3d(0, 0, 0);
	pointer-events: none;
}

.checkbox-wrapper-12 .cbx svg {
	position: absolute;
	width: $icon-size;
	height: $icon-size;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 1;
	pointer-events: none;
}

.checkbox-wrapper-12 .cbx svg path {
	stroke: $check-color;
	stroke-width: $stroke-width;
	stroke-linecap: round;
	stroke-linejoin: round;
	stroke-dasharray: 19;
	stroke-dashoffset: 19;
	transition: stroke-dashoffset 0.3s ease;
	transition-delay: 0.2s;
}

.checkbox-wrapper-12 .cbx input:checked + label {
	animation: splash-12 0.6s ease forwards;
}

.checkbox-wrapper-12 .cbx input:checked + label + svg path {
	stroke-dashoffset: 0;
}

@-moz-keyframes splash-12 {
	40% {
		background: $active-color;
		box-shadow: 0 -18px 0 -8px $active-color, 16px -8px 0 -8px $active-color,
			16px 8px 0 -8px $active-color, 0 18px 0 -8px $active-color, -16px 8px 0 -8px $active-color,
			-16px -8px 0 -8px $active-color;
	}

	100% {
		background: $active-color;
		box-shadow: 0 -36px 0 -10px transparent, 32px -16px 0 -10px transparent,
			32px 16px 0 -10px transparent, 0 36px 0 -10px transparent, -32px 16px 0 -10px transparent,
			-32px -16px 0 -10px transparent;
	}
}

@-webkit-keyframes splash-12 {
	40% {
		background: $active-color;
		box-shadow: 0 -18px 0 -8px $active-color, 16px -8px 0 -8px $active-color,
			16px 8px 0 -8px $active-color, 0 18px 0 -8px $active-color, -16px 8px 0 -8px $active-color,
			-16px -8px 0 -8px $active-color;
	}

	100% {
		background: $active-color;
		box-shadow: 0 -36px 0 -10px transparent, 32px -16px 0 -10px transparent,
			32px 16px 0 -10px transparent, 0 36px 0 -10px transparent, -32px 16px 0 -10px transparent,
			-32px -16px 0 -10px transparent;
	}
}

@-o-keyframes splash-12 {
	40% {
		background: $active-color;
		box-shadow: 0 -18px 0 -8px $active-color, 16px -8px 0 -8px $active-color,
			16px 8px 0 -8px $active-color, 0 18px 0 -8px $active-color, -16px 8px 0 -8px $active-color,
			-16px -8px 0 -8px $active-color;
	}

	100% {
		background: $active-color;
		box-shadow: 0 -36px 0 -10px transparent, 32px -16px 0 -10px transparent,
			32px 16px 0 -10px transparent, 0 36px 0 -10px transparent, -32px 16px 0 -10px transparent,
			-32px -16px 0 -10px transparent;
	}
}

@keyframes splash-12 {
	40% {
		background: $active-color;
		box-shadow: 0 -18px 0 -8px $active-color, 16px -8px 0 -8px $active-color,
			16px 8px 0 -8px $active-color, 0 18px 0 -8px $active-color, -16px 8px 0 -8px $active-color,
			-16px -8px 0 -8px $active-color;
	}

	100% {
		background: $active-color;
		box-shadow: 0 -36px 0 -10px transparent, 32px -16px 0 -10px transparent,
			32px 16px 0 -10px transparent, 0 36px 0 -10px transparent, -32px 16px 0 -10px transparent,
			-32px -16px 0 -10px transparent;
	}
}
</style>

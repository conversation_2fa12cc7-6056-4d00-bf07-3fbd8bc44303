<template>
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		width="680px"
		align-center
	>
		<el-form
			ref="dialogFormRef"
			:model="state.ruleForm"
			label-width="120px"
			size="large"
			label-position="right"
		>
			<el-form-item
				label="监测任务名称"
				prop="name"
				:rules="[{ required: true, message: '监测任务名称不能为空', trigger: 'blur' }]"
			>
				<el-input
					v-model="state.ruleForm.name"
					placeholder="请输入（例：65设备鸟类监听）"
					clearable
				></el-input>
			</el-form-item>

			<el-form-item
				label="关联设备类型"
				prop="sourceType"
				:rules="[{ required: true, message: '关联设备类型不能为空', trigger: 'blur' }]"
			>
				<el-radio-group
					v-model="state.ruleForm.sourceType"
					class="ml-4"
					@change="onSourceTypeChange"
				>
					<!-- <el-radio :label="1">萤石云设备</el-radio> -->
					<el-radio :label="2">红外设备</el-radio>
					<el-radio :label="3">监控设备</el-radio>
					<!-- <el-radio :label="4">巡护设备</el-radio> -->
				</el-radio-group>
			</el-form-item>

			<!-- <el-form-item
				v-if="state.ruleForm.sourceType === 3"
				label="监测数据"
				prop="processType"
				:rules="[{ required: true, message: '监测对象不能为空', trigger: 'blur' }]"
			>
				<el-radio-group v-model="state.ruleForm.processType">
					<el-radio :label="0">监控视频</el-radio>
					<el-radio :label="1">巡航任务</el-radio>
				</el-radio-group>
			</el-form-item> -->

			<el-form-item
				label="关联设备"
				prop="deviceIds"
				:rules="[{ required: true, message: '关联设备不能为空', trigger: 'blur' }]"
			>
				<!-- :multiple="state.deviceIsMultiple" -->
				<el-select
					v-model="state.ruleForm.deviceIds"
					placeholder="请选择关联设备"
					clearable
					style="width: 100%"
				>
					<div v-for="item in props.devices" :key="item.id">
						<el-option
							v-if="item.sourceType === state.ruleForm.sourceType"
							:label="`设备编号：${item.num}，名称：${item.name}${
								item.channelName ? `[${item.channelName}]` : ''
							}`"
							:value="item.id"
						/>
					</div>
				</el-select>
			</el-form-item>

			<el-form-item
				label="关联模型"
				prop="aiModelId"
				:rules="[{ required: true, message: '关联模型不能为空', trigger: 'blur' }]"
			>
				<el-select
					v-model="state.ruleForm.aiModelId"
					placeholder="请选择关联模型"
					clearable
					style="width: 100%"
				>
					<el-option
						v-for="item in props.aiModels"
						:key="item.id"
						:label="item.name"
						:value="item.id"
					/>
				</el-select>
			</el-form-item>

			<el-form-item
				:class="state.ruleForm.allDay === false ? 'mb2' : ''"
				label="每日监测时间段"
				prop="allDay"
				:rules="[{ required: true, message: '每日监测时间段不能为空', trigger: 'blur' }]"
				style="width: 100%"
			>
				<el-radio-group v-model="state.ruleForm.allDay" class="ml-4">
					<el-radio :label="true">全天</el-radio>
					<el-radio :label="false">自定义</el-radio>
				</el-radio-group>
			</el-form-item>

			<el-form-item
				v-if="state.ruleForm.allDay === false"
				label=""
				prop="periodTime"
				:rules="[{ required: true, message: '每日监测时间段不能为空', trigger: 'blur' }]"
			>
				<el-time-picker
					v-model="state.ruleForm.periodTime"
					is-range
					range-separator="-"
					start-placeholder="监测开始时间"
					end-placeholder="监测结束时间"
					style="width: 100%"
				/>
			</el-form-item>

			<el-form-item
				label="监测间隔/s"
				prop="sampleInterval"
				:rules="[
					{ required: true, message: '监测间隔不能为空', trigger: 'blur' },
					{ validator: validatorSampleInterval, trigger: 'change' },
				]"
			>
				<el-select
					v-model="state.ruleForm.sampleInterval"
					placeholder="请选择监测间隔（秒）"
					clearable
					style="width: 100%"
				>
					<el-option label="5秒" :value="5" />
					<el-option label="10秒" :value="10" />
					<el-option label="30秒" :value="30" />
					<el-option label="1分钟" :value="60" />
					<el-option label="5分钟" :value="300" />
					<el-option label="10分钟" :value="600" />
					<el-option label="20分钟" :value="1200" />
					<el-option label="30分钟" :value="1800" />
					<el-option label="45分钟" :value="2700" />
					<el-option label="60分钟" :value="3600" />
				</el-select>
			</el-form-item>

			<el-form-item
				label="视频录制时长/s"
				prop="sampleDuration"
				:rules="[
					{ required: true, message: '视频录制时长不能为空', trigger: 'blur' },
					{ validator: validateSampleDuration, trigger: 'blur' },
				]"
			>
				<el-select
					v-model="state.ruleForm.sampleDuration"
					placeholder="请选择视频录制时长（秒）"
					clearable
					style="width: 50%"
				>
					<el-option label="5秒" :value="5" />
					<el-option label="10秒" :value="10" />
				</el-select>
			</el-form-item>

			<!-- <el-form-item
				label="预警频度/s"
				prop="warnFrequency"
				:rules="[
					{ required: true, message: '预警频度不能为空', trigger: 'blur' },
					{ validator: validateWarnFrequency, trigger: 'change' },
				]"
			>
				<el-select v-model="state.ruleForm.warnFrequency" placeholder="请选择预警频度（秒）" clearable style="width: 100%;">
					<el-option label="1分钟" :value="60" />
					<el-option label="5分钟" :value="300" />
					<el-option label="10分钟" :value="600" />
				</el-select>
			</el-form-item> -->

			<!-- <el-form-item label="未发现物种不上报">
				<el-switch inline-prompt active-text="OFF" inactive-text="NO" />
			</el-form-item> -->
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="onCancel">取 消</el-button>
				<el-button type="primary" @click="onSubmit">{{ state.dialog.submitTxt }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';
import type { FormInstance } from 'element-plus';
import { createMonitor, updateMonitor } from '/@/api/monitors';
import { formatDate } from '/@/utils/formatTime';
import { ElMessage } from 'element-plus';

const props = withDefaults(
	defineProps<{
		devices: SelectOptions[];
		aiModels: SelectOptions[];
	}>(),
	{
		devices: () => [],
		aiModels: () => [],
	}
);

const emits = defineEmits(['refresh']);

const dialogFormRef = ref<FormInstance>();
// 用reactive时数据回显失败
const state = ref({
	ruleForm: {} as DialogMonitor | EmptyObjectType,
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
	// deviceIsMultiple: false,
});
// targetDevice存在时，默认选中该设备
const openDialog = (
	type: string,
	row?: MonitorRow,
	targetDevice?: { id: string; sourceType: number }
) => {
	nextTick(() => {
		if (type === 'add') {
			state.value.dialog.title = '新增任务';
			state.value.dialog.submitTxt = '新 增';
		} else if (type === 'edit') {
			state.value.dialog.title = '修改任务';
			state.value.dialog.submitTxt = '修 改';
		} else {
			// 复制
			state.value.dialog.title = '新增任务';
			state.value.dialog.submitTxt = '新 增';
		}
		if (row) {
			console.log(row);
			const {
				id,
				name,
				deviceId,
				deviceModel,
				aiModelId,
				allDay,
				periodStartTime,
				periodEndTime,
				sampleInterval,
				sampleDuration,
				warnFrequency,
			} = row;
			state.value.ruleForm = {
				id,
				name,
				deviceIds: deviceId,
				sourceType: deviceModel?.sourceType ?? 1,
				processType: 0,
				aiModelId,
				allDay,
				periodTime: allDay
					? []
					: [
							`${formatDate(new Date(), 'YYYY-mm-dd')} ${periodStartTime}`,
							`${formatDate(new Date(), 'YYYY-mm-dd')} ${periodEndTime}`,
					  ],
				sampleInterval,
				sampleDuration,
				warnFrequency,
			};
			if (type === 'copy') {
				delete state.value.ruleForm.deviceIds;
				delete state.value.ruleForm.sourceType;
			}
		} else {
			state.value.ruleForm = {
				allDay: true,
				processType: 0,
				deviceIds: targetDevice ? targetDevice.id : undefined,
				sourceType: targetDevice ? targetDevice.sourceType : 1,
				sampleDuration: 5,
			};
		}
		// state.value.deviceIsMultiple = type !== 'edit';
		state.value.dialog.type = type;
		state.value.dialog.isShowDialog = true;
		dialogFormRef.value?.resetFields();
	});
};

const onSourceTypeChange = () => {
	state.value.ruleForm.deviceIds = null;
};
// const handleCheckboxChange = (value: boolean) => {
// 	const now = formatDate(new Date(), 'YYYY-mm-dd')
// 	if (value) {
// 		state.ruleForm.periodTime = [`${now} 00:00:00`, `${now} 23:59:59`];
// 	} else {
// 		state.ruleForm.periodTime = [];
// 	}
// }

const validatorSampleInterval = (rule: any, value: any, callback: any) => {
	if (state.value.ruleForm.sampleDuration) {
		dialogFormRef.value?.validateField(['sampleDuration']);
	}
	if (state.value.ruleForm.warnFrequency) {
		dialogFormRef.value?.validateField(['warnFrequency']);
	}
	callback();
};

const validateSampleDuration = (rule: any, value: any, callback: any) => {
	const sampleInterval = state.value.ruleForm.sampleInterval;
	if (sampleInterval && (value < 3 || value > sampleInterval || value > 30)) {
		callback(new Error('视频录制时长介于3s-30s之间，且不能超过监测间隔'));
		return;
	}
	callback();
};

// const validateWarnFrequency = (rule: any, value: any, callback: any) => {
// 	const sampleInterval = state.value.ruleForm.sampleInterval;
// 	if (sampleInterval && value < sampleInterval) {
// 		callback(new Error('预警频度必须大于监测间隔'));
// 		return
// 	}
// 	callback();
// };

const onCancel = () => {
	state.value.dialog.isShowDialog = false;
};

const onSubmit = () => {
	dialogFormRef.value?.validate((valid: boolean) => {
		if (!valid) return;
		const query = {
			...state.value.ruleForm,
			processType: 0,
			periodStartTime: state.value.ruleForm.allDay
				? '00:00:00'
				: formatDate(new Date(state.value.ruleForm.periodTime[0]), 'HH:MM:SS'),
			periodEndTime: state.value.ruleForm.allDay
				? '23:59:59'
				: formatDate(new Date(state.value.ruleForm.periodTime[1]), 'HH:MM:SS'),
			warnFrequency: 0, // 预警频度隐藏后，默认0
		} as DialogMonitor;
		delete query.periodTime;

		if (state.value.dialog.type === 'edit') {
			query.deviceId = query.deviceIds as string;
			delete query.deviceIds;
			updateMonitor(query).then(() => {
				ElMessage.success('修改成功');
				onCancel();
				emits('refresh');
			});
		} else {
			if (query.id) delete query.id;
			query.deviceIds = [query.deviceIds] as string[];
			createMonitor(query).then(() => {
				ElMessage.success('新增成功');
				onCancel();
				emits('refresh', true, true);
			});
		}
	});
};

defineExpose({
	openDialog,
});
</script>

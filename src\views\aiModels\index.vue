<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<el-form-item label="识别类型:">
					<el-radio-group v-model="searchOptions.filter.recType">
						<el-radio v-for="item in METypeOptions" :key="item.id" :label="item.eventType">
							{{ item.name }}
						</el-radio>
					</el-radio-group>
				</el-form-item>
				<el-form-item label="模型名称">
					<el-input v-model="searchOptions.filter.name" placeholder="请输入模型名称" clearable>
						<template #suffix>
							<el-icon><ele-Search /></el-icon>
						</template>
					</el-input>
				</el-form-item>
			</template>

			<template #searchBtns>
				<div v-auths="['*:*:*', 'ai-models:*:*']">
					<el-button type="primary" @click="onCreate">
						<template #icon>
							<el-icon><ele-Plus /></el-icon>
						</template>
						新增
					</el-button>
					<!-- <el-button type="success" :disabled="tableRef?.selectRows.length !== 1"
            @click="onUpdateRow(null)">
            <template #icon>
              <el-icon><ele-Edit /></el-icon>
            </template>
            修改
          </el-button> -->
					<!-- <el-button type="danger" :disabled="tableRef?.selectRows.length === 0"
            @click="onBatchDelete">
            <template #icon>
              <el-icon><ele-Delete /></el-icon>
            </template>
            删除
          </el-button> -->
				</div>
			</template>
		</ViewSearch>

		<Table v-bind="tableOptions" ref="tableRef" @pageChange="onPageChange">
			<template #operate="{ row }">
				<el-button size="small" text type="primary" @click="onUpdateRow(row)">
					<el-icon><ele-EditPen /></el-icon>
					修改
				</el-button>
				<!-- <el-button size="small" text type="primary" @click="onDelRow(row.id)">
          <el-icon><ele-Delete /></el-icon>
          删除
        </el-button> -->
			</template>
		</Table>
		<CreateDialog
			ref="createDialogRef"
			:modelTypes="METypeOptions"
			@refresh="onRefresh"
			@resetAiModels="onResetAiModels"
		></CreateDialog>
	</div>
</template>

<script setup lang="ts" name="AiModels">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
// import { ElMessage, ElMessageBox } from 'element-plus';
// import { getAiModels, deleteAiModel, batchDeleteAiModel } from '/@/api/aiModels';
import { getAiModels } from '/@/api/aiModels';
import { useAiModels } from '/@/hooks/useAiModels';
import { AUTHS } from '/@/directive/authDirective';
import { getMonitorEventTypes } from '/@/api/monitorEvents';
// 引入异步组件
const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));
const CreateDialog = defineAsyncComponent(() => import('./dialog.vue'));

const aiModel = useAiModels();
const tableRef = ref<InstanceType<typeof Table>>();

const searchOptions = reactive({
	filter: {
		name: '',
		recType: '',
		sort: 'name,asc',
	},
});
const tableOptions: GlobalTableOptions<AiModelRow> = reactive({
	data: [],
	header: [
		{
			title: '模型名称',
			key: 'name',
			isCheck: true,
		},
		{
			title: '模型简介',
			key: 'describe',
			isCheck: true,
		},
		{
			title: '版本',
			key: 'versionName',
			isCheck: true,
		},
		{
			title: 'URL',
			key: 'apiUrl',
			isCheck: true,
		},
		{
			title: '创建时间',
			key: 'createTime',
			isCheck: true,
			// isDate: true,
			// format: 'YYYY-mm-dd HH:MM:SS',
		},
	],
	config: {
		loading: true,
		isSelection: false,
		isSerialNo: true,
		isOperate: AUTHS(['*:*:*', 'ai-models:*:*']),
		operateWidth: 150, // 操作列宽
		total: 0, // 总条数
	},
	pageParams: {
		page: 1,
		size: 10,
	},
});

const METypeOptions = ref<Array<{ id: number; eventType: number; name: string }>>([]);
const getMETypeOptions = async () => {
	const query = {
		groupEventType: 0,
	};
	const { payload } = await getMonitorEventTypes(query);
	METypeOptions.value = payload;
};

onMounted(() => {
	getMETypeOptions();
	initTableData();
});

const initTableData = async () => {
	tableOptions.config.loading = true;
	try {
		const query: any = {
			page: tableOptions.pageParams?.page && tableOptions.pageParams.page - 1,
			size: tableOptions.pageParams?.size,
			...searchOptions.filter,
		};
		const { payload } = await getAiModels(query);
		tableOptions.config.loading = false;
		tableOptions.data = payload.content;
		tableOptions.config.total = payload.totalElements;
	} catch (e) {
		tableOptions.config.loading = false;
		tableOptions.config.total = 0;
		tableOptions.data = [];
	}
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetFilter) {
		searchOptions.filter.recType = '';
		searchOptions.filter.name = '';
	}
	if (resetPage) {
		tableRef.value?.pageReset();
	} else {
		initTableData();
	}
	tableRef.value?.clearSelection();
};

const onPageChange = async (page: { pageNum: number; pageSize: number }) => {
	if (tableOptions.pageParams) {
		tableOptions.pageParams.page = page.pageNum;
		tableOptions.pageParams.size = page.pageSize;
	}
	initTableData();
};

// const onBatchDelete = () => {
//   ElMessageBox({
//     title: '提示',
//     message: '此操作将永久删除，是否继续?',
//     type: 'warning',
//     showCancelButton: true,
//   }).then(async () => {
//     const ids = tableRef?.value?.selectRows.map((item) => item.id) as string[];
//     await batchDeleteAiModel(ids);
//     ElMessage.success('删除成功');
//     onRefresh();
//     onResetAiModels();
//   })
//     .catch(() => { })
// }

// const onDelRow = (rowId: string) => {
//   ElMessageBox({
//     title: '提示',
//     message: '此操作将永久删除，是否继续?',
//     type: 'warning',
//     showCancelButton: true,
//   }).then(async () => {
//     await deleteAiModel(rowId);
//     ElMessage.success('删除成功');
//     onRefresh();
//     onResetAiModels();
//   })
//     .catch(() => { })
// };

// 模型更新时（增/改/删）
const onResetAiModels = () => {
	aiModel.onAiModelChange();
};

const createDialogRef = ref<InstanceType<typeof CreateDialog>>();
const onCreate = () => {
	createDialogRef.value?.openDialog();
};
const onUpdateRow = (row: AiModelRow | null) => {
	if (!row) {
		row = tableOptions.data.find(
			(item) => item.id === tableRef?.value?.selectRows[0].id
		) as AiModelRow;
	}
	createDialogRef.value?.openDialog(row);
};
</script>

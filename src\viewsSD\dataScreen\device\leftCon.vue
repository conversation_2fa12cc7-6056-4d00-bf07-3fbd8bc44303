<template>
	<div class="left-container">
		<!-- type stat -->
		<div class="module stat">
			<div class="module-title">
				<span
					><img class="species" src="/src/assets/sd/data-screen/species.png" alt="" />物种种类统计
				</span>
			</div>
			<div class="hint">数字指自2023-01-01日起设备发现种类数量</div>
			<div class="module-cont">
				<div class="num-stat">
					<div class="num-stat-cont">
						<div class="num-stat-item" v-for="(item, index) in stat1" :key="item.key">
							<div
								class="text"
								:style="{
									backgroundImage: index > 1 ? 'url(' + item.bgimg + ')' : '',
								}"
							>
								{{ item.label }}
							</div>
							<div
								class="count"
								:style="{
									backgroundImage: index < 2 ? 'url(' + item.bgimg + ')' : '',
								}"
							>
								{{ item.count }}
							</div>
						</div>
					</div>
				</div>
				<!-- monitor trend -->
				<div class="monitor-trend">
					<div class="module-title">
						<span
							><img class="trend" src="/src/assets/sd/data-screen/trend.png" alt="" />发现种类趋势
						</span>
					</div>
					<div class="monitor-trend-cont">
						<Echarts id="typeMonitorTrendChart" ref="typeMonitorTrendChartRef"></Echarts>
					</div>
				</div>
			</div>
		</div>

		<!-- recent monitor -->
		<div class="module recent-monitor">
			<div class="module-title">
				<span
					><img class="device" src="/src/assets/sd/data-screen/device.png" alt="" />最近发现
				</span>
				<img @click="onToMore" src="/src/assets/sd/data-screen/more.png" alt="" />
			</div>
			<div class="module-cont recent-monitor-cont">
				<RecentMonitor
					:recentMonitorEvents="recentMonitorEvents"
					animationName="animate__fadeInLeft"
				></RecentMonitor>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import {
	mergeLineOptions,
	ECOption,
	chartLinearColors,
	transDate,
} from '/@/components/echarts/mergeOptions';
import { getNumStat } from '/@/api/sd/dataScreen/device';
import { getSpeciesTypeTrend, getRecentDiscovered } from '/@/api/sd/dataScreen/bird';
import Echarts from '/@/components/echarts/echarts.vue';
type Chart = InstanceType<typeof Echarts>;
import RecentMonitor from '../component/recentMonitor/index.vue';
import { useInterval, markNewData } from '/@/hooks/useInterval';
const router = useRouter();
const props = defineProps<{
	selectedDevices: string[];
	timeType: number | null;
}>();

import item1 from '/@/assets/sd/data-screen/species-item1.png';
import item2 from '/@/assets/sd/data-screen/species-item2.png';
import item3 from '/@/assets/sd/data-screen/species-item3.png';
import item4 from '/@/assets/sd/data-screen/species-item4.png';

// 物种种类统计
const stat1 = ref([
	{
		key: 'speciesType',
		label: '全部',
		bgimg: item1,
		count: 0,
	},
	{
		key: 'tagNumMap',
		subKey: '2',
		label: '珍稀',
		bgimg: item2,
		count: 0,
	},
	{
		key: 'tagNumMap',
		subKey: '1',
		label: '特定',
		bgimg: item3,
		count: 0,
	},
	{
		key: 'tagNumMap',
		subKey: '0',
		label: '当地',
		bgimg: item4,
		count: 0,
	},
]);
const getStat1 = async () => {
	const query = {
		deviceList: props.selectedDevices,
		timeType: props.timeType,
	};
	const { payload } = await getNumStat(query);
	stat1.value.forEach((item) => {
		if (item.subKey) {
			item.count = payload[item.key][item.subKey] || 0;
		} else {
			item.count = payload[item.key] || 0;
		}
	});
};
// 趋势
const typeMonitorTrendChartRef = ref<Chart>();
const getTypeMonitorTrend = async () => {
	const query = {
		deviceList: props.selectedDevices,
		timeType: props.timeType,
	};
	const { payload } = await getSpeciesTypeTrend(query);
	const chartData = payload.map((et: SpeciesEvent) => {
		const date = transDate(props.timeType, et.date);
		return [date, et.speciesType];
	});
	const options: ECOption = {
		dataset: {
			source: chartData,
		},
		color: [chartLinearColors[0]],
		series: [
			{
				type: 'line',
				name: '发现物种种类',
				symbol: 'circle',
				symbolSize: 6,
				showSymbol: false,
				areaStyle: {
					color: chartLinearColors[2],
				},
			},
		],
	};
	typeMonitorTrendChartRef.value?.resetOption(mergeLineOptions(options));
};
// 最近发现
const onToMore = () => {
	router.push({ name: 'DataScreenAtlas', query: { pagePath: 'device' } });
};
const recentMonitorEvents = ref<RecentMonitorEvent[]>([]);
const getRecentMonitorEvents = async () => {
	const query = {
		deviceList: props.selectedDevices,
		timeType: props.timeType,
		rankCount: 3,
	};
	const { payload } = await getRecentDiscovered(query);
	recentMonitorEvents.value = markNewData(payload.content, recentMonitorEvents.value, 'eventId');
};
useInterval(getRecentMonitorEvents);
watch(
	[() => props.selectedDevices, () => props.timeType],
	() => {
		getStat1();
		getTypeMonitorTrend();
		recentMonitorEvents.value = [];
		getRecentMonitorEvents();
	},
	{ immediate: true }
);
</script>

<style lang="scss" scoped>
.hint {
	height: vh(17);
	color: $sd-screen-primary;
	font-size: 12px;
	margin: vh(14) 0;
}
.num-stat {
	height: vh(169);
	margin-bottom: vh(35);
	.title {
		font-size: vw(16);
		font-family: 'ALiMaMaShuHeiTi';
	}
	.num-stat-cont {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		margin: vw(15) 0 vw(3);
		background: url('/@/assets/sd/data-screen/species-bg.png');
		background-repeat: no-repeat;
		background-position: center;
		background-size: vw(60) vh(60);

		.num-stat-item {
			width: vw(130);
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			&:nth-child(-n + 2) {
				.text {
					height: 30%;
				}
				.count {
					height: 70%;
				}
			}

			&:nth-child(n + 3) {
				.text {
					display: flex;
					align-items: flex-end;
				}
			}

			&:nth-child(2n) {
				text-align: right;
				.text {
					justify-content: flex-end;
				}
			}
			.text {
				height: 70%;
				font-size: vw(16);
				background-repeat: no-repeat;
				background-size: 100% vh(22);
				background-position: 0 100%;
				padding-bottom: vh(5);
				span {
					font-size: vw(13);
				}
			}
			.count {
				height: 30%;
				color: $sd-screen-success;
				font-size: vw(28);
				font-family: 'ALiMaMaShuHeiTi';
				background-repeat: no-repeat;
				background-size: 100% vh(22);
				padding-top: vh(2);
			}
		}
	}
}
.monitor-trend {
	.title {
		font-size: vw(16);
		font-family: 'ALiMaMaShuHeiTi';
	}
	.monitor-trend-cont {
		height: vh(240);
	}
}
</style>

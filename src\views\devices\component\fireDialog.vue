<template>
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		width="580px"
		align-center
	>
		<el-form
			ref="dialogFormRef"
			:model="state.ruleForm"
			:rules="rules"
			label-width="100px"
			size="large"
		>
      <el-form-item label="设备编号" prop="num">
        <el-input
          v-model="state.ruleForm.num"
          placeholder="请输入设备编号（例：1921687165）"
          clearable
        ></el-input>
      </el-form-item>

      <el-form-item label="设备名称" prop="name">
        <el-input v-model="state.ruleForm.name" placeholder="请输入设备名称" clearable></el-input>
      </el-form-item>

      <el-form-item label="设备IP" prop="productId">
        <el-input v-model="state.ruleForm.productId" placeholder="请输入设备IP" clearable></el-input>
      </el-form-item>

      <el-form-item label="生产厂商" prop="manufacturer">
        <el-radio-group v-model="state.ruleForm.manufacturer">
          <el-radio v-for="(item, key) in Manufacturer" :key="key" :label="Number(key)">
            {{ item }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="热成像地址" prop="infraredVideoUrl">
				<el-input
					v-model="state.ruleForm.infraredVideoUrl"
					type="textarea"
					placeholder="请输入（例：rtsp://xxxxxx）"
					clearable
				></el-input>
			</el-form-item>

      <el-form-item label="可见光地址" prop="visibleLightVideoUrl">
				<el-input
					v-model="state.ruleForm.visibleLightVideoUrl"
					type="textarea"
					placeholder="请输入（例：rtsp://xxxxxx）"
					clearable
				></el-input>
			</el-form-item>

      <el-form-item label="坐标类型" prop="coordinateType">
				<el-radio-group v-model="state.ruleForm.coordinateType">
					<el-radio v-for="(item, key) in Coordinate_Type" :key="key" :label="Number(key)">
						{{ item }}
					</el-radio>
				</el-radio-group>
			</el-form-item>

			<el-form-item label="经度" prop="longitude">
				<el-input
					v-model="state.ruleForm.longitude"
					placeholder="请输入经度（例：116.40）"
					clearable
				></el-input>
			</el-form-item>
      
			<el-form-item label="纬度" prop="latitude">
				<el-input
					v-model="state.ruleForm.latitude"
					placeholder="请输入纬度（例：39.92）"
					clearable
				></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="onCancel">取 消</el-button>
				<el-button type="primary" @click="onSubmit">{{ state.dialog.submitTxt }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';
import { createFireDevice, updateFireDevice } from '/@/api/devices';
import { Coordinate_Type, Manufacturer } from '/@/utils/constants';

const emits = defineEmits<{
	(e: 'refresh', resetPage?: boolean, resetFilter?: boolean): void;
}>();

// 定义变量内容
const dialogFormRef = ref<FormInstance>();
const state = reactive<FireDeviceDialogState>({
	ruleForm: {
		name: '',
		num: '',
    manufacturer: 1,
    productId: '',
    infraredVideoUrl: '',
    visibleLightVideoUrl: '',
		longitude: null,
		latitude: null,
    coordinateType: 1,
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
});
// 校验经纬度
const reg = /^-?\d+(\.\d+)?$/;
const validLongitude = (rule: any, value: string, callback: Function) => {
	if (value && !reg.test(value)) {
		callback(new Error('请输入有效的经度'));
		return;
	}
	if (value && !(Number(value) >= -180 && Number(value) <= 180)) {
		callback(new Error('经度范围在 -180 到 180 之间'));
		return;
	}
	callback();
};
const validLatitude = (rule: any, value: string, callback: Function) => {
	if (value && !reg.test(value)) {
		callback(new Error('请输入有效的纬度'));
		return;
	}
	if (value && !(Number(value) >= -90 && Number(value) <= 90)) {
		callback(new Error('纬度范围在 -90 到 90 之间'));
		return;
	}
	callback();
};
const rules = {
	name: { required: true, message: '设备名称不能为空', trigger: 'blur' },
	num: { required: true, message: '设备编号不能为空', trigger: 'blur' },
  productId: { required: true, message: '设备IP不能为空', trigger: 'blur' },
  manufacturer: { required: true, message: '生产厂商不能为空', trigger: 'blur' },
	infraredVideoUrl: { required: true, message: '热成像地址不能为空', trigger: 'blur' },
  visibleLightVideoUrl: { required: true, message: '可见光地址不能为空', trigger: 'blur' },
	longitude: [
		{ validator: validLongitude, trigger: 'blur' },
	],
	latitude: [
		{ validator: validLatitude, trigger: 'blur' },
	],
};

// 打开弹窗
const openDialog = (row?: FireDeviceRow) => {
	state.dialog.isShowDialog = true;
	nextTick(() => {
		dialogFormRef.value?.resetFields();
		if (row && row.name) {
			state.dialog.type = 'edit';
			state.dialog.title = '修改设备';
			state.dialog.submitTxt = '修 改';
      // 回显数据
      state.ruleForm.name = row.name;
      state.ruleForm.num = row.num;
      state.ruleForm.manufacturer = row.manufacturer;
      state.ruleForm.productId = row.productId;
      // 修改时需传递 `infraredId` 和 `visibleLightId`
      state.ruleForm.infraredId = row.infraredId;
      state.ruleForm.visibleLightId = row.visibleLightId;
      state.ruleForm.infraredVideoUrl = row.infraredVideoUrl;
      state.ruleForm.visibleLightVideoUrl = row.visibleLightVideoUrl;
      state.ruleForm.coordinateType = row.coordinateType;
      state.ruleForm.longitude = row.longitude;
      state.ruleForm.latitude = row.latitude;
		} else {
			state.dialog.type = 'add';
			state.dialog.title = '新增设备';
			state.dialog.submitTxt = '新 增';
      state.ruleForm.infraredId = '';
      state.ruleForm.visibleLightId = '';
      state.ruleForm.coordinateType = 1
		}
	});
};
const closeDialog = () => {
	state.dialog.isShowDialog = false;
};
const onCancel = () => {
	closeDialog();
};
const onSubmit = () => {
	dialogFormRef.value?.validate(async (valid: boolean) => {
		if (!valid) return;
		if (state.dialog.type === 'add') {
			await createFireDevice(state.ruleForm);
			ElMessage.success('新增成功');
			emits('refresh', true, true);
			closeDialog();
			return;
		}
		await updateFireDevice(state.ruleForm);
		ElMessage.success('修改成功');
		emits('refresh');
		closeDialog();
	});
};

defineExpose({
	openDialog,
});
</script>

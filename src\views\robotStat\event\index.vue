<template>
	<div class="event-container fullScreen">
		<Menu ref="menuRef" @menuSub="onMenuSub" @menuItem="onMenuItem" />
		<div class="map">
			<div class="map-select">
				<span class="search-title"></span>
				<span class="search-item">
					选择时间：
					<el-date-picker
						v-model="date"
						type="date"
						placeholder="点击选择日期"
						:teleported="false"
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD"
						:clearable="false"
						@change="onDateChange"
					/>
				</span>
			</div>
			<div class="map-legend" v-show="legendShow">
				<div v-for="le in legends" class="map-legend-item">
					<img class="map-legend-item-icon" :src="le.url" alt="" />
					<span class="map-legend-item-label">{{ le.name }}</span>
				</div>
			</div>
			<el-amap
				ref="mapRef"
				:center="center"
				:zoom="zoom"
				:pitch="25"
				viewMode="3D"
				mapStyle="amap://styles/blue"
				:features="['bg', 'point', 'road', 'building']"
				@click="clickMap"
				@init="initMap"
			>
				<el-amap-mass-marks
					:visible="visible"
					:data="points"
					:styles="styles"
					@init="markerInit"
					@click="clickMarker"
				/>
				<!-- 信息窗口 -->
				<template v-if="infoWindow.center.length">
					<el-amap-info-window
						:autoMove="true"
						:visible="infoWindow.visible"
						:avoid="[100, 100, 100, 100]"
						:position="infoWindow.center"
						:isCustom="true"
						@close="handelInfoWindowClose"
					>
						<div class="info-window">
							<div class="info-window-top">
								<div class="info-window-close" @click="infoWindow.visible = false">关闭</div>
							</div>
							<el-image
								title="点击放大图片"
								class="disease-img"
								:src="infoWindow.picture"
								:preview-teleported="true"
								:zoom-rate="1.2"
								:max-scale="7"
								:min-scale="0.2"
								:z-index="99999"
								:preview-src-list="[infoWindow.picture]"
								:initial-index="0"
								fit="contain"
								draggable="false"
							>
								<template #placeholder>
									<div class="image-placeholder">
										<el-icon class="is-loading">
											<ele-Loading />
										</el-icon>
									</div> </template
								><template #error>
									<div class="load-error">
										<img
											class="big-img-error"
											src="/src/assets/fullScreen/big-load-error.png"
											title="加载失败"
											alt=""
										/>
									</div>
								</template>
							</el-image>
							<div class="info-window-text">
								<span>{{ infoWindow.pointName }}{{ infoWindow.recResult }}</span>
								<div class="info-window-type">
									<img
										src="/src/assets/fullScreen/type.png"
										alt=""
										style="width: 15px; height: 15px"
									/>
									{{ infoWindow.parentEventTypeName }}
								</div>
								<span>{{ infoWindow.recTime }}</span>
							</div>
						</div>
					</el-amap-info-window>
				</template>
			</el-amap>
		</div>
		<Stat ref="statRef" :seriesColors="seriesColors" :date="date"></Stat>
	</div>
</template>

<script lang="ts" setup>
import { ref, shallowReactive, reactive, nextTick } from 'vue';
import { convertCoordinates } from '/@/utils/coordinates';
import Menu from './menu.vue';
import Stat from './stat.vue';
import { getRobotsEvents, getRobotsEventsOverview } from '/@/api/robotStat';
import { getRobotsEventById } from '/@/api/home';
import { ElAmap, ElAmapMassMarks } from '@vuemap/vue-amap';
import { formatDate } from '/@/utils/formatTime';
import other from '/@/utils/other';

const date = ref(formatDate(new Date(), 'YYYY-mm-dd'));
const zoom = ref(17);
const center = ref([116.31452328617829, 39.91963527721522]);
const legendShow = ref(true);
const points = ref([]);

const styles = ref([
	{
		url: 'https://webapi.amap.com/images/mass/mass0.png',
		anchor: [6, 6],
		size: [11, 11],
		zIndex: 3,
	},
	{
		url: 'https://webapi.amap.com/images/mass/mass1.png',
		anchor: [4, 4],
		size: [7, 7],
		zIndex: 2,
	},
	{
		url: 'https://webapi.amap.com/images/mass/mass2.png',
		anchor: [3, 3],
		size: [5, 5],
		zIndex: 1,
	},
]);

const visible = ref(true);
const changeVisible = () => {
	visible.value = !visible.value;
};

const clickMap = (e: any) => {
	// console.log('click map: ', e);
	handelInfoWindowClose();
};

let map: AMap.Map;
const initMap = (amap: AMap.Map) => {
	map = amap;
};

const markerInit = (e: any) => {
	console.log('marker init: ', e);
};

// 点击地图事件圆点，显示详情弹窗
const infoWindow = reactive({
	visible: false,
	center: [] as any,
	picture: '',
	recResult: '',
	pointName: '',
	recTime: '',
	eventType: -1,
	parentEventTypeName: '',
});
const clickMarker = async (event: any) => {
	const { lnglat, eventId, parentEventTypeName } = event.data;
	// # 优化：由于地图事件分布接口响应时间过长，所以后台减少了不必要的信息；点击地图事件圆点时，通过`eventId`获取更多事件详情。
	const { payload } = await getRobotsEventById(eventId);
	map.setStatus({
		animateEnable: map.getBounds().contains(new AMap.LngLat(lnglat[0], lnglat[1])),
	});
	infoWindow.visible = true;
	infoWindow.center = lnglat;
	infoWindow.picture = payload.pictureUrl || payload.oriPictureThumbnailUrl || '';
	infoWindow.pointName = payload.pointName;
	infoWindow.recResult = payload?.monitorEventDetails[0]?.recResult;
	infoWindow.recTime = payload.recTime;
	infoWindow.eventType = payload.eventType;
	infoWindow.parentEventTypeName = parentEventTypeName;
};
const handelInfoWindowClose = () => {
	infoWindow.visible = false;
	infoWindow.picture = '';
	infoWindow.recResult = '';
	infoWindow.recTime = '';
};

const legends = ref();
const onMenuSub = (groupEventType: number, events: any) => {
	if (!groupEventType && !events) return;
	console.log('onMenuSub', groupEventType, events);
	legends.value = styles.value = events.map((item: any) => {
		return {
			...item,
			url: mapIcon[item.eventType],
			anchor: [10, 10],
			size: [20, 20],
			// zIndex: events.findIndex((i: any) => i.eventType === item.eventType),
		};
	});
	legendShow.value = true;
	statRef.value.pieShow = true;
	statRef.value.onGetRobotsEventsStat({ groupEventType });
	onGetRobotsEventsOverview({ groupEventType });
};
const onMenuItem = (eventType: number, events: any) => {
	console.log('onMenuItem', events);
	if (events) {
		statRef.value.pieShow = true;
		// 全部
		legends.value = styles.value = events
			.map((item: any) => {
				if (item.groupEventType !== -1) {
					return {
						...item,
						url: mapIcon[item.groupEventType],
						anchor: [8, 8],
						size: [17, 17],
						// zIndex: events.findIndex((i: any) => i.eventType === item.groupEventType),
					};
				}
			})
			.filter((item: any) => item);
		legendShow.value = true;
		onGetRobotsEventsOverview({});
		statRef.value.onGetRobotsEventsStat({});
	} else {
		styles.value = [
			{
				url: mapIcon[eventType],
				anchor: [12, 12],
				size: [25, 25],
				zIndex: 12,
			},
		];
		statRef.value.pieShow = false;
		legendShow.value = false;
		onGetRobotsEventsOverview({ eventType });
		statRef.value.onGetRobotsEventsStat({ eventType });
	}
};
const menuRef = ref<InstanceType<typeof Menu>>();
const onDateChange = () => {
	const id = menuRef.value?.activeMenu as string;
	if (id === '-1-item' || id.includes('sub')) {
		menuRef.value?.handleClick({ index: id }, true);
	} else if (id.includes('item')) {
		menuRef.value?.handleMenuOpen(id, [id], true);
	}
};

const icon6 = other.getStaticImag('fullScreen/robotStat/event/map/6.png');
const icon7 = other.getStaticImag('fullScreen/robotStat/event/map/7.png');
const icon8 = other.getStaticImag('fullScreen/robotStat/event/map/8.png');
const icon9 = other.getStaticImag('fullScreen/robotStat/event/map/9.png');
const icon11 = other.getStaticImag('fullScreen/robotStat/event/map/11.png');
const icon13 = other.getStaticImag('fullScreen/robotStat/event/map/13.png');
const icon16 = other.getStaticImag('fullScreen/robotStat/event/map/16.png');
const mapIcon: EmptyObjectType = {
	0: icon6,
	1: icon7,
	2: icon8,
	3: icon9,
	4: icon11,
	5: icon13,

	6: icon6,
	7: icon7,
	8: icon8,
	9: icon9,
	11: icon11,
	16: icon13,
	13: icon16,

	10: icon6,
	15: icon7,
	17: icon9,
};
const seriesColors: EmptyObjectType = shallowReactive({
	0: '#22D69F',
	1: '#F8CE6B',
	2: '#114CFF',
	3: '#9123C8',
	4: '#FF6000',
	5: '#0BBDFF',

	6: '#fd8136',
	7: '#a23bff',
	8: '#4ac6fb',
	9: '#f8ce6b',
	11: '#22d69f',
	16: '#eb49f9',
	13: '#ff1515',

	10: '#fd8136',
	15: '#a23bff',
	17: '#4ac6fb',
});

const statRef = ref();
const onGetRobotsEventsOverview = async (params = {}) => {
	handelInfoWindowClose();
	// # `/statistics/robots_events_overview`接口响应时间过长，所以将地图事件分布与右侧事件类型概览拆成两个接口
	const query = {
		date: date.value,
		groupEventType: '',
		eventType: '',
		...params,
	};
	getMapEvents(query);
	getEventOverview(query);
};
// 获取中间地图事件分布
const getMapEvents = async (query: any = {}) => {
	points.value = [];
	const { payload } = await getRobotsEvents(query);
	points.value = payload.events
		.map((event: any) => {
			if (!event.longitude && !event.latitude) return;
			console.log(event.longitude, event.latitude);
			// 样式索引：获取全部时，以事件groupEventType查找；大类以eventType查找；小类索引为0
			let style = 0;
			const menuId = menuRef.value?.activeMenu;
			if (menuId === '-1-item') {
				style = styles.value.findIndex((item: any) => item.groupEventType === event.groupEventType);
			} else if (menuId?.includes('item')) {
				style = styles.value.findIndex((item: any) => item.eventType === event.eventType);
			}

			return {
				lnglat: convertCoordinates(event.longitude, event.latitude, event.coordinateType),
				// name: event.remark,
				style,
				eventId: event.id,
				parentEventTypeName: event.parentEventTypeName,
			};
		})
		.filter((event: any) => event);

	// console.log(points.value);
	nextTick(() => {
		map.setFitView(undefined, true, [10, 10, 10, 10]);
	});
};
// 获取右侧事件类型
const getEventOverview = async (query: Object) => {
	const { payload } = await getRobotsEventsOverview(query);
	if (!payload?.overview?.length) {
		statRef.value.handleOverviewStat([], []);
		return;
	}
	const xData: string[] = [];
	let sData: any = [];
	const total = payload.overview?.reduce(
		(accumulator: any, item: { count: number }) => accumulator + item.count,
		0
	);
	sData = payload.overview.map((item: any) => {
		xData.push(item.name);
		return {
			...item,
			name: item.name,
			percent: Number(((item.count / total) * 100).toFixed(2)),
			value: item.count,
			itemStyle: {
				color: seriesColors[item.eventType],
			},
		};
	});
	statRef.value.handleOverviewStat(xData, sData);
};
</script>

<style lang="scss" scoped>
%bg-r-s {
	background-repeat: no-repeat;
	background-size: 100% 100%;
}
.event-container {
	width: 100%;
	height: 100%;
	display: flex;
	.map {
		position: relative;
		flex: 1;
		background-color: transparent;
		padding: 2px;
		margin: 0 vw(20);
		background-image: url('/src/assets/fullScreen/robotStat/event/map-bg.png');
		@extend %bg-r-s;
		&-select {
			position: absolute;
			top: 2px;
			left: 2px;
			z-index: 1;
			width: calc(100% - 4px);
			height: vh(55);
			background: linear-gradient(to bottom, rgba(0, 0, 0, 1), transparent);
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 vw(20);

			.search-title {
				width: vw(106);
				height: vh(29);
				background-image: url('/src/assets/fullScreen/robotStat/event/shijianfenbu.png');
				@extend %bg-r-s;
			}
			.search-item {
				&:deep(.el-input__inner::placeholder) {
					// color: rgba(255, 255, 255, 1);
					font-size: 14px;
				}
				&:deep(.el-select-dropdown__item.selected) {
					background-color: transparent !important;
					color: rgb(104, 207, 173);
				}

				&:deep(.el-input) {
					.el-input__wrapper {
						border-radius: 50px !important;
						border: 1px solid rgba(104, 207, 173, 1) !important;
						background-color: transparent !important;
						box-shadow: none !important;
						padding: 0 11px !important;
					}
					.el-input__inner {
						--el-input-inner-height: vh(30) !important;
						color: rgba(255, 255, 255, 1);
					}
				}

				&:deep(.el-popper.is-light .el-popper__arrow::before) {
					border: 1px solid rgba(104, 207, 173, 1) !important;
					border-bottom-color: transparent !important;
					border-right-color: transparent !important;
					background: #010b07 !important;
				}
				&:deep(.el-picker-panel) {
					background: #000;
				}
			}
		}

		&-legend {
			position: absolute;
			bottom: vw(15);
			right: vw(15);
			width: auto;
			z-index: 2;
			padding: 5px 10px;
			@extend %bg-r-s;
			background-image: url('/src/assets/fullScreen/robotStat/event/tuli.png');

			&-item {
				width: 100%;
				height: 24px;
				display: flex;
				// justify-content: center;
				align-items: center;
				&-icon {
					width: 17px;
					height: 17px;
					margin-right: vw(6);
				}
				&-label {
					font-size: 12px;
				}
			}
		}
	}
}
.info-window {
	width: 668px;
	height: 514px;
	background: transparent;
	background-image: url('/src/assets/fullScreen/window-info.png');
	background-position: bottom;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	padding: 0px 20px;

	&::before {
		content: '';
		position: absolute;
		top: 10px;
		left: 24px;
		width: 66px;
		height: 29px;
		background-image: url('/src/assets/fullScreen/aq-bg.png');
		background-repeat: no-repeat;
		background-size: 100%;
	}

	.disease-img {
		width: 100%;
		height: 354px;
		.image-placeholder {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			.is-loading {
				color: #22d69f;
				font-size: 26px;
			}
		}
	}

	.info-window-top {
		height: 50px;
		display: flex;
		justify-content: end;
		color: #fff;
		line-height: 50px;
		.info-window-close {
			cursor: pointer;
		}
	}

	.info-window-text {
		margin-top: 10px;
		color: #fff;
		display: flex;
		flex-direction: column;
		.info-window-type {
			display: flex;
			align-items: center;
			img {
				margin-right: 5px;
			}
		}
		span {
			font-style: 14px;
			height: 20px;
			line-height: 20px;
			margin-bottom: 5px;
			&:first-child {
				font-weight: 700;
				font-size: 16px;
				line-height: 22px;
			}
		}
	}
}
</style>

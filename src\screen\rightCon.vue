<template>
	<SideContainer direction="right" v-model="isRightCollapsed">
		<!-- species frequency -->
		<div class="module species-frequency">
			<div class="module-title">
				<span
					><img class="paiming" src="/src/assets/sd/data-screen/paiming.png" alt="" />物种频次排名
				</span>
				<img @click="rankOnToMore" src="/src/assets/sd/data-screen/more.png" alt="" />
			</div>

			<div class="module-cont species-frequency-cont">
				<div class="rank-list">
					<div class="rank-item rank-title">
						<span class="rank">排名</span>
						<span class="name">名称</span>
						<span class="progress">频次占比</span>
						<span class="count">频次</span>
					</div>

					<div class="legend">
						<span> <i class="legend-specific"></i>珍稀</span>
						<span> <i class="legend-precious"></i>特定</span>
					</div>
					<div
						class="rank-list-content"
						v-loading="isLoading"
						element-loading-custom-class="chart-loading-class"
					>
						<template v-if="!isLoading">
							<div
								v-if="speciesFrequencyRanks.length > 0"
								class="rank-item rank-item-bg"
								v-for="(item, $index) in speciesFrequencyRanks"
								:key="$index"
							>
								<span class="rank">{{ $index + 1 }}</span>
								<span class="name">
									<span>{{ item.speciesAlias }}</span>
									<i v-if="item.tags === '珍稀鸟类'" class="legend-specific"></i>
									<i v-else-if="item.tags === '特殊鸟类'" class="legend-precious"></i>
								</span>
								<span class="progress">
									<el-progress
										class="data-screen-progress"
										:stroke-width="15"
										striped
										:percentage="item.ratio"
										color="rgba(49, 237, 181, 0.8)"
									/>
								</span>
								<div class="count">{{ item.speciesFrequency }}</div>
							</div>
							<el-empty v-else description="暂无数据">
								<template #image>&nbsp;</template>
							</el-empty>
						</template>
					</div>
				</div>
			</div>
		</div>

		<div class="module recent-monitor">
			<div class="module-title">
				<span
					><img class="device" src="/src/assets/sd/data-screen/device.png" alt="" />最近感知</span
				>
				<img @click="onToMore" src="/src/assets/sd/data-screen/more.png" alt="" />
			</div>
			<div class="module-cont recent-monitor-cont">
				<RecentMonitor
					:recentMonitorEvents="recentMonitorEvents"
					animationName="animate__fadeInLeft"
				></RecentMonitor>
			</div>
		</div>
	</SideContainer>
</template>

<script setup lang="ts">
import { ref, watch, inject, Ref } from 'vue';
import SideContainer from './component/sideContainer/index.vue';
import { mergePieOptions } from '/@/components/echarts/mergeOptions';
import {
	getResidencyStat,
	getSpeciesFrequencyRank,
	getRecentDiscovered,
} from '/@/api/sd/dataScreen/bird';
import { useInterval, markNewData } from '/@/hooks/useInterval';
import RecentMonitor from './component/recentMonitor/index.vue';
import Echarts from '/@/components/echarts/echarts.vue';
import { IsCompanyNmg } from '/@/hooks/useConfig';
import { useRouter } from 'vue-router';
import { createQuery } from './utils/query';
const router = useRouter();
const timeType = inject('timeType') as Ref<TimeType>;
const eventType = inject('eventType') as Ref<string>;
const isRightCollapsed = ref(false);
// 物种频次排名
const speciesFrequencyRanks = ref<SpeciesEvent[]>([]);
const isLoading = ref(false);
const getSpeciesFrequencyRanks = async () => {
	isLoading.value = true;
	try {
		const query = createQuery({
			timeType: timeType.value,
			eventType: eventType.value,
		});
		const { payload } = await getSpeciesFrequencyRank(query);
		speciesFrequencyRanks.value = payload.slice(0, 4).map((item: SpeciesEvent) => {
			return {
				...item,
				ratio: Number(((item.speciesFrequency / item.totalSpeciesFrequency) * 100).toFixed(1)),
				// tags: item.tags.replace(/鸟类/g, '').split('、'),
			};
		});
	} catch (error) {
		console.error('获取物种频次排名失败:', error);
	} finally {
		isLoading.value = false;
	}
};

// 居留类型
const residencyTypeChartRef = ref<InstanceType<typeof Echarts>>();
const getResidencyTypeStat = async () => {
	const query = createQuery({
		timeType: timeType.value,
	});
	const { payload } = await getResidencyStat(query);
	const options = {
		tooltip: {
			formatter: (params: any) => {
				return `发现${params.name}：${params.value['value']}次`;
			},
		},
		dataset: {
			source: payload.map((item: { typeText: string; num: number }) => ({
				name: item.typeText,
				value: item.num,
			})),
		},
	};
	residencyTypeChartRef.value?.resetOption(mergePieOptions(options));
};

const rankOnToMore = () => {
	router.push({
		path: '/screen/rankDetail',
		query: {
			pagePath: 'species',
			typeFilte: 10,
			timeType: timeType.value,
			eventType: eventType.value,
		},
	});
};

// 最近发现
const onToMore = () => {
	router.push({
		path: '/screen/atlas',
		query: {
			pagePath: 'species',
			eventType: eventType.value,
			timeType: timeType.value,
		},
	});
};
const recentMonitorEvents = ref<RecentMonitorEvent[]>([]);
const getRecentMonitorEvents = async () => {
	const query = createQuery({
		eventType: eventType.value,
		timeType: timeType.value,
	});

	const { payload } = await getRecentDiscovered(query);
	recentMonitorEvents.value = markNewData(payload.content, recentMonitorEvents.value, 'eventId');
};
useInterval(getRecentMonitorEvents);
watch(
	[timeType, eventType],
	() => {
		getSpeciesFrequencyRanks();
		getRecentMonitorEvents();
	},
	{ immediate: true }
);
</script>
<style lang="scss" scoped>
.recent-monitor {
	flex: 1;
}
.monitor-trend .monitor-trend-cont {
	height: vh(260);
}
.residence-type .residence-type-cont {
	height: vh(220);
}

.species-frequency {
	flex: 0 0 auto;
	height: auto;
	max-height: vw(305) !important;
	.species-frequency-cont {
		overflow: visible;

		.rank-list {
			.legend {
				height: vh(40);
				display: flex;
				justify-content: flex-end;
				align-items: center !important;

				span {
					font-size: vw(12);
					margin-right: vw(30);
				}
			}
			i {
				display: inline-block;
				font-style: normal;
				width: 10px;
				height: 10px;
				border-radius: 50%;
				margin-right: vw(5);
			}
			.legend-specific {
				background-color: #ffda00;
			}
			.legend-precious {
				background-color: #fc74d9;
			}
			.legend-nmg {
				background-color: #5af6e8;
			}
			.rank-title {
				height: vw(25);
				font-size: vw(14) !important;
				padding-bottom: 0 !important;
			}
			width: calc(100% + vw(40));
			position: relative;
			left: vw(-20);
			min-height: vw(245);
			max-height: vw(245);
			.rank-item {
				display: flex;
				align-items: center;
				text-align: center;

				.name {
					width: vw(100);
					span {
						padding-right: vw(5);
					}
					.tag-tooltips {
						display: inline-block;
						width: vw(16);
						height: vw(16);
						vertical-align: text-top;
						margin-left: vw(4);
						color: $sd-screen-primary;
						cursor: pointer;
					}
				}

				.rank {
					width: vw(28);
				}

				.progress {
					width: vw(140);
				}
				.count {
					flex: 1;
				}
				// .tag {
				//   border: 1px solid $sd-screen-primary;
				//   color: $sd-screen-primary;
				//   font-size: vw(12);
				//   padding: 0 vw(8);
				//   border-radius: vw(8);
				//   margin-left: vw(10);
				// }
			}
			.rank-list-content {
				min-height: vh(160);
				:deep(.el-loading-spinner) {
					.el-loading-text {
						color: #fff;
					}
					.circular .path {
						stroke: #31edb5;
					}
				}
			}
		}
	}
}
</style>

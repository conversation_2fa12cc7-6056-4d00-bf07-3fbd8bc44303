import { defineStore } from 'pinia';
import { getUnknownSpecies } from '/@/api/monitorEvents';

export interface AnimalTypeOption {
  label: string;
  value: number | string;
  eventType?: number;
}

export interface AnimalTypesState {
  animalTypes: AnimalTypeOption[];
  loading: boolean;
  error: string | null;
  lastFetchTime: number; // 上次获取数据的时间戳
  cacheExpireTime: number; // 缓存过期时间（毫秒）
}

/**
 * 动物类型管理 Store
 * @methods ensureAnimalTypes 确保动物类型数据存在（智能缓存）
 * @methods fetchAnimalTypes 强制获取动物类型列表
 */
export const useAnimalTypesStore = defineStore('animalTypes', {
  state: (): AnimalTypesState => ({
    animalTypes: [
      { label: '全部', value: 'all' }
    ],
    loading: false,
    error: null,
    lastFetchTime: 0,
    cacheExpireTime: 5 * 60 * 1000, // 5分钟缓存过期时间
  }),
  
  getters: {
    // 获取带全部选项的动物类型列表
    animalTypeOptions: (state): AnimalTypeOption[] => {
      return state.animalTypes;
    },

    // 获取大屏专用的动物类型列表（只包含：全部、动物、鸟类、水生）
    screenAnimalTypeOptions: (state): AnimalTypeOption[] => {
      const allowedEventTypes = [0, 1, 21]; // 动物、鸟类、水生
      return state.animalTypes.filter(item => 
        item.value === 'all' || allowedEventTypes.includes(item.value as number)
      );
    },

    // 判断缓存是否已过期
    isCacheExpired: (state): boolean => {
      const now = Date.now();
      return now - state.lastFetchTime > state.cacheExpireTime;
    },

    // 判断是否有有效数据（不仅仅是默认的"全部"选项）
    hasValidData: (state): boolean => {
      return state.animalTypes.length > 1;
    }
  },

  actions: {
    /**
     * 确保动物类型数据存在（智能缓存）
     * 如果没有数据或缓存过期，则自动获取
     */
    async ensureAnimalTypes(params?: object): Promise<void> {
      // 如果正在加载，直接返回
      if (this.loading) {
        return;
      }

      // 如果有有效数据且缓存未过期，直接返回
      if (this.hasValidData && !this.isCacheExpired) {
        return;
      }

      // 获取数据
      await this.fetchAnimalTypes(params);
    },

    /**
     * 获取动物类型列表（强制刷新）
     */
    async fetchAnimalTypes(params?: object): Promise<void> {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await getUnknownSpecies(params || {});
        
        if (response.status === 200 && response.payload) {
          // 转换 API 数据为组件需要的格式
          const apiTypes = response.payload.map((item: any) => ({
            label: item.name,
            value: item.eventType,
            eventType: item.eventType
          }));
          
          // 保持全部选项在第一位
          this.animalTypes = [
            { label: '全部', value: 'all' },
            ...apiTypes
          ];

          // 更新最后获取时间
          this.lastFetchTime = Date.now();
        }
      } catch (error) {
        console.error('获取动物类型失败:', error);
        this.error = '获取动物类型失败';
        
        // 保持默认的全部选项
        this.animalTypes = [
          { label: '全部', value: 'all' }
        ];
      } finally {
        this.loading = false;
      }
    },

    /**
     * 重置状态
     */
    resetState(): void {
      this.animalTypes = [
        { label: '全部', value: 'all' }
      ];
      this.loading = false;
      this.error = null;
      this.lastFetchTime = 0;
    },

    /**
     * 设置缓存过期时间
     */
    setCacheExpireTime(milliseconds: number): void {
      this.cacheExpireTime = milliseconds;
    }
  },

  // 开启数据持久化
  persist: {
    key: 'animal-types-store',
    paths: ['animalTypes', 'lastFetchTime'] // 持久化动物类型数据和最后获取时间
  }
});
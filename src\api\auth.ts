import request from '/@/utils/request';

/**
 * （不建议写成 request.post(xxx)，因为这样 post 时，无法 params 与 data 同时传参）
 * 登录api接口集合
 * @method signIn 本系统用户登录（登录成功后，后台会在客户端Cookies中设置：access_token）
 * @method tkSignIn ticket登录
 * @method signOut 用户退出登录
 * @method getUserInfo 用户信息
 * # 验证码
 * @method getCaptcha 获取图形验证码
 * @method verifyCaptcha 验证用户输入的code
 * 
 * # 角色权限
 * @method getRoles 系统角色
 * @method getPermissions 系统权限
 * @method getRolePermissions 获取角色已分配权限
 * @method assignRolePermission 给角色分配权限
 * @method unassignRolePermission 移除角色已有权限
 */
export function signIn(data: object) {
	return request({
		url: '/auth/login',
		method: 'post',
		data,
	});
}

export function tkSignIn(tk: string) {
	return request({
		url: `/users/login?token=${tk}`,
		method: 'post',
	});
}

export function signOut() {
  return request({
    // url: '/auth/logout',
    url: '/users/logout',
    method: 'post',
  })
}

export function getUserInfo() {
  return request({
    url: '/users/info',
    method: 'get',
  })
}

// export function refreshToken(data) {
//   return request({
//     url: '/authc/refresh',
//     method: 'post',
//     data,
//   })
// }

// # 验证码
export function getCaptcha() {
	return request({
		url: '/captcha',
		method: 'get',
	});
}

/**
 * 验证信息
 * @param data { challenge: string, response: string }
 */
export function verifyCaptcha (data: Object) {
  return request({
		url: '/captcha/verify',
		method: 'post',
    data,
	});
}

// export function getPublicKey() {
//   return request({
//     url: '/encrypt/public-key',
//     method: 'get',
//   })
// }

// # 角色权限
export function getRoles() {
  return request({
    // url: `/authz/roles`,
    url: '/authz/roles/permissions',
    method: 'get',
  })
}

export function getPermissions() {
  return request({
    url: `/authz/permissions`,
    method: 'get',
  })
}

export function getRolePermissions(roleId: number) {
  return request({
    url: `/authz/roles/${roleId}/permissions`,
    method: 'get',
  })
}

export function assignRolePermission(data: { roleId: number, permNames: string[] }) {
  return request({
    url: `/authz/roles/${data.roleId}/permissions/assign`,
    method: 'patch',
    data: data.permNames,
  })
}

export function unassignRolePermission(data: { roleId: number, permNames: string[] }) {
  return request({
    url: `/authz/roles/${data.roleId}/permissions/unassign`,
    method: 'patch',
    data: data.permNames,
  })
}
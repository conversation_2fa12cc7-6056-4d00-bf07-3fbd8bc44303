import type { UploadFile } from 'element-plus'

export interface Query {
  id?: number | undefined;
  name?: string | undefined;
}

export interface HistoryImage {
  name: string;
  url: string;
  imageId: string;
}

export interface Detail {
  key: string;
  value: string;
}

export interface Species {
  id: number;
  name: string;
  latinName: string;
  className: string;
  order: string;
  family: string;
  genus: string;
  protectLevel: string;
  describe: string;
  imageList: UploadFile | HistoryImage[];
  detail: string | Detail[];
  loading?: boolean;
  imageUrl: string;
  specie: string;
  [key: string]: any;
}

export interface Animal {
  name: string;
  alias: string;
  imageUrl: string;
  residencyType: number;
  residencyTypeText: string;
  tagNames: string[];
  tags: number[];
}


export interface FormFields {
  id: number;
  name: string;
  latinName: string;
  className: string;
  order: string;
  family: string;
  genus: string;
  protectLevel: string;
  describe: string;
  imageList: UploadFile | HistoryImage[];
  detail: string | Detail[];
}

export interface Tags {
  visible: boolean;
  label: string;
  prop: string;
  required: boolean;
  type: string;
  colSpan: number;
  tagOptions?: {
    label: string;
    value: number;
  }[];
  radioOptions?: {
    label: string;
    value: string | number;
  }[];
}

export interface RuleObj {
  [key: string]: {
    required: boolean;
    message: string;
    trigger: string;
  };
}

export interface SpeciesKey {
  key: string;
  label: string;
  value: number;
  childs?: SpeciesKey[];
}
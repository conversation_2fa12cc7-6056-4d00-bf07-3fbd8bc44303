import request from '/@/utils/request';
import axios, { CancelTokenSource } from 'axios';
/**
 * @method deviceCountStat 监测设备数
 * @method monitorCountStat 监测任务数
 * @method speciesStat 物种总数统计
 * @method getProtectionLevel 保护等级统计
 */

export function deviceCountStat() {
  return request({
    url: '/devices/count',
    method: 'get',
  });
}

export function monitorCountStat() {
  return request({
    url: '/monitors/count',
    method: 'get',
  });
}


// type 0植物 1动物 2鸟 3昆虫
const urls = [
  'directory-plants',
  'directory-animals',
  'directory-birds',
  'directory-insects'
]

export function speciesStat() {
  return request({
    url: '/directory-plants/count-total',
    method: 'get',
  });
}

export function getProtectionLevel(type: number) {
  return request({
    url: `/${urls[type]}/count-protect`,
    method: 'get',
  });
}

/**
 * 获取巡检线路
 * @returns 
 */
export function getLines(robotIds?: number) {
  return request({
    url: `/robots/patrol_lines`,
    method: 'get',
    params: {
      robotIds
    }
  });
}
/**
 * 获取机器人列表
 * @returns 
 */
export function getRobots() {
  return request({
    url: `/robots`,
    method: 'get',
  });
}
/**
 * 获取机器人列表（携带状态信息）
 * @returns 
 */
export function getRobotsStatus() {
  return request({
    url: `/robots/status`,
    method: 'get',
  });
}
/**
 * 获取机器人实时位置
 * @returns 
 */
export function getRobotsLocation(robotIds: number[]) {
  return request({
    url: `/robots/location`,
    method: 'get',
    params: {
      robotIds,
    }
  });
}

/**
 * 获取最近直播数据
 * @returns 
 */
export function getLive() {
  return request({
    url: `/lives/live`,
    method: 'get',
  });
}

type Vedio = {
  liveId: string,
  channelId: string
}

/**
 * 获取音、视频流地址
 * @returns 
 */
export function getVideoPlay(params: Vedio) {
  return request({
    url: `/lives/${params.liveId}/video_play?channelId=${params.channelId}`,
    method: 'get',
  });
}

let sources: CancelTokenSource[] = []
/**
 * 监测事件
 * @returns 
 */
export function getMonitorEventFiles(params: EmptyObjectType) {
  const source = axios.CancelToken.source();
  sources.push(source)
  return request({
    url: `/monitor-event-files/poly`,
    method: 'get',
    cancelToken: source.token,
    params,
  });
}

export function cancelRequest() {
  // console.log('cancelRequest', sources);
  sources.forEach((source) => {
    source.cancel('取消请求')
  })
  sources = []
}

export function getMonitorEventTypes() {
  return request({
    url: `/monitor-events/event_types`,
    method: 'get',
  });
}
export function getMonitorEventStat(params: { type: StatType }) {
  return request({
    url: `/monitor-event-files/statistic/overview`,
    method: 'get',
    params,
  });
}

/**
 * 根据事件id获取事件详情
 * @param eventId 事件id
 * @returns 
 */
export function getRobotsEventById(eventId: string) {
  return request({
    url: `/monitor-event-files/${eventId}`,
    method: 'get',
  });
}



/**
 * 趋势统计（折线）
 * @param params type null或0 为当天，1为近7天周 2为近30天
 * @returns 
 */
export function getMonitorEventStatTrend(params: { type: StatType }) {
  return request({
    url: `/monitor-event-files/statistic/trend`,
    method: 'get',
    params,
  });
}


type EventParams = {
  page?: number,
  size?: number,
  deviceType?: number
  taskId?: string
  eventTypes?: number[],
}
/**
 * 
 * @param params 
 * @returns 
 */
export function getMonitorEvent(params: EventParams) {
  return request({
    url: `monitor-event-files/events`,
    method: 'get',
    params,
  });
}




/**
 * 获取直播统计
 * @returns 
 */
export function getLivesStatistic() {
  return request({
    url: `lives/statistic`,
    method: 'get',
  });
}



/**
 * 上报直播次数
 * @param liveId 直播id
 * @returns 
 */
export function reportForViewing(liveId: number) {
  return request({
    url: `lives/${liveId}/play`,
    method: 'post',
  });
}



/**
 * 获取视频流地址
 * @param liveId 直播id
 * @param channelNos 视频流的类型（高清， 前后左右）
 * @returns 
 */
export function getLiveUrl(param: { liveId: number | string, channelNos?: string[] | number[] }) {

  let baseURL = `lives/${param.liveId}/play`;
  // 如果channelNos存在并且不是空数组，则添加到查询参数对象中
  if (param.channelNos && param.channelNos.length) {
    // 将数组转换为查询字符串参数，例如 channelNos=1&channelNos=7
    const channelNosString = param.channelNos.map(no => `channelNos=${encodeURIComponent(no)}`).join('&');
    baseURL += `?${channelNosString}`;
  }
  return request({
    url: baseURL,
    method: 'get',
  });
}

/**
 * 机器人的实时视频流
 * @param deviceId 机器人设备id
 * @param channelNos 视频流的类型（高清， 前后左右）
 */
export function getrobotMonitorPlay({ deviceId, channelNos }: { deviceId: number, channelNos: number[] }) {
  let baseURL = `robots/${deviceId}/play`;
  // 如果channelNos存在并且不是空数组，则添加到查询参数对象中
  if (channelNos && channelNos.length) {
    // 将数组转换为查询字符串参数，例如 channelNos=1&channelNos=7
    const channelNosString = channelNos.map(no => `channelNos=${encodeURIComponent(no)}`).join('&');
    baseURL += `?${channelNosString}`;
  }
  return request({
    url: baseURL,
    method: 'get',
  });
}

/**
 * 获取任务信息
 * @param deviceId 机器人设备id
 * @param taskId 任务id
 * @returns 
 */
export function getLastTask(deviceId: number, taskId?: string | number) {
  return request({
    url: `/robot-patrol-tasks/${deviceId}/task_statistics`,
    method: 'get',
    params: {
      taskId: taskId
    }
  });
}



/**
 * 获取机器人任务列表
 * @param params
 * @returns 
 */
export function getPatrolTasks(params: any) {
  return request({
    url: `/robot-patrol-tasks`,
    method: 'get',
    params
  });
}

type PatrolS = {
  page: number,
  size: number,
  deviceId: number | string,
  beginDate?: string,
  planId?: number,
}

/**
 * 机器人页面-巡检计划列表
 * @param params
 * @returns 
 */
export function getPatrolPlans(params: PatrolS) {
  return request({
    url: `/robot-patrol-plans`,
    method: 'get',
    params
  });
}
/**
 * 机器人-巡检计划-列表页面
 * @param params
 * @returns 
 */
export function getPatrolTasksStat(params: PatrolS) {
  return request({
    url: `/robot-patrol-tasks/statistics`,
    method: 'get',
    params
  });
}

/**
 * 机器人-任务列表
 * @param params
 * @returns 
 */
export function getTasksPoints(params: any) {
  return request({
    url: `/robot-patrol-tasks/points`,
    method: 'get',
    params
  });
}

/**
 * 机器人-任务列表
 * @param robotIds: []
 * @returns 
 */
export function getRobotMaps(params: { robotIds?: number[] }) {

  return request({
    url: `robot-maps`,
    method: 'get',
    params
  });
}



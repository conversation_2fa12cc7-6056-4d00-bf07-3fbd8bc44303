<template>
	<div class="stat-container fullScreen">
		<img class="fanhui" src="/src/assets/live/fanhui.png" alt="返回" @click="goBack" />
		<div class="title">
			<img src="/src/assets/fullScreen/title-name.png" alt="" draggable="false" />
		</div>
		<div class="mian">
			<div class="tab">
				<div
					v-for="item in tab"
					:key="item.groupEventType"
					class="tab-item"
					@click="handleTabClick(item.groupEventType)"
				>
					<img
						class="tab-item-img"
						:width="item.width"
						:src="activeTab === item.groupEventType ? item.activePicture : item.defaultPicture"
						alt=""
					/>
					<div
						class="a-line"
						:style="{ width: item.width }"
						:class="{ 'a-line-bg': activeTab === item.groupEventType }"
					></div>
				</div>
			</div>
			<div class="content">
				<component :is="activeCom"></component>
			</div>
			<!-- <Echarts class="echarts-item" ref="durationRef" id="duration-statistics" /> -->
			<!-- <Echarts class="echarts-item" ref="mileagRef" id="mileage-statistics" /> -->
		</div>
		<div class="footer">
			<img src="/src/assets/fullScreen/footer-bg.png" alt="底部" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, shallowReactive, onMounted, defineAsyncComponent, computed } from 'vue';
import { useRouter } from 'vue-router';
import { NextLoading } from '/@/utils/loading';
// import * as echarts from 'echarts';
// import Echarts from '/@/components/echarts/echarts.vue';
import other from '/@/utils/other';

const Overview = defineAsyncComponent(() => import('./overview.vue'));
const Robot = defineAsyncComponent(() => import('./robot.vue'));
const Task = defineAsyncComponent(() => import('./task.vue'));
const TaskReport = defineAsyncComponent(() => import('./taskReport.vue'));
const Event = defineAsyncComponent(() => import('./event/index.vue'));
const router = useRouter();

const activeTab = ref<number>(-2);
const tab = shallowReactive([
	{
		defaultPicture: other.getStaticImag('fullScreen/robotStat/shujugailan.png'),
		activePicture: other.getStaticImag('fullScreen/robotStat/a-shujugailan.png'),
		groupEventType: -2,
		width: 67,
		component: Overview,
	},
	{
		defaultPicture: other.getStaticImag('fullScreen/robotStat/jiqiren.png'),
		activePicture: other.getStaticImag('fullScreen/robotStat/a-jiqiren.png'),
		groupEventType: 2,
		width: 50,
		component: Robot,
	},
	{
		defaultPicture: other.getStaticImag('fullScreen/robotStat/renwu.png'),
		activePicture: other.getStaticImag('fullScreen/robotStat/a-renwu.png'),
		groupEventType: 1,
		width: 34,
		component: Task,
	},
	{
		defaultPicture: other.getStaticImag('fullScreen/robotStat/baogao.png'),
		activePicture: other.getStaticImag('fullScreen/robotStat/a-baogao.png'),
		groupEventType: -1,
		width: 34,
		component: TaskReport,
	},
	{
		defaultPicture: other.getStaticImag('fullScreen/robotStat/shijian.png'),
		activePicture: other.getStaticImag('fullScreen/robotStat/a-shijian.png'),
		groupEventType: 4,
		width: 34,
		component: Event,
	},
]);
const activeCom = computed(() => {
	const currentTab = tab.find((item) => item.groupEventType === activeTab.value);
	return currentTab?.component;
});
const handleTabClick = (groupEventType: number) => {
	activeTab.value = groupEventType;
};

const goBack = () => {
	router.push('/full-screen');
};
onMounted(async () => {
	NextLoading.done();
});
</script>

<style lang="scss">
.tab {
	width: 100%;
	height: vh(23);
	margin-bottom: vh(25);
	display: flex;

	.tab-item {
		margin-right: vh(50);
		cursor: pointer;
		display: flex;
		flex-direction: column;
		align-items: center;
		.tab-item-img {
			height: vh(23);
			object-fit: contain;
		}
		.a-line {
			width: 100%;
			height: vh(4);
			background-repeat: no-repeat;
			background-size: 100% 100%;
		}
		.a-line-bg {
			background-image: url('/src/assets/eventDetails/a-line.png');
		}
	}
}
</style>
<style lang="scss" scoped>
.bg-lg {
	background-image: linear-gradient(to bottom, #186047, #41a97f);
}
.stat-container {
	position: relative;
	height: 100%;
	background-color: #000000;
	color: rgba(255, 255, 255, 1);
	.mian {
		width: 100%;
		height: 100%;
		padding: vh(112) vw(39) vh(59);
	}
	.content {
		width: 100%;
		height: calc(100% - vh(23) - vh(15));
		overflow-x: hidden;
		overflow-y: auto;
		&::-webkit-scrollbar {
			width: 0 !important;
		}
	}

	&:deep(.el-loading-mask) {
		background-color: rgba(0, 0, 0, 0.7);
		.path {
			stroke: #68cfad;
		}
	}
	.text-loding {
		text-align: center;
		font-size: 14px;
		color: #bbb;
	}
	.fanhui {
		width: 84px;
		height: 35px;
		position: absolute;
		top: vh(45);
		left: vw(40);
		z-index: 10;
		object-fit: cover;
		cursor: pointer;
	}
	.title {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: vh(80);
		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.footer {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: vh(187);
		pointer-events: none;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;

		img {
			display: block;
			width: 100%;
			height: vh(33);
			object-fit: cover;
		}
	}
}

:deep(.el-popper) {
	.el-icon {
		color: #fff;
	}
	.el-date-picker {
		--el-datepicker-active-color: #68cfad;
		--el-datepicker-hover-text-color: #68cfad;
	}

	.el-date-picker__header-label {
		color: #fff;
	}

	.el-date-table th {
		color: #fff;
		border-bottom: solid 1px #606266;
	}
	.el-date-table td.next-month,
	.el-date-table td.prev-month {
		color: var(--el-text-color-regular);
	}

	.el-picker-panel {
		color: #fff;
	}

	.el-date-table td.today .el-date-table-cell__text {
		color: #68cfad;
		font-weight: 700;
	}

	.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
		color: #fff !important;
	}
}
</style>

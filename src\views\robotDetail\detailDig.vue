<template>
	<div class="masking" v-if="isVisible">
		<div class="event-details-container">
			<span class="colse" @click="handleClose"></span>
			<div class="main">
				<div class="task-title">
					<span class="task-name">{{ patrolPlanName }}</span>
					<span>开始时间：{{ state.tableData.filter.startTime }}</span>
					<span>结束时间：{{ state.tableData.filter.endTime }}</span>
				</div>
				<div class="search">
					<span class="search-item">
						事件分类：
						<el-select
							v-model="state.tableData.filter.groupEventType"
							placeholder="点击选择事件分类"
							clearable
							:teleported="false"
							@change="handleGroupChange"
							@clear="handleGroupChange"
						>
							<el-option
								v-for="item in state.groupEventTypeOptions"
								:key="item.id"
								:label="item.name"
								:value="item.eventType"
							>
							</el-option> </el-select
					></span>
					<span class="search-item">
						事件类型：
						<el-select
							v-model="state.tableData.filter.eventTypes"
							placeholder="点击选择事件类型："
							multiple
							collapse-tags
							clearable
							:teleported="false"
						>
							<el-option
								v-for="item in state.METypeOptions"
								:key="item.id"
								:label="item.name"
								:value="item.eventType"
							>
							</el-option> </el-select
					></span>
					<span class="button-round" @click="onRefresh(true, false)">搜索</span>
					<span class="button-round" style="margin-left: 30px" @click="onRefresh(true, true)"
						>重置</span
					>
				</div>
				<div class="table-box" v-loading="state.tableData.loading">
					<template v-if="state.tableData.data.length > 0">
						<div
							v-for="(item, $index) in state.tableData.data"
							:key="item.id"
							class="atlas-item"
							@click="onClickAtlasItem(item, $index)"
						>
							<!-- <el-checkbox class="checkbox" v-model="item.checked" @click.stop /> -->
							<div class="atlas-type">{{ item.parentEventTypeName }}</div>
							<div class="thumb-rec">
								<el-image
									class="thumbnail"
									:src="item.pictureUrl || item.oriPictureThumbnailUrl"
									:preview-src-list="perviewPicList"
									:initial-index="$index"
									fit="contain"
									lazy
									@show="handleImageShow(item)"
									@close="handleImageClose"
									@switch="handleImageSwitch"
								>
									<template #placeholder>
										<div class="image-placeholder">
											<el-icon class="is-loading">
												<ele-Loading />
											</el-icon>
										</div>
									</template>
									<template #error>
										<div class="load-error">
											<img
												class="small-img-error"
												src="/src/assets/fullScreen/small-load-error.png"
												title="加载失败"
												alt=""
											/>
										</div>
									</template>
								</el-image>
							</div>
							<div class="other-content">
								<span
									v-if="item?.monitorEventDetails && item.monitorEventDetails.length"
									v-for="res in item.monitorEventDetails"
									:key="res.recResult"
								>
									{{ item.pointName }}
									{{ res.recResult }}
									<template v-if="item.eventType < 2">
										{{ res.recResultCnt && `（${res.recResultCnt}）` }}
									</template>
								</span>
								<span>{{ extractTimePart(item.recTime, 'MM-DD HH:mm:ss') }}</span>
							</div>
						</div>
						<el-pagination
							class="el-pagination_largeScreen mt10"
							v-model:current-page="state.tableData.pageParams.page"
							:page-sizes="[8, 16, 32]"
							background
							v-model:page-size="state.tableData.pageParams.size"
							layout="total, sizes, prev, pager, next, jumper"
							:total="state.tableData.total"
							:teleported="false"
							@size-change="onHandleSizeChange"
							@current-change="onHandleCurrentChange"
						>
						</el-pagination>
					</template>
					<div v-else class="text-loding">暂无数据</div>
				</div>
			</div>
		</div>
		<div v-if="dragData.show" v-drag class="viewer-remark">
			{{ dragData.remark }}
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, onMounted, onUnmounted, computed, nextTick } from 'vue';
import { extractTimePart } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';
import { useRouter, useRoute } from 'vue-router';
import { getMonitorEvent } from '/@/api/home';
import { getMonitorEventMenuTypes, getMonitorEventTypes } from '/@/api/monitorEvents';

const props = defineProps({
	taskId: { type: String, required: true },
	robotSn: { type: String, required: true },
});

const isVisible = ref(false);
const detailDigRef = ref();
const emits = defineEmits(['currentIndex', 'updateStat', 'location', 'filterMarker']);

const perviewPicList = computed(() => {
	return state.tableData.data.map((item) => item.pictureUrl || item.oriPictureUrl);
});

const perviewPicIndex = ref(0);
const router = useRouter();
const route = useRoute();

const state = reactive<ViewBaseState<MonitorEventRow>>({
	METypeOptions: [],
	groupEventTypeOptions: [],
	patrolPlanName: '',
	tableData: {
		filter: {
			deviceType: 4,
			eventTypes: [], // eventType：0鸟类 1动物
			num: props.robotSn, // 物种名称模糊匹配
			groupEventType: null,
			// speciesName: '', // 物种名称全匹配
			noResult: 0,
			startTime: '',
			endTime: '',
			sort: 'recTime,desc',
		},
		data: [],
		loading: false,
		pageParams: {
			page: 1,
			size: 8,
		},
		total: 0,
	},
	selectAll: false,
});

// tab 切换
const activeTab = ref<number | null>(null);

const handleClose = () => {
	isVisible.value = false;
	state.tableData.filter.name = '';
	state.tableData.filter.eventTypes = [];
	state.tableData.filter.noResult = 0;
	state.tableData.filter.startTime = '';
	state.tableData.filter.endTime = '';
	state.tableData.pageParams.page = 1;
	state.tableData.pageParams.size = 8;
	activeTab.value = null;
};

// 全选
const onSelectAll = () => {
	state.selectAll = !state.selectAll;
	state.tableData.data = state.tableData.data.map((item) => ({
		...item,
		checked: state.selectAll,
	}));
};

const onClickAtlasItem = (data: MonitorEventRow, $index: number) => {
	perviewPicIndex.value = $index;
	console.log('111111111111111');
	detailDigRef.value?.onOpen($index);
};

type Params = {
	startTime: string;
	endTime: string;
	taskId: string;
	num: string;
	patrolPlanName: string;
};

const patrolPlanName = ref('');
const handleOpen = (params: Params) => {
	console.log('handleOpen-params', params);
	patrolPlanName.value = params.patrolPlanName;
	state.tableData.filter = { ...state.tableData.filter, ...params };
	isVisible.value = true;
	getTableData();
};

// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	state.tableData.data = [];
	state.tableData.total = 0;

	const query = {
		page: state.tableData.pageParams.page - 1,
		size: state.tableData.pageParams.size,
		groupEventType: activeTab.value,
		...state.tableData.filter,
	};
	console.log('query', query);
	const { payload } = await getMonitorEvent(query);
	state.tableData.data = payload.content;
	state.tableData.total = payload.totalElements;

	state.selectAll = false;
	state.tableData.loading = false;
};

const handleGroupChange = () => {
	state.tableData.filter.eventTypes = [];
	getMETypeOptions();
};

const getMETypeOptions = async () => {
	const query = {
		groupEventType: state.tableData.filter.groupEventType,
	};
	const { payload } = await getMonitorEventTypes(query);
	state.METypeOptions = payload;
};

// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getTableData();
};
const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getTableData();
};
const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetPage) state.tableData.pageParams.page = 1;
	if (resetFilter) {
		state.tableData.filter.name = '';
		state.tableData.filter.deviceType = 4;
		state.tableData.filter.eventTypes = [];
		state.tableData.filter.noResult = 0;
		state.tableData.filter.groupEventType = [];
	}
	getTableData();
};

const dragData = ref({
	remark: '',
	show: false,
});

const handleImageShow = (item: any) => {
	if (item.groupEventType === 1) {
		dragData.value.show = true;
		dragData.value.remark =
			(item.monitorEventDetails?.length && item.monitorEventDetails[0]?.remark) || '';
	}
};
const handleImageClose = () => {
	dragData.value.show = false;
};
const handleImageSwitch = (index: number) => {
	dragData.value.show = false;
	const item: any = state.tableData.data[index];
	if (item.groupEventType === 1) {
		dragData.value.remark =
			(item.monitorEventDetails?.length && item.monitorEventDetails[0]?.remark) || '';

		nextTick(() => {
			dragData.value.show = true;
		});
	}
};

onBeforeMount(async () => {
	const { payload } = await getMonitorEventMenuTypes();
	state.groupEventTypeOptions = payload;
	payload.forEach((item: any) => {});
	getMETypeOptions();
	console.log('11111111111111');
});

onMounted(() => {
	NextLoading.done();
});

onUnmounted(() => {});

defineExpose({
	handleOpen,
});
</script>

<style lang="scss" scoped>
/* @import url(); 引入css类 */

.bg-lg {
	background-image: linear-gradient(to bottom, #186047, #41a97f);
}

.masking {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 505;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.3);
	display: flex;
	justify-content: center;
	align-items: center;
}
.event-details-container {
	position: relative;
	width: 80%;
	height: 90%;
	z-index: 505;
	background-image: url('/src/assets/eventDetails/dig/dig-bg.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;

	.colse {
		position: absolute;
		top: 24px;
		right: 24px;
		width: 18px;
		height: 18px;
		z-index: 999;
		background-image: url('/src/assets/eventDetails/dig/close.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		cursor: pointer;
	}

	.fanhui {
		width: 84px;
		height: 35px;
		object-fit: cover;
		position: absolute;
		top: 45px;
		left: 40px;
		z-index: 10;
		cursor: pointer;
	}
	.main {
		width: 100%;
		height: calc(100% - 43px);
		padding: 50px 39px 0px;
		.task-title {
			display: flex;
			align-items: center;
			height: 25px;
			color: rgba(255, 255, 255, 1);
			line-height: 25px;
			font-size: 16px;
			margin-bottom: 25px;
			span {
				margin-right: 20px;
			}
			.task-name {
				font-size: 18px;
			}
		}
		.tab {
			width: 100%;
			height: 23px;
			margin-bottom: 28px;
			display: flex;

			.tab-item {
				margin-right: 50px;
				cursor: pointer;
				display: flex;
				flex-direction: column;
				align-items: center;
				.tab-item-img {
					height: 23px;
					object-fit: contain;
				}
				.a-line {
					width: 100%;
					height: 4px;
					background-repeat: no-repeat;
					background-size: 100% 100%;
				}
				.a-line-bg {
					background-image: url('/src/assets/eventDetails/a-line.png');
				}
			}
		}

		.search {
			width: 100%;
			height: 34px;
			margin-bottom: 30px;
			color: #fff;
			.search-item {
				margin-right: 80px;

				.el-select {
					width: 254px;
				}
				&:deep(.el-tag) {
					@extend .bg-lg;
					color: #fff;
				}
				&:deep(.el-select-dropdown__item.selected) {
					background-color: transparent !important;
					color: rgb(104, 207, 173);
				}

				&:deep(.el-input__prefix) {
					display: none;
				}
			}

			&:deep(.el-popper.is-light .el-popper__arrow::before) {
				border: 1px solid rgba(104, 207, 173, 1) !important;
				border-bottom-color: transparent !important;
				border-right-color: transparent !important;
				background: #010b07 !important;
			}

			&:deep(.el-form-item__label) {
				color: #fff;
				font-size: 14px;
			}

			&:deep(.el-select .el-input__inner::placeholder) {
				color: #767876 !important;
				font-size: 14px;
			}

			&:deep(.el-input__wrapper) {
				border-radius: 50px !important;
				border: 1px solid rgba(104, 207, 173, 1) !important;
				background-color: transparent !important;
				box-shadow: none !important;
				padding: 0 11px !important;
			}
			&:deep(.el-input__inner) {
				--el-input-inner-height: 30px !important;
				color: rgba(255, 255, 255, 1);
			}
			&:deep(.el-select .el-input.is-focus .el-input__wrapper) {
				box-shadow: none !important;
			}
			&:deep(.el-select .el-input__inner::placeholder) {
				color: rgba(255, 255, 255, 1);
				font-size: 14px;
			}

			.el-icon :deep(svg) {
				color: rgba(255, 255, 255, 1);
			}

			&:deep(.el-select) {
				--el-select-input-focus-border-color: none !important;
			}

			.button-round {
				display: inline-block;
				width: 108px;
				height: 34px;
				border-radius: 54px;
				color: #fff;
				line-height: 34px;
				text-align: center;
				cursor: pointer;
				@extend .bg-lg;
				-webkit-user-select: none; /* Safari */
				-moz-user-select: none; /* Firefox */
				-ms-user-select: none; /* IE/Edge */
				user-select: none; /* 标准语法 */
			}
		}

		.table-box {
			height: calc(100% - 115px);
			display: flex;
			flex-wrap: wrap;
			overflow-y: auto;

			.text-loding {
				width: 100%;
				margin-top: 200px;
				text-align: center;
				font-size: 14px;
				color: #bbb;
			}

			&:deep(.el-loading-mask) {
				background-color: rgba(0, 0, 0, 0.7);
				.path {
					stroke: #68cfad;
				}
			}
			&::-webkit-scrollbar {
				width: 10px;
			}
			&::-webkit-scrollbar-thumb {
				background-color: #22d69f; /* 滑块的背景颜色 */
				border-radius: 0; /* 滑块的圆角 */
				border-left: 6px solid transparent; /* 左边透明边框，占滚动条宽度的一半 */
				// background-clip: content-box; /* 使背景色不延伸到边框 */
			}
			&::-webkit-scrollbar-track-piece {
				background-color: transparent;
			}
			.atlas-item {
				position: relative;
				width: calc((100% - 30px) / 4);
				height: 248px;
				margin-right: 10px;
				margin-bottom: 0.926vh;
				background-image: url('/src/assets/eventDetails/atlas-bg.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
				padding: 10px;

				&:nth-last-child(-n + 4) {
					margin-bottom: 0;
				}

				.thumbnail {
					width: 100%;
					height: 189px;

					.image-placeholder {
						width: 100%;
						height: 100%;
						display: flex;
						justify-content: center;
						align-items: center;
						.is-loading {
							color: #22d69f;
							font-size: 20px;
						}
					}
					:deep(.el-image__inner) {
						&:hover {
							transform: scale(1.05);
						}
					}
				}

				.checkbox {
					position: absolute;
					top: 10px;
					left: 20px;
				}

				&:deep(.el-checkbox__inner) {
					background-color: rgba(1, 11, 7, 0.3);
					border: 1px solid rgba(104, 207, 173, 1);
				}

				/* 当鼠标悬停时改变边框颜色 */
				&:deep(.el-checkbox__inner:hover) {
					border-color: #409eff;
				}

				&:deep(.el-checkbox__inner.is-checked) {
					background-color: #409eff;
					border-color: #409eff;
				}

				.atlas-type {
					position: absolute;
					top: 20px;
					right: 20px;
					width: 77px;
					height: 21px;
					line-height: 21px;
					text-align: center;
					color: #fff;
					border-radius: 12px;
					font-size: 12px;
					background-color: rgba(0, 0, 0, 0.3);
					z-index: 2;
				}

				&:nth-child(4n) {
					margin-right: 0;
				}
			}

			.other-content {
				height: 25px;
				margin-top: 10px;
				color: #fff;
				display: flex;
				justify-content: space-between;
				span {
					line-height: 25px;
					&:first-child {
						font-size: 18px;
					}
					&:last-child {
						font-size: 14px;
					}
				}
			}
		}
	}

	.title {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 80px;
		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.footer {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 187px;
		// background-image: url('/src/assets/fullScreen/footer.png');
		// background-repeat: no-repeat;
		// background-size: 100% 103%;
		pointer-events: none;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		// background-color: #fff;

		img {
			display: block;
			width: 100%;
			height: 33px;
			object-fit: cover;
		}
	}
}
</style>

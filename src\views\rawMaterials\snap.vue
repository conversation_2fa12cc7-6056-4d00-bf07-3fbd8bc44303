<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<!-- 仅红外设备 -->
				<el-form-item label="设备">
					<el-select v-model="state.tableData.filter.num" placeholder="请选择" clearable>
						<el-option
							v-for="item in deviceOptions"
							:key="item.id"
							:label="`设备编号：${item.num}，名称：${item.channelName || item.name}`"
							:value="item.num"
						/>
					</el-select>
				</el-form-item>
				<!-- <el-form-item label="是否识别">
					<el-select v-model="state.tableData.filter.noResult" placeholder="请选择" clearable>
						<el-option label="全部" value=""></el-option>
						<el-option label="未识别" value="1"></el-option>
						<el-option label="已识别" value="0"></el-option>
					</el-select>
				</el-form-item> -->
				<el-form-item label="">
					<el-date-picker
						v-model="state.tableData.filter.timePeriod"
						type="datetimerange"
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						value-format="YYYY-MM-DD HH:mm:ss"
						clearable
					/>
				</el-form-item>
			</template>
		</ViewSearch>

		<el-row class="atlas" v-loading="state.tableData.loading" :gutter="20">
			<template v-if="state.tableData.data.length > 0">
				<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" v-for="item in state.tableData.data" :key="item.id">
					<el-card class="atlas-item">
            <div class="thumbnail-container">
							<div class="main-thumb">
								<el-image
									class="thumbnail"
									:src="item.oriPictureThumbnailUrl"
									fit="cover"
									:preview-src-list="[item.oriPictureUrl]"
									lazy
								></el-image>
							</div>
							<!-- 抓拍图片素材（肯定是检测过的）识别结果取 `monitorEventDetails` -->
							<div class="thumb-recs" v-if="!item.pictureUrl">
								<div class="thumb-rec">
									<el-image
										class="thumbnail"
										:src="item.pictureUrl || item.oriPictureThumbnailUrl"
										fit="cover"
										:preview-src-list="[item.pictureUrl || item.oriPictureUrl]"
										lazy
									></el-image>
									<div class="recResult-content">
										<template v-if="!item.rawResult">
											未发现物种
										</template>
										<span v-else v-for="res in item.monitorEventDetails" :key="res.recResult">
											{{ res.recResult }}（{{ res.recResultCnt }}）
										</span>
									</div>
								</div>
							</div>
						</div>
						<div class="other-content">
							<div>{{ item.device.num }}</div>
							<div class="mt5">GPS：{{ item.device.longitude ? `${item.device.longitude},${item.device.latitude}` : '-' }}</div>
							<div class="mt5">{{ item.createTime }}</div>
							<div class="operate-btns" v-auths="['*:*:*', 'images:*:*']">
								<el-button type="primary" text title="下载" @click="onDownload(item)">
									<i class="iconfont icon-xiazai font17"></i>
								</el-button>
							</div>
              <!-- deleted：0未删除 1已删除-->
              <div v-if="item.deleted === 1" class="rb-icon">
                <el-tooltip
									effect="dark"
									content="抓拍图像已被删除"
									placement="bottom"
								>
                  <ele-Warning />
								</el-tooltip>
              </div>
						</div>
					</el-card>
				</el-col>
			</template>
			<el-empty class="flex-margin" v-else></el-empty>
		</el-row>
		<el-pagination
			class="mt10"
			v-model:current-page="state.tableData.pageParams.page"
			:page-sizes="[12, 24, 38, 48]"
			background
			v-model:page-size="state.tableData.pageParams.size"
			layout="total, sizes, prev, pager, next, jumper"
			:total="state.tableData.total"
			@size-change="onHandleSizeChange"
			@current-change="onHandleCurrentChange"
		>
		</el-pagination>
	</div>
</template>

<script setup lang="ts" name="RawMaterials">
import { computed, reactive, onMounted, defineAsyncComponent } from 'vue';
import { getSnapRawMaterials, downloadFile } from '/@/api/rawMaterials';
import { useDevices } from '/@/hooks/useDevices';

const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));

// 定义变量内容
const props = defineProps<{
  typeFilter: number; // 2监控定时抓拍、4巡护定时抓拍
}>();
const state = reactive<ViewBaseState<SnapRMRow>>({
	deviceOptions: [],
	tableData: {
		filter: {
			num: '',
			noResult: '',
			timePeriod: [],
			sort: 'createTime,desc',
		},
		data: [],
		loading: false,
		pageParams: {
			page: 1,
			size: 12,
		},
		total: 0,
	},
});
const devices = useDevices();
const deviceOptions = computed(() => {
  // 监控设备 `sourceType = 1` 和 `sourceType = 3`
  if (props.typeFilter === 2) {
    return devices.data.value.filter((item: DeviceRow) => (item.sourceType === 1 || item.sourceType === 3));
  }
  // 巡护设备 `sourceType = 4`
  if (props.typeFilter === 4) {
    return devices.data.value.filter((item: DeviceRow) => (item.sourceType === 4));
  }
	return devices.data.value;
});
// 页面加载时
onMounted(() => {
	getTableData();
});

// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	const { timePeriod } = state.tableData.filter;
	const query = {
    typeFilter: props.typeFilter,
		...state.tableData.filter,
		page: state.tableData.pageParams.page - 1,
		size: state.tableData.pageParams.size,
		startTime: timePeriod && timePeriod.length > 0 ? timePeriod[0] : null,
		endTime: timePeriod && timePeriod.length > 0 ? timePeriod[1] : null,
		timePeriod: null,
	};
  try {
    const { payload } = await getSnapRawMaterials(query);
    state.tableData.data = payload.content;
    state.tableData.total = payload.totalElements;
    state.tableData.loading = false;
  } catch (error) {
    state.tableData.data = [];
    state.tableData.total = 0;
    state.tableData.loading = false;
  }
};
const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetPage) state.tableData.pageParams.page = 1;
	if (resetFilter) {
		state.tableData.filter.num = '';
		state.tableData.filter.noResult = '';
		state.tableData.filter.timePeriod = [];
	}
	getTableData();
};
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getTableData();
	resetScroll();
};
const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getTableData();
	resetScroll();
};
// 重置滚动条
const resetScroll = () => {
	// .layout-backtop-header-fixed .el-scrollbar__wrap
	const scrollEle = document.querySelector('.layout-backtop .el-scrollbar__wrap');
	(<HTMLDivElement>scrollEle).scrollTo({
		top: 0,
		behavior: 'smooth',
	});
};
const onDownload = ({ oriPictureId, oriPictureUrl, device }: SnapRMRow) => {
	const suffix = oriPictureId.slice(oriPictureId.lastIndexOf('.'));
	downloadFile(oriPictureUrl).then((res) => {
		const url = URL.createObjectURL(new Blob([res as unknown as Blob]));
		const link = document.createElement('a');
		link.href = url;
    link.download = device.num + suffix;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
	});
};
</script>

<style scoped lang="scss">
@import '../../theme/atlas.scss';
</style>

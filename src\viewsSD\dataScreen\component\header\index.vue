<template>
	<div class="header-container">
		<!-- <div class="h-logo">
			<img src="/src/assets/sd/header/logo.png" alt="" />
		</div> -->
		<div class="h-left">
			<div>{{ currentDate }}</div>
		</div>
		<div class="h-right">
			<div class="icon">
				<el-dropdown :teleported="false" @command="onDropdownMenuClick">
					<img src="/src/assets/sd/header/hr-admin.png" alt="" />
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item v-for="item in adminMenus" :key="item.path" :command="item.path">
								<SvgIcon :name="item.meta?.icon" :size="20" />
								{{ item.meta?.title }}
							</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</div>
			<div class="icon" @click="onLogout">
				<img src="/src/assets/sd/header/hr-logout.png" alt="" />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeMount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import { useRoutesList } from '/@/stores/routesList';
import { useUserInfo } from '/@/stores/userInfo';
import { formatDate } from '/@/utils/formatTime';
import { getStaticImag } from '/@/utils/other';
import { toLindong } from '/@/api/sd/dataScreen/device';
import { IsCompanyNmg } from '/@/hooks/useConfig';

const props = defineProps<{
	activeMenu: string;
}>();

const emits = defineEmits<{
	(e: 'activeMenuChange', key: string): void;
}>();
const route = useRoute();
const router = useRouter();

// 点击菜单
const onNavMenuClick = (menu: { path: string; key: string }) => {
	router.push(menu.path);
};

// 右侧日期 和 天气
// 当前日期时间
const currentDate = ref('');
const getCurrentDate = () => {
	currentDate.value = formatDate(new Date(), 'YYYY-mm-dd HH:MM WWW');
	window.requestAnimationFrame(getCurrentDate);
};
window.requestAnimationFrame(getCurrentDate);

// 管理端菜单
const adminMenus = ref<RouteItem[]>([]);
const routesListStore = useRoutesList();
const getAdminMenus = () => {
	const filterRoutesFun = <T extends RouteItem>(arr: T[]): T[] => {
		return arr
			.filter((item: T) => !item.meta?.isHide && item.name !== 'SDDataScreen')
			.map((item: T) => {
				item = Object.assign({}, item);
				// if (item.children) item.children = filterRoutesFun(item.children);
				return item;
			});
	};
	adminMenus.value = filterRoutesFun(routesListStore.routesList);
};
const onDropdownMenuClick = (path: string) => {
	router.push(path);
};

// 退出登录
const userStore = useUserInfo();
const onLogout = () => {
	ElMessageBox({
		closeOnClickModal: false,
		closeOnPressEscape: false,
		title: '提示',
		message: '此操作将退出登录, 是否继续?',
		showCancelButton: true,
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		buttonSize: 'default',
		beforeClose: (action, instance, done) => {
			if (action === 'confirm') {
				instance.confirmButtonLoading = true;
				instance.confirmButtonText = '退出中';
				userStore
					.userLogout()
					.then(() => {
						instance.confirmButtonLoading = false;
						done();
					})
					.catch(() => {
						instance.confirmButtonLoading = false;
					});
			} else {
				done();
			}
		},
	});
};

watch(
	() => route.path,
	() => {
		if (route.query?.pagePath) {
			// 最近发现
			emits('activeMenuChange', route.query.pagePath as string);
		}
	},
	{ immediate: true }
);

onBeforeMount(() => {
	getAdminMenus();
});
</script>

<style lang="scss" scoped>
.header-container {
	height: 100%;
	display: flex;
	justify-content: space-between;
	overflow: visible;
	position: relative;
	z-index: 20;
	background-image: url('/src/assets/sd/header/2025-07-25_095842_994.png');
	background-size: 100% 100%;
	&::after {
		content: '';
		display: block;
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		height: vw(2);
		background: linear-gradient(to right, transparent 18%, rgba(75, 239, 124, 0.4) 28%);
	}
	.h-logo {
		width: vw(540);
		img {
			position: relative;
			top: vw(10);
			height: 100%;
			width: 100%;
		}
	}
	.h-nav-menus {
		flex: 1;
		display: flex;
		align-items: center;

		.nav-menu-item {
			width: vw(206);
			height: vw(48);
			line-height: vw(48);
			text-align: center;
			// margin-left: vw(-5);
			cursor: pointer;
			color: rgba(255, 255, 255, 0.8);
			font-size: vw(20);
			font-family: 'ALiMaMaShuHeiTi';
			img {
				// width: 100%;
				height: 100%;
				transition: transform 0.2s;
			}
			&:hover {
				img {
					transform: scale(1.03);
				}
			}
		}
	}
	.h-left {
		padding: vw(18) vw(15);
		display: flex;
		align-items: center;
		font-size: vw(14);
		font-weight: lighter;
		font-family: 'ALiMaMaShuHeiTi';
	}
	.h-right {
		padding: vw(18) vw(15);
		display: flex;
		align-items: center;
		font-size: vw(14);
		font-weight: lighter;
		font-family: 'ALiMaMaShuHeiTi';
		.icon {
			width: vw(24);
			height: vw(24);
			margin-left: vw(12);
			cursor: pointer;
			text-align: center;
			border-radius: 4px;
			img {
				width: vw(21);
				position: relative;
				top: vw(4);
			}
			.lingdong {
				width: vw(24);
			}
			&:hover {
				background-color: rgba(75, 239, 124, 0.1);
			}
		}
	}
}
</style>

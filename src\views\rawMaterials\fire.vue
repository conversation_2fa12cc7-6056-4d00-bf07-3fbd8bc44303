<template>
  <div class="layout-pd">
    <ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<el-form-item label="日期">
					<el-date-picker
						v-model="searchOptions.filter.timePeriod"
						type="date"
						value-format="YYYY-MM-DD"
						:clearable="false"
					/>
				</el-form-item>
        <el-form-item label="通道编号">
          <el-select v-model="searchOptions.filter.channelNo" placeholder="请选择">
            <el-option v-for="num in 33" :key="num" :label="`channel ${num - 1}`" :value="num - 1">
            </el-option>
          </el-select>
        </el-form-item>
			</template>
		</ViewSearch>

    <Table v-bind="tableOptions" ref="tableRef" @pageChange="onPageChange">
      <template #size="{ row }">
        {{ other.formatSizeUnit(row.size) }}
      </template>
      <template #operate="{ row }">
        <el-button type="primary" text size="small" @click="onPlay(row.name)">
          <template #icon>
            <i class="iconfont icon-zhibo font15"></i>
          </template>
          查看视频
        </el-button>
      </template>
    </Table>

    <VideoPlayerDialog ref="VideoPlayerDialogRef"></VideoPlayerDialog>
  </div>
</template>

<script setup lang="ts" name="FireMaterials">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { getFireMaterials, generatPlayUrl } from '/@/api/rawMaterials';
import { AUTHS } from '/@/directive/authDirective';
import other from '/@/utils/other';
import { formatDate } from '/@/utils/formatTime'

// 引入异步组件
const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));
const VideoPlayerDialog = defineAsyncComponent(() => import('./components/videoPlayerDialog.vue'));

const tableRef = ref<InstanceType<typeof Table>>();
const searchOptions = reactive({
  filter: {
    timePeriod: formatDate(new Date(), 'YYYY-mm-dd'), // 必填，默认选中当天
    channelNo: 0, // 通道号，必填
  },
});
const tableOptions: GlobalTableOptions<EmailRow> = reactive({
  data: [],
  header: [
    {
      title: '开始时间',
      key: 'startTime',
      isCheck: true,
    },
    {
      title: '结束时间',
      key: 'endTime',
      isCheck: true,
    },
    {
      title: '文件名',
      key: 'name',
      isCheck: true,
    },
    {
      title: '文件大小',
      key: 'size',
      isCheck: true,
    },
  ],
  config: {
    loading: true,
    isSelection: false,
    isSerialNo: true,
    isOperate: AUTHS(['*:*:*', 'images:*:*']),
    operateWidth: 150, // 操作列宽
    total: 0, // 总条数
  },
  pageParams: {
    page: 1,
    size: 12,
  },
});

onMounted(() => {
  initTableData();
});

const initTableData = async () => {
  tableOptions.config.loading = true;
  const { timePeriod, channelNo } = searchOptions.filter;
  const query = {
    pageNo: tableOptions.pageParams.page - 1,
		pageSize: tableOptions.pageParams.size,
    channelNo: channelNo,
    startDateTime: timePeriod ? `${timePeriod} 00:00:00` : '',
    endDateTime: timePeriod ? `${timePeriod} 23:59:59` : '',
  };
  try {
    const { payload } = await getFireMaterials(query)
    tableOptions.config.loading = false;
    tableOptions.data = payload.content;
    tableOptions.config.total = payload.totalElements;
  } catch (e) {
    tableOptions.config.loading = false;
    tableOptions.data = [];
    tableOptions.config.total = 0;
  }
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
  if (resetFilter) {
    searchOptions.filter.timePeriod = formatDate(new Date(), 'YYYY-mm-dd');
    searchOptions.filter.channelNo = 0;
  }
  if (resetPage) {
    tableRef.value?.pageReset();
  } else {
    initTableData();
  }
};
const onPageChange = async (page: { pageNum: number; pageSize: number; }) => {
  if (tableOptions.pageParams) {
    tableOptions.pageParams.page = page.pageNum;
    tableOptions.pageParams.size = page.pageSize;
  }
  initTableData();
};

// 查看视频
const VideoPlayerDialogRef = ref<InstanceType<typeof VideoPlayerDialog>>();
const onPlay = (fileName: string) => {
  const data = {
    type: 'flv',
    playUrl: generatPlayUrl(fileName),
  };
	VideoPlayerDialogRef.value?.openDialog(data);
};
</script>

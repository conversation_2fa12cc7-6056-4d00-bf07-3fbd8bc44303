import { defineStore } from 'pinia';
import { getLive, getMonitorEventFiles, reportForViewing, getMonitorEventTypes, getRobotsStatus, cancelRequest } from '/@/api/home';
import { gps84_To_gcj02, bd09_To_gcj02, } from '@vuemap/vue-amap';
import other from '/@/utils/other';
import { convertCoordinates } from '../utils/coordinates';


let setEventTypesOnce = false


interface EventType {
  id: number;
  eventType: number;
  eventLevel: number;
  name: string;
  messageFormat: string;
}

interface Live {
  id: string;
  name: string;
  robotChannels: {
    id: string;
    name: string;
    channelId: number;
  }[];
}
interface EventTypeOptions {
  label: string;
  value: number;
  icon: string
}


interface SetEventType {
  poly: boolean,
  discrepancy: boolean,
  limit?: number
}

/**
 * 直播信息
 * @methods setLiveId 获取最近的直播数据
 */
export const liveInfo = defineStore('liveInfo', {
  state: () => ({
    liveId: '',
    deviceId: 0,
    robotIds: -1,
    robotsInfo: new Map() as Map<number, RobotInfo>, // 所有机器人列表
    channelNo: [
      { name: '高清', channelId: 2 },
      { name: '前', channelId: 1 },
      { name: '后', channelId: 4 },
      { name: '左', channelId: 5 },
      { name: '右', channelId: 6 },
      { name: '红外', channelId: 3 },
    ],
    eventTypes: {
      0: {
        recResult: '野生动物监测',
        markerUrl: other.getStaticImag('fullScreen/mapIcon/dongwu.png')
      },
      1: {
        recResult: "鸟类监测",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/niaolei.png')
      },
      3: {
        recResult: "病虫害监测",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/bingchonghai.png')
      },
      4: {
        recResult: "路灯故障检测",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/ludeng.png')
      },
      5: {
        recResult: "烟火告警",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/yanhuo.png')
      },
      6: {
        recResult: "踩踏草坪告警",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/jianta.png')
      },
      7: {
        recResult: "吊床帐篷告警",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/diaochuang.png')
      },
      8: {
        recResult: "非机动车告警",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/feijidongche.png')
      },
      9: {
        recResult: "宠物入园告警",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/chongwu.png')
      },
      10: {
        recResult: "拉横幅告警",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/lahengfu.png')
      },
      11: {
        recResult: "吸烟告警",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/xiyan.png')
      },
      13: {
        recResult: "乱扔垃圾告警",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/laji.png')
      },
      15: {
        recResult: "人员聚集告警",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/renyuanjuji.png')
      },
      16: {
        recResult: "占用长椅告警",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/changyi.png')
      },
      17: {
        recResult: "垃圾桶满溢告警",
        markerUrl: other.getStaticImag('fullScreen/mapIcon/lajimanyi.png')
      }
    } as any,
    groupEventTypes: [] as MonitorEventType[],
    eventNewEvents: [] as MarkerInfo[], // 右侧感知事件 发现新的事件
    mapNewEvents: [] as MarkerInfo[], // 地图 发现新的marker点
    eventList: [] as MarkerInfo[], // 右侧感知事件列表
    mapEventList: [] as MarkerInfo[], // 地图的marker集合
    liveEventList: [] as MarkerInfo[], // 实况页面的事实事件
    eventTypeOptions: [] as EventTypeOptions[], // 全部的事件类型
    legendFilterList: [] as number[], // 图例筛选事件，过滤右侧感知事件的
  }),
  actions: {
    async setLiveId(liveId?: string) {
      return new Promise((resolve, reject) => {
        getLive().then(({ payload }) => {
          if (payload?.length > 0) {
            // 存在liveId，播放该直播；否则播放列表第一条直播
            const currentLive = liveId ? payload.find((item: Live) => item.id === liveId) : payload[0];
            if (currentLive && currentLive.id) {
              this.liveId = payload[0].id;
              reportForViewing(payload[0].id);
              // 返回要播放的直播通道列表
              resolve(this.channelNo);
            }
          } else {
            reject();
          }
        });
      });
    },
    /**
     * 
     * @param poly true 去重 用于地图的marker   false: 不去重 用于安全事件列表
     * @param discrepancy 查看数据差异，取到最新的数据更新地图marker
     * @param limit 指定取多少条数据
     * @returns 
     */

    async setEventList(poly = false, discrepancy = false, limit?: number) {
      await this.setEventTypes();
      return new Promise((resolve, reject) => {

        let eventTypes: number[] | null = this.legendFilterList
        const params = {
          poly,
          eventTypes,
          overAll: poly, // 是否全局去重，true代表忽略事件类型进行全局去重（即地图上同坐标的事件只显示一个）
          deviceType: 4, // 设备类型：1萤石云设备 2红外设备 3监控设备 4巡护设备，这里只查询机器人（巡护）设备
          limit: limit ? limit : poly ? null : 100, // 感知事件取前100条
        }
        getMonitorEventFiles(params).then(({ payload }) => {

          if (discrepancy) {
            const oldData: any[] = poly ? this.mapEventList : this.eventList;
            const newData: any[] = payload;
            const idsInOldData = {} as { [key: string]: boolean };
            oldData.forEach(item => {
              idsInOldData[item.id] = true;
            });

            // 找出oldData中不存在的对象
            const notInA = newData.filter((item) => !idsInOldData.hasOwnProperty(item.id))
            const newEvents = notInA.map((element, index) => {
              return this.notInAMapped(element, 12 + oldData.length + index, true)
            });

            if (newEvents.length) {
              if (poly) {
                console.log('地图新事件', newEvents);
                this.mapNewEvents = [...newEvents]
                this.mapEventList = [...newEvents, ...this.mapEventList]
              } else {
                // 右侧感知检测到新事件
                console.log('右侧新事件', newEvents);
                this.eventNewEvents = [...newEvents]
                this.eventList = [...newEvents, ...this.eventList]
              }
            }

            return resolve(payload);
          }
          let temp: MarkerInfo[] = []
          payload.forEach((item: any, index: number) => {
            temp.push(this.notInAMapped(item, index))
          });

          if (typeof limit === 'number') {
            this.liveEventList = temp
          } else if (poly) {
            this.mapEventList = temp
          } else {
            this.eventList = temp
          }


          resolve(payload);
        })
          .catch((error) => {
            console.log('getMonitorEventFiles-catch-error', error);
            reject()
          });


      });
    },
    notInAMapped(item: any, zIndex: number, animation = false): MarkerInfo {
      const position = convertCoordinates(item.longitude, item.latitude, item.coordinateType)
      return {
        id: item.id,
        eventType: item.eventType,
        groupEventType: item.groupEventType,
        eventLevel: item.eventLevel,
        position,
        markerUrl: this.eventTypes[item.eventType].markerUrl,
        recResult: item.monitorEventDetails?.length && item.monitorEventDetails[0]?.recResult || '',
        remark: item.monitorEventDetails?.length && item.monitorEventDetails[0]?.remark || '',
        recTime: item.recTime,
        parentEventTypeName: item.parentEventTypeName,
        pointName: item.pointName,
        // 人员聚集与占用长椅不作标注，所以不存在标注图pictureUrl，取原图缩略图展示oriPictureThumbnailUrl
        picture: item.pictureUrl || item.oriPictureThumbnailUrl,
        animation,
        zIndex,
      }
    },
    // 获取感知事件类型
    async setEventTypes() {
      if (setEventTypesOnce) return //只执行一次
      setEventTypesOnce = true
      if (this.eventTypes[0].id === undefined) {
        const temp: EventTypeOptions[] = [];
        const { payload } = await getMonitorEventTypes();
        payload.forEach((item: EventType) => {
          if (this.eventTypes[item.eventType]) {
            this.eventTypes[item.eventType] = Object.assign(this.eventTypes[item.eventType], item);
          }
          temp.push({
            label: item.name,
            value: item.eventType,
            icon: this.eventTypes[item.eventType].markerUrl
          })
        })
        this.eventTypeOptions = temp;
        if (this.legendFilterList.length === 0) {
          this.legendFilterList = this.eventTypeOptions.map((item) => item.value);
        }

        // console.log('this.legendFilterList--length', this.legendFilterList.length);
      }
    },
    async getRobotList() {
      return new Promise((resolve) => {
        getRobotsStatus().then(({ payload }) => {
          if (payload) {

            if (!this.deviceId) {
              // 缓存机器设备id，刷新页面不会更改机器信息
              this.deviceId = payload[0].deviceId;
            }
            console.log('this.deviceId', payload[0].deviceId);
            console.log(this.deviceId);
            payload.forEach((item: any, index: number) => {
              const { robotName, deviceId, status } = item;
              // 获取机器人巡检路线
              // getLines(deviceId).then((lines) => {
              //   console.log('1111111111112222333', lines);
              // });
              const obj: RobotInfo = {
                robotName,
                deviceId,
                status,
                electricity: '',
                currentMileage: '',
                currentDuration: '',
                state: '',
                robotSn: '',
                lng: 0,
                lat: 0,
              }
              obj.deviceId = deviceId;
              obj.robotName = robotName;
              if (status) {
                const { electricity, currentMileage, online, robotSn, currentDuration, longitude, latitude } =
                  status;
                obj.state = online === 0 ? '离线' : '在线'
                obj.robotSn = robotSn
                obj.electricity = electricity + '%';
                obj.currentMileage = (currentMileage / 1000).toFixed(2) + 'km';
                obj.currentDuration = convertMinutesToHM(currentDuration)
                const { lng, lat } = bd09_To_gcj02(longitude, latitude);
                obj.lng = lng;
                obj.lat = lat;
              }
              this.robotsInfo.set(deviceId, obj);
              console.log('全局的机器人设备deviceId', index, '--', this.robotsInfo);
            })
            resolve(payload);
          }
        })
      })
    },
  },
  // persist: {
  //   enabled: true,  //开启数据缓存
  //   key: `${__NEXT_NAME__}:fullScreenStores`,//设置存储的key,默认为 scrollView,可以不指定
  //   storage: localStorage,//表示存储在localStorage，默认存储sessionStorage
  //   paths: ['deviceId', 'legendFilterList']   //指定要长久化的字段

  // }
  persist: {
    key: `${__NEXT_NAME__}:fullScreenStores`,//设置存储的key,默认为 scrollView,可以不指定
    storage: localStorage,//表示存储在localStorage，默认存储sessionStorage
    paths: ['deviceId', 'legendFilterList', 'robotsInfo'],   //指定要长久化的字段
    serializer: {
      serialize: (state) => {
        return JSON.stringify({
          deviceId: state.deviceId,
          robotsInfo: [...state.robotsInfo],
          legendFilterList: state.legendFilterList,
          groupEventTypes: state.groupEventTypes,
        });
      },
      deserialize: (state) => {
        const parsed = JSON.parse(state);
        return {
          deviceId: parsed.deviceId,
          robotsInfo: new Map(parsed.robotsInfo),
          legendFilterList: parsed.legendFilterList,
          groupEventTypes: parsed.groupEventTypes,
        };
      },
    },
  },
});


function convertMinutesToHM(minutes: number) {
  const hours = Math.floor(minutes / 60); // 获取小时数
  const remainingMinutes = minutes % 60; // 获取剩余的分钟数
  return `${hours}时${remainingMinutes}分`;
}






<template>
	<div class="live-container">
		<img
			v-if="!isMobile"
			class="fanhui"
			src="/src/assets/live/fanhui.png"
			alt="返回"
			@click="goBack"
		/>
		<div class="title">
			<img src="/src/assets/fullScreen/title-name.png" alt="" draggable="false" />
		</div>
		<div class="footer">
			<img src="/src/assets/fullScreen/footer-bg.png" alt="底部" />
		</div>
		<div class="main">
			<div class="main-top">
				<div class="main-left">
					<div :class="videoInfos.high.isIosFullScreen" class="high-video" id="highRef">
						<img class="video-item" data-ref="high" :src="videoInfos.high.switchImg" alt="" />
						<component
							:is="videoInfos.high.component"
							ref="highVideoRef"
							:channelNos="videoInfos.high.channelNos"
							:url="videoInfos.high.url"
							name="high"
							:liveId="currentPageLiveId"
							@recordingTimestamp="jessibucaOnRecordingTimestamp"
							@load="jessibucaOnLoad"
							@play="jessibucaOnPlay"
							@recordEnd="stopRecordAndSave"
							@recordStart="startRecord"
							@destroy="jessibucaOnDestroy"
							@fullScreen="onFullScreen"
						></component>
						<!-- 录制的时长 -->
						<div
							class="recorded-info"
							v-if="videoInfos.high.isRecording"
							@click.stop="stopRecordAndSave('high')"
						>
							<img src="/src/assets/live/luzhizhong.png" alt="" />
							<span> {{ timestamp }}</span>
						</div>
					</div>
				</div>
				<div class="main-right">
					<template v-for="(value, key, index) in videoInfos">
						<div v-if="key !== 'high'" class="vertical-stack-item" :key="key">
							<div class="video-main" :id="key + 'Ref'" :class="value.isIosFullScreen">
								<img class="video-item" :data-ref="key" :src="value.switchImg" alt="" />
								<div
									class="recorded-info"
									v-if="value.isRecording"
									@click.stop="stopRecordAndSave(key as string)"
								>
									<img src="/src/assets/live/luzhizhong.png" alt="" />
									<span>{{ timestamp }}</span>
								</div>
								<component
									:is="value.component"
									:ref="key + 'VideoRef'"
									:channelNos="value.channelNos"
									:url="value.url"
									:liveId="currentPageLiveId"
									:name="key"
									@recordingTimestamp="jessibucaOnRecordingTimestamp"
									@play="jessibucaOnPlay"
									@recordEnd="stopRecordAndSave"
									@recordStart="startRecord"
									@destroy="jessibucaOnDestroy"
									@fullScreen="onFullScreen"
								></component>
							</div>
						</div>
					</template>
				</div>
			</div>
			<DataLine />

			<!-- 告警信息 -->
			<!-- <div :class="isMobile ? 'event-warning-mobile' : 'event-warning'">
				<div class="event-warning-item" v-for="item in liveEventList" :key="item.id">
					<div class="rec">
						<div class="rec-result">{{ item.recResult }}</div>
						<div class="rec-time">{{ item.recTime }}</div>
					</div>
				</div>
			</div> -->
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onBeforeMount, onMounted, onUnmounted, nextTick, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { getLiveUrl } from '/@/api/home';
import { NextLoading } from '/@/utils/loading';
import { liveInfo } from '/@/stores/fullScreen';
import screenfull from 'screenfull';
import other from '/@/utils/other';
import Video from './video.vue';
import { useRouter, useRoute } from 'vue-router';
import DataLine from './dateLine.vue';

const router = useRouter();
const route = useRoute();

const deviceId = ref(route.query.deviceId);

const timestamp = ref('00:00:00');

const isMobile = ref(other.isMobile());

const highVideoRef = ref();
const frontVideoRef = ref();
const backVideoRef = ref();
const leftVideoRef = ref();
const rightVideoRef = ref();

const liveStore = liveInfo();
const { liveId } = storeToRefs(liveStore);

const currentPageLiveId = computed(() => {
	return liveId.value || route.query.liveId;
});

function getSwitchImg(imgName: string) {
	const url = isMobile ? `live/mobile/${imgName}.png` : `live/${imgName}.png`;
	return other.getStaticImag(url);
}

const videoInfos = reactive<EmptyObjectType>({
	high: {
		component: Video,
		jessibuca: null,
		title: '高清',
		channelNos: 2,
		isPlaying: false,
		url: '',
		controlVisible: false,
		isIosFullScreen: '',
		isRecording: false,
		switchImg: getSwitchImg('m-high'),
	},
	front: {
		component: Video,
		jessibuca: null,
		title: '前',
		channelNos: 1,
		isPlaying: false,
		url: '',
		controlVisible: false,
		isIosFullScreen: '',
		isRecording: false,
		switchImg: getSwitchImg('m-front'),
	},
	back: {
		component: Video,
		jessibuca: null,
		title: '后',
		channelNos: 4,
		isPlaying: false,
		url: '',
		controlVisible: false,
		isIosFullScreen: '',
		isRecording: false,
		switchImg: getSwitchImg('m-back'),
	},
	left: {
		component: Video,
		jessibuca: null,
		title: '左',
		channelNos: 5,
		isPlaying: false,
		url: '',
		controlVisible: false,
		isIosFullScreen: '',
		isRecording: false,
		switchImg: getSwitchImg('m-left'),
	},
	right: {
		component: Video,
		jessibuca: null,
		title: '右',
		channelNos: 6,
		isPlaying: false,
		url: '',
		controlVisible: false,
		isIosFullScreen: '',
		isRecording: false,
		switchImg: getSwitchImg('m-right'),
	},
});

const refVideos: any = {
	high: highVideoRef,
	front: frontVideoRef,
	back: backVideoRef,
	left: leftVideoRef,
	right: rightVideoRef,
};

const getLiveChannels = async (channelNos = [2]) => {
	const params = {
		liveId: currentPageLiveId.value,
		channelNos,
	};

	try {
		// 尝试获取监控URL
		const { payload } = await getLiveUrl(params);
		console.log('payload:', payload);

		// 检查payload是否存在且不是null，并且不为空数组
		if (payload && payload.length > 0) {
			// 过滤掉payload中的null值
			const filteredPayload = payload.filter(Boolean);
			// 遍历videoInfos中的项
			Object.values(videoInfos).forEach((item) => {
				// 查找channelNum匹配的项，确保rc不是null
				const rc = filteredPayload.find(
					(rc: { channelNum: number }) => rc && rc.channelNum === item.channelNos
				);
				// 只有当rc存在且rc.url存在时，才设置item.url
				if (rc && rc.url) {
					item.url = rc.url;
				}
			});
		}
	} catch (error) {
		console.error('Error fetching live channels:', error);
	}
};

const lastTouchEnd = ref();
const goBack = () => {
	router.push('/full-screen');
};

const formatSeconds = (seconds: number): string => {
	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const secs = seconds % 60;
	return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs
		.toString()
		.padStart(2, '0')}`;
};

const jessibucaOnRecordingTimestamp = (seconds: number) => {
	console.log('录制的时长 is11111111', seconds);

	timestamp.value = formatSeconds(seconds);
};

const stopRecordAndSave = (key: string) => {
	videoInfos[key].isRecording = false;

	console.log(videoInfos[key]);
	videoInfos[key].jessibuca.stopRecordAndSave();
	timestamp.value = '00:00:00';
};
const startRecord = (key: string) => {
	videoInfos[key].isRecording = true;
};

// 定义touchstart事件的处理函数
const handleTouchStart = (event: TouchEvent) => {
	if (event.touches.length > 1) {
		event.preventDefault();
	}
};

// 定义touchend事件的处理函数
const handleTouchEnd = (event: TouchEvent) => {
	const now = new Date().getTime();
	if (now - lastTouchEnd.value <= 300) {
		event.preventDefault();
	}
	lastTouchEnd.value = now;
};

function resizeToOriginal(key: string | number) {
	const jessibuca = videoInfos[key].jessibuca;
	if (!jessibuca) return;
	jessibuca.$container.style.width = `100%`;
	jessibuca.$container.style.height = `100%`;
	jessibuca?.resize();
}

function resizeTo16by9(key: string | number) {
	const windowWidth = window.innerWidth;
	const windowHeight = window.innerHeight;
	const targetAspectRatio = 16 / 9;

	// 根据窗口的尺寸计算容器的宽度和高度
	let newWidth, newHeight;
	if (windowWidth / windowHeight > targetAspectRatio) {
		// 如果窗口的宽高比大于16:9，调整高度
		newHeight = windowHeight;
		newWidth = newHeight * targetAspectRatio;
	} else {
		// 否则，调整宽度
		newWidth = windowWidth;
		newHeight = newWidth / targetAspectRatio;
	}

	// console.log(newWidth, newHeight);

	const jessibuca = videoInfos[key].jessibuca;

	console.log('resizeTo16by9---jessibuca', jessibuca);

	if (!jessibuca) return;
	// 设置容器的尺寸
	jessibuca.$container.style.width = `${newWidth}px`;
	jessibuca.$container.style.height = `${newHeight}px`;
	jessibuca.resize();
}

let currentFullScreenKey: any;

const jessibucaOnDestroy = (key: string) => {
	videoInfos[key].jessibuca?.destroy();
	videoInfos[key].jessibuca = null;
	videoInfos[key].isPlaying = false;
	videoInfos[key].controlVisible = false;
};

const jessibucaOnLoad = (data: { name: string; jessibuca: any }) => {};

const jessibucaOnPlay = (data: { name: string; jessibuca: any }) => {
	videoInfos[data.name].jessibuca = data.jessibuca;
	videoInfos[data.name].isPlaying = true;
	if (videoInfos[data.name].isIosFullScreen) {
		// 移动端: 当播放的时候检测到是全屏状态
		const jessibuca = videoInfos[data.name].jessibuca;
		videoInfos[data.name].isIosFullScreen = 'ios-wechat-fullscreen';
		const body = document.body;
		const windowWidth = body.clientWidth;
		const windowHeight = body.clientHeight;
		jessibuca.$container.style.width = `${windowWidth}px`;
		jessibuca.$container.style.height = `${windowHeight}px`;
		jessibuca.setRotate(90);
	}
};

const onFullScreen = (key: string | number) => {
	console.log('onFullScreen');
	if (isMobile.value) {
		const jessibuca = videoInfos[key].jessibuca;
		if (videoInfos[key].isIosFullScreen) {
			videoInfos[key].isIosFullScreen = '';

			if (jessibuca) {
				jessibuca.$container.style.width = `100%`;
				jessibuca.$container.style.height = `100%`;
				jessibuca.setRotate(0);
			}

			console.log('ios---已退出全屏模式', currentFullScreenKey);
			let channelNos: any = [];
			Object.keys(refVideos).forEach((key) => {
				if (key !== currentFullScreenKey && videoInfos[key].isPlaying) {
					channelNos.push(videoInfos[key].channelNos);
				}
			});

			if (channelNos.length) {
				getLiveChannels(channelNos);
			}
			currentFullScreenKey = '';
		} else {
			currentFullScreenKey = key;
			videoInfos[key].isIosFullScreen = 'ios-wechat-fullscreen';
			const body = document.body;
			const windowWidth = body.clientWidth;
			const windowHeight = body.clientHeight;

			if (jessibuca) {
				jessibuca.$container.style.width = `${windowWidth}px`;
				jessibuca.$container.style.height = `${windowHeight}px`;
				jessibuca.setRotate(90);
			}
			console.log('ios---进入全屏模式', currentFullScreenKey);
			Object.keys(refVideos).forEach((key) => {
				if (key !== currentFullScreenKey && videoInfos[key].jessibuca) {
					videoInfos[key].jessibuca.destroy();
				}
			});
		}

		nextTick(() => {
			if (jessibuca) jessibuca.resize();
		});
	} else {
		if (screenfull.isEnabled) {
			console.log('pc端全屏---key', key);
			const dom = document.getElementById(`${key}Ref`) as HTMLElement;
			console.log('pc端全屏---dom', dom);
			if (dom) screenfull.toggle(dom);
			currentFullScreenKey = key;
			// screenfull.toggle(key === 'high' ? refDoms[key].value : refDoms[key].value[0]);
		}
	}
};

// 安卓跟web端支持screenfull
if (screenfull.isEnabled && !isMobile.value) {
	let debounceTimer: NodeJS.Timeout | null = null;
	screenfull.on('change', (event: Event) => {
		console.log('pc端全屏screenfull-change');
		if (debounceTimer) clearTimeout(debounceTimer);
		debounceTimer = setTimeout(() => {
			const target = event.target as HTMLElement;
			console.log('target', target.childNodes[0]);
			if (screenfull.isFullscreen) {
				console.log('pc端现在是全屏模式', currentFullScreenKey);
				resizeTo16by9(currentFullScreenKey);
				Object.keys(refVideos).forEach((key) => {
					if (key !== currentFullScreenKey && videoInfos[key].jessibuca) {
						videoInfos[key].jessibuca.destroy();
					}
				});
			} else {
				resizeToOriginal(currentFullScreenKey);
				let channelNos: any = [];
				Object.keys(refVideos).forEach((key) => {
					if (key !== currentFullScreenKey && videoInfos[key].isPlaying) {
						channelNos.push(videoInfos[key].channelNos);
					}
				});
				console.log('pc端已退出全屏模式', channelNos);
				if (channelNos.length) {
					getLiveChannels(channelNos);
				}
				currentFullScreenKey = '';
			}
		}, 50);
	});
}

// 查询告警信息
let eventInterval: NodeJS.Timeout | null = null;
onBeforeMount(async () => {
	console.log('liveId.value', liveId.value);
	if (!currentPageLiveId.value) {
		await liveStore.setLiveId();
	}
	getLiveChannels();
	// Object.keys(refVideos).forEach((key) => {
	// 	resizeTo16by9(key);
	// });
	// const limit = isMobile.value ? 5 : 2;
	// if (!liveEventList.value.length) {
	// 	liveStore.setEventList(false, false, limit);
	// }
	// eventInterval = setInterval(() => {
	// 	liveStore.setEventList(false, false, limit);
	// }, 10000);
});

onMounted(() => {
	NextLoading.done();
	// 阻止移动端默认的双击缩放和滑动功能
	window.addEventListener('touchstart', handleTouchStart, { passive: false });
	window.addEventListener('touchend', handleTouchEnd, { passive: false });
	window.addEventListener('orientationchange', function () {
		// 阻止页面重排
		var lastOrientation = window.orientation;
		window.addEventListener('resize', function (event: Event) {
			if (window.orientation === lastOrientation) {
				// 阻止默认行为
				event.preventDefault();
			}
		});
	});
});

onUnmounted(() => {
	window.removeEventListener('touchstart', handleTouchStart);
	window.removeEventListener('touchend', handleTouchEnd);
	eventInterval && clearInterval(eventInterval);
});
</script>

<style lang="scss" scoped>
/* @import url(); 引入css类 */

/* PC端样式 */
@media (min-width: 992px) {
	.live-container {
		position: relative;
		z-index: 0;
		height: 100%;
		background-color: #000000;

		.fanhui {
			width: 84px;
			height: 35px;
			object-fit: cover;
			position: absolute;
			top: 58px;
			left: 40px;
			z-index: 20;
			cursor: pointer;
		}
		.main {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 2;
			padding: 113px 20px 20px 40px;

			.main-top {
				height: calc(100% - 75px);
				display: flex;
				flex-wrap: wrap;
				.main-left {
					width: 80%;
					height: 100%;
					margin-right: 20px;
					position: relative;
					background-image: url('/src/assets/live/big-video-bg.png');
					background-repeat: no-repeat;
					background-size: 100% 100%;
					padding: 10px 14px;

					.high-video {
						height: 100%;
						position: relative;
						display: flex;
						align-items: center;
						.video-item {
							position: absolute;
							top: 5px;
							left: 5px;
							z-index: 10;
							width: 70px;
							height: 29px;
							background-repeat: no-repeat;
							background-size: 100% 100%;
						}
					}
					.recorded-info {
						position: absolute;
						top: 18px;
						left: 50%;
						z-index: 11;
						transform: translateX(-50%);
						width: 189px;
						height: 32px;
						display: flex;
						justify-content: center;
						align-items: center;
						color: #fff;
						font-size: 18px;
						background-image: url('/src/assets/live/recorded-bg.png');
						background-repeat: no-repeat;
						background-size: 100% 100%;
						cursor: pointer;
						img {
							width: 23px;
							height: 23px;
							object-fit: contain;
							margin-right: 15px;
						}
					}

					:deep(.play) {
						width: 73px !important;
						height: 73px !important;
					}
					.main-left-control {
						height: 35px;
						margin-top: 10px;
						display: flex;
						justify-content: space-between;
						img {
							object-fit: contain;
							width: 97px;
							height: 35px;
							cursor: pointer;
						}
					}

					.disabled-div {
						pointer-events: none;
					}
				}
				.main-right {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: center;

					:deep(.play) {
						width: 35px !important;
						height: 35px !important;
					}
					.vertical-stack-item {
						flex: 1;
						margin-bottom: 20px;
						position: relative;
						background-image: url('/src/assets/live/small-video-bg.png');
						background-repeat: no-repeat;
						background-size: 100% 100%;
						padding: 4px;
						display: flex;
						align-items: center;
						overflow: hidden;
						.video-main {
							width: 100%;
							height: 100%;
							position: relative;
							z-index: 0;
							display: flex;
							align-items: center;

							.recorded-info {
								position: absolute;
								top: 18px;
								left: 50%;
								z-index: 11;
								transform: translateX(-50%);
								width: 189px;
								height: 32px;
								display: flex;
								justify-content: center;
								align-items: center;
								color: #fff;
								font-size: 18px;
								background-image: url('/src/assets/live/recorded-bg.png');
								background-repeat: no-repeat;
								background-size: 100% 100%;
								cursor: pointer;
								img {
									width: 23px;
									height: 23px;
									object-fit: contain;
									margin-right: 15px;
								}
							}
							.video-item {
								position: absolute;
								top: 7px;
								left: 7px;
								width: 57px;
								height: 27px;
								z-index: 20;
								background-repeat: no-repeat;
								background-size: 100% 100%;
								cursor: pointer;
							}
						}
						&:last-child {
							margin-bottom: 0;
						}
					}
				}
			}

			.event-warning {
				width: 80%;
				height: 3.611vh;
				// background-color: #fff;
				overflow: hidden;
				margin: 0.463vh 0;
				.event-warning-item {
					position: relative;
					width: 50%;
					height: 100%;
					display: inline-block;
					background-image: url('/src/assets/live/event-warning.png');
					background-repeat: no-repeat;
					background-size: 100% 100%;
					&::before {
						content: '';
						position: absolute;
						top: 50%;
						left: 18px;
						transform: translateY(-54%);
						width: 106px;
						height: 2.685vh;
						background-image: url('/src/assets/live/event-warning-name.png');
						background-repeat: no-repeat;
						background-size: 100%;
					}
					.rec {
						height: 100%;
						color: #fff;
						font-size: 16px;
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding-left: calc(106px + 18px + 12px);
						padding-right: 30px;
					}
				}
			}
		}

		.title {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 80px;
			// background-image: url('/src/assets/fullScreen/title-name.png');
			// background-repeat: no-repeat;
			// background-size: contain;
			img {
				display: block;
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
		}

		.footer {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			height: 187px;
			background-image: url('/src/assets/fullScreen/footer.png');
			background-repeat: no-repeat;
			background-size: 100% 103%;

			display: flex;
			flex-direction: column;
			justify-content: flex-end;
			// background-color: #fff;

			img {
				display: block;
				width: 100%;
				height: 33px;
				object-fit: cover;
			}
		}
	}
}

@media (max-width: 767px) {
	.live-container {
		position: relative;
		z-index: 0;
		height: 100%;
		background-color: #000000;

		.main {
			width: 100%;
			height: 100%;
			z-index: 2;
			padding: 5px;
			display: flex;
			flex-direction: column;

			.main-left {
				width: 100%;
				height: 215px;
				position: relative;
				background-image: url('/src/assets/live//mobile/big-video-bg.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
				padding: 8px;
				.recorded-info {
					position: absolute;
					top: 18px;
					left: 50%;
					z-index: 20;
					transform: translateX(-50%);
					width: 189px;
					height: 32px;
					display: flex;
					justify-content: center;
					align-items: center;
					color: #fff;
					font-size: 18px;
					background-image: url('/src/assets/live/mobile/recorded-bg.png');
					background-repeat: no-repeat;
					background-size: 100% 100%;
					cursor: pointer;
					img {
						width: 23px;
						height: 23px;
						object-fit: contain;
						margin-right: 15px;
					}
				}
				.high-video {
					height: 100%;
					position: relative;
					.video-item {
						position: absolute;
						top: 5px;
						left: 5px;
						z-index: 20;
						width: 70px;
						height: 29px;
						background-repeat: no-repeat;
						background-size: 100% 100%;
					}
				}
				:deep(.play) {
					width: 73px !important;
					height: 73px !important;
				}
				.main-left-control {
					height: 25px;
					margin-top: 8px;
					display: flex;
					justify-content: space-between;

					.stop-record {
						width: 68px;
						height: 100%;
					}
					img {
						object-fit: cover;
						width: 100%;
						height: 100%;
						cursor: pointer;
					}
				}
			}

			// .ios-safari-fullscreen {
			// 	@extend .ios-wechat-fullscreen;
			// 	width: 390px !important;
			// 	height: 664px !important;
			// }

			.main-right {
				margin-top: 8px;
				display: flex;
				flex-wrap: wrap;

				:deep(.play) {
					width: 25px !important;
					height: 25px !important;
				}
				:deep(.jessibuca-container) {
					.jessibuca-control {
						min-height: 30px;
						.jessibuca-control-item {
							width: 30px;
							scale: 0.9;
						}
					}
				}
				.vertical-stack-item {
					width: calc((100% - 8px) / 2);
					height: 100px;
					margin-right: 8px;
					margin-bottom: 8px;
					position: relative;
					background-image: url('/src/assets/live/mobile/small-video-bg.png');
					background-repeat: no-repeat;
					background-size: 100% 100%;
					padding: 4px;

					.video-main {
						width: 100%;
						height: 100%;
						position: relative;
						z-index: 0;
						.video-item {
							position: absolute;
							top: 5px;
							left: 5px;
							width: 57px;
							height: 27px;
							z-index: 20;
							background-repeat: no-repeat;
							background-size: 100% 100%;
							cursor: pointer;
						}
					}

					&:nth-child(2n) {
						margin-right: 0;
						margin-bottom: 0;
					}
				}
			}

			.ios-wechat-fullscreen {
				position: fixed !important;
				margin: 0;
				top: 0;
				left: 0;
				width: 100vw;
				height: 100vh !important;
				z-index: 999 !important;
				display: flex;

				.recorded-info {
					top: 50%;
					left: calc(90% - 5px);
					transform-origin: top left;
					transform: rotate(90deg) translate(-50%, -100%);
					cursor: pointer;
				}

				.video-item {
					top: 6px !important;
					left: 90% !important;
					transform-origin: top left;
					transform: rotate(90deg) translate(0%, -100%);
				}
				:deep(.jessibuca-container) {
					.play {
						width: 73px !important;
						height: 73px !important;
						transform-origin: top left !important;
						transform: rotate(90deg) translate(-50%, -50%) !important;
					}
					.jessibuca-control {
						min-height: 50px;
						left: -90%;
						top: 50%;
						transform: rotate(90deg) translateY(-50%);
						transform-origin: center top;
						width: 100vh;
						padding-right: 30px;

						.jessibuca-control-item {
							width: 50px !important;
							height: 100% !important;
							scale: 1 !important;
						}
					}
				}
			}
		}

		.title,
		.footer {
			display: none;
		}
	}
}

.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
	opacity: 0;
}
</style>

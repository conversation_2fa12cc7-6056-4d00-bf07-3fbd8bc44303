import { defineStore } from 'pinia';
// import { dynamicRoutes } from '/@/router/route';
import { getUserInfo, signOut } from '/@/api/auth';
import defaultAvatar from '/@/assets/default-avatar.png';
import { Session, Local } from '/@/utils/storage';

/**
 * 仅为了测试使用
 *  @methods allPermissions 获取所有动态路由的权限，从`meta.permissions`中获取
 */
// let allPermissions: string[] = [];
// const getAllPermissions = (arr: any) => {
// 	arr.forEach((item: any) => {
// 		if (item.meta.permissions) {
// 			allPermissions = allPermissions.concat(item.meta.permissions);
// 		}
// 		if (item.children) {
// 			getAllPermissions(item.children)
// 		}
// 	});
// };

/**
 * 用户信息
 * @methods setUserInfos 设置用户信息
 */
export const useUserInfo = defineStore('userInfo', {
	state: (): UserInfosState => ({
		userInfos: {
			id: '',
			userName: '',
			photo: defaultAvatar,
			time: 0,
			role: null, // 角色
			permissions: [], // 权限
			authBtnList: [],
		},
	}),
	actions: {
		async setUserInfos() {
			return new Promise((resolve) => {
				getUserInfo().then(({ payload }) => {
					const userInfos = {
						id: payload.id,
						userName: payload.username,
						time: new Date().getTime(),
						role: payload.role,
						permissions: payload.perms,
					};
					this.userInfos = Object.assign(this.userInfos, userInfos);
					resolve(userInfos);
				});
				// getAllPermissions(dynamicRoutes[0].children);
				// const userInfos = {
				// 	id: 111,
				// 	userName: 'admin',
				// 	time: new Date().getTime(),
				// 	role: [],
				// 	permissions: allPermissions,
				// };
				// this.userInfos = Object.assign(this.userInfos, userInfos);
				// resolve(userInfos);
			});
		},
		// 退出登录
		async userLogout () {
			return new Promise((resolve, reject) => {
				signOut().then(({ payload }) => {
					resolve(payload);
					this.clearAll(payload);
				}).catch(() => {
					reject();
				})
			});
		},
		clearAll (redirctUrl: string) {
			// const { hash, protocol, hostname } = window.location;
			Session.clear();
      Local.clear();
			// 本系统退出后刷新当前页；若为湿地子系统，则跳转到湿地登陆页。
			if (!redirctUrl) {
				// 使用 reload 时，不需要调用 resetRoute() 重置路由
				window.location.reload();
			} else {
				window.location.href = redirctUrl;
			}
		}
	},
});

<template>
  <div class="search-container" :gutter="20">
    <div class="form" v-for="opt in props.searchItems" :key="opt.key" :span="5">
      <span class="form-label">{{ opt.label }}</span>
      <div class="form-content">
        <!-- 输入框 -->
        <el-input
          style="width: 100%;"
          v-if="opt.type === 'input'"
          v-model.trim="params[opt.key]"
          :placeholder="'请输入' + opt.label"
          clearable
        />
        <!-- 选择框 -->
        <el-select
          style="width: 100%;"
          v-else-if="opt.type === 'select'"
          v-model="params[opt.key]"
          clearable
          :placeholder="'请选择' + opt.label"
          :multiple="opt.multiple"
        >
          <el-option
            v-for="item in opt.options"
            :key="item[opt.optionsValue as string] || item.value"
            :label="item[opt.optionsLabel as string] || item.label"
            :value="item[opt.optionsValue as string] || item.value"
          />
        </el-select>
        <!-- 时间范围 -->
        <el-date-picker
          style="width: 100%;"
          v-else-if="opt.type === 'daterange'"
          v-model="params[opt.key]"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </div>
    </div>
    <div class="btns">
      <el-button type="success" plain @click="onSearch">
        <el-icon>
          <ele-Search />
        </el-icon>
        查询
      </el-button>
      <el-button type="primary" plain @click="onReset">
        <el-icon>
          <ele-Refresh />
        </el-icon>
        重置
      </el-button>
      <slot name="afterBtn"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  
  interface SearchItem {
    type: string;
    key: string;
    label: string;
    options?: EmptyArrayType,
    optionsLabel?: string;
    optionsValue?: string;
    multiple?: boolean;
  }
  const props = withDefaults(defineProps<{
    searchItems: SearchItem[],
    isSearchBtns: boolean,
  }>(), {
    searchItems: () => [],
    isSearchBtns: true
  })
  const emits = defineEmits(['onSearch'])
  const params = ref<EmptyObjectType>({})

  const onSearch = () => {
    emits('onSearch', params.value)
  }
  const onReset = () => {
    params.value = {}
    emits('onSearch', params.value)
  }
</script>

<style lang="scss" scoped>
.search-container {
  display: flow-root;
  .form {
    // width: 248px;
    display: inline-block;
    color: #fff;
    line-height: 36px;
    margin-bottom: 22px;
    margin-right: 10px;
    &-label {
      white-space: nowrap;
      font-weight: bold;
      margin-right: 8px;
    }
    &-content {
      display: inline-block;
    }
  }
  .btns {
    float: right;
    margin-right: 10px;
    margin-top: 2px;
  }
}
</style>
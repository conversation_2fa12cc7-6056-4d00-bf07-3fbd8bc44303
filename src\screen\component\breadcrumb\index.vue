<template>
	<div class="hlj-breadcrumb">
		<div class="breadcrumb-container">
			<!-- 移除调试输出 -->
			<div class="breadcrumb-item" v-for="(item, index) in breadcrumbList" :key="index">
				<span
					:class="{ clickable: item.path && index !== breadcrumbList.length - 1 }"
					@click="handleClick(item)"
				>
					{{ item.title }}
				</span>
				<span class="separator" v-if="index !== breadcrumbList.length - 1">></span>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';

interface BreadcrumbItem {
	title: string;
	path?: string;
	query?: Record<string, any>;
}

interface ParentConfig {
	match?: (query: Record<string, any>) => boolean;
	path: string;
	title: string;
	keepQuery?: string[];
	parent?: {
		path: string;
		title: string;
	};
}

interface BreadcrumbConfig {
	parents?: ParentConfig[];
}

const props = defineProps({
	// 当前页面标题（可选，如果不提供则使用路由元数据的title）
	currentTitle: {
		type: String,
		default: '',
	},
	// 自定义面包屑列表（如果提供，将覆盖自动生成的面包屑）
	customList: {
		type: Array as () => BreadcrumbItem[],
		default: () => [],
	},
});

const router = useRouter();
const route = useRoute();

// 计算面包屑列表
const breadcrumbList = computed(() => {
	// 如果提供了自定义列表，则使用自定义列表
	if (props.customList && props.customList.length > 0) {
		return props.customList;
	}

	const list: BreadcrumbItem[] = [];

	// 1. 从路由元数据中获取面包屑配置
	const breadcrumbConfig = route.meta.breadcrumb as BreadcrumbConfig | undefined;

	if (breadcrumbConfig?.parents && breadcrumbConfig.parents.length > 0) {
		// 修改匹配逻辑，优先处理更具体的匹配条件
		// 首先检查是否包含 fromRank=true 的匹配条件
		let matchedConfig: ParentConfig | undefined;

		// 优先匹配同时具有 pagePath 和 fromRank 的配置
		if (route.query.fromRank === 'true') {
			matchedConfig = breadcrumbConfig.parents.find(
				(p) => p.match?.(route.query) && p.match.toString().includes('fromRank')
			);
		}

		// 如果没找到，再尝试普通匹配
		if (!matchedConfig) {
			matchedConfig = breadcrumbConfig.parents.find((p) => p.match?.(route.query));
		}

		if (matchedConfig) {
			// 3. 如果有祖父级，先添加祖父级
			if (matchedConfig.parent) {
				list.push({
					title: matchedConfig.parent.title,
					path: matchedConfig.parent.path,
					query: {}, // 对于最顶层的祖父级导航，不需要传递查询参数
				});
			}

			// 4. 添加父级
			const query: Record<string, any> = {};
			if (matchedConfig.keepQuery) {
				matchedConfig.keepQuery.forEach((key) => {
					if (route.query[key]) {
						query[key] = route.query[key];
					}
				});
			}

			list.push({
				title: matchedConfig.title,
				path: matchedConfig.path,
				query,
			});
		}
	} else {
		// 兼容旧的导航逻辑，基于URL参数
		const pagePath = route.query.pagePath as string;
		if (pagePath === 'species') {
			list.push({
				title: '物种监测',
				path: '/hlj-data-screen/species',
			});

			// 如果有fromRank参数，说明是从rankDetail页面跳转到atlas页面的
			if (route.query.fromRank === 'true') {
				list.push({
					title: '发现物种统计',
					path: '/hlj-data-screen/rankDetail',
					query: {
						pagePath: route.query.pagePath,
						eventType: route.query.eventType,
					},
				});
			}
		} else if (pagePath === 'device') {
			list.push({
				title: '设备监控',
				path: '/hlj-data-screen/device-monitor',
			});

			// 如果有fromRank参数，说明是从rankDetail页面跳转到atlas页面的
			if (route.query.fromRank === 'true') {
				list.push({
					title: '发现物种统计',
					path: '/hlj-data-screen/rankDetail',
					query: {
						pagePath: route.query.pagePath,
						eventType: route.query.eventType,
						filterType: route.query.filterType,
					},
				});
			}
		} else if (pagePath === 'patrol') {
			list.push({
				title: '巡护管理',
				path: '/hlj-data-screen/patrol',
			});
		} else if (pagePath === 'recent') {
			list.push({
				title: '最近感知',
				path: '/hlj-data-screen/recent',
			});
		}
	}

	// 5. 添加当前页面
	if (props.currentTitle) {
		list.push({
			title: props.currentTitle,
		});
	} else {
		// 如果没有提供当前页面标题，使用路由元数据中的标题
		const routeTitle = route.meta.title as string;
		if (routeTitle) {
			list.push({
				title: routeTitle,
			});
		}
	}

	return list;
});

// 处理面包屑点击
const handleClick = (item: BreadcrumbItem) => {
	if (item.path) {
		router.push({
			path: item.path,
			query: item.query || {},
		});
	}
};
</script>

<style lang="scss" scoped>
.hlj-breadcrumb {
	display: flex;
	align-items: center;

	.breadcrumb-container {
		display: flex;
		align-items: center;

		.breadcrumb-item {
			display: flex;
			align-items: center;
			font-size: vw(14);
			color: rgba(255, 255, 255, 0.7);

			.clickable {
				cursor: pointer;
				color: $sd-screen-success;
				&:hover {
					text-decoration: underline;
				}
			}

			.separator {
				margin: 0 vw(5);
			}
		}
	}
}
</style>

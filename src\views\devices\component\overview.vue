<template>
	<el-dialog title="设备概览" v-model="state.dialog.isShowDialog" width="680px" align-center>
    <el-descriptions border :column="2" :span="2">
      <el-descriptions-item label="温度">{{ state.robotPram.temperature }} ℃</el-descriptions-item>
      <el-descriptions-item label="湿度">{{ state.robotPram.humidity }} %RH</el-descriptions-item>
      <el-descriptions-item label="总巡航里程">{{ Number((state.robotPram.mileage / 1000).toFixed(2)) }} km</el-descriptions-item>
      <el-descriptions-item label="当前巡航里程">{{ Number((state.robotPram.currentMileage / 1000).toFixed(2)) }} km</el-descriptions-item>
      <el-descriptions-item label="总开机时长">{{ Number((state.robotPram.duration / 60).toFixed(0)) }} h</el-descriptions-item>
      <el-descriptions-item label="当前开机时长">{{ state.robotPram.currentDuration }} min</el-descriptions-item>
      <el-descriptions-item label="电量百分比">{{ state.robotPram.electricity }} %</el-descriptions-item>
      <el-descriptions-item label="电池温度">{{ state.robotPram.batteryTemp }} ℃</el-descriptions-item>
      <el-descriptions-item label="经度">{{ state.robotPram.longitude }}</el-descriptions-item>
      <!-- <el-descriptions-item label="GCJ-02">{{ state.robotPram.gclLongitude }}</el-descriptions-item> -->
      <el-descriptions-item label="纬度">{{ state.robotPram.latitude }}</el-descriptions-item>
      <!-- <el-descriptions-item label="GCJ-02">{{ state.robotPram.getGclLatitude }}</el-descriptions-item> -->
      <el-descriptions-item label="是否正在充电">
        <el-tag :type="state.robotPram.ifChargeTask ? '' : 'info'">
          {{ state.robotPram.ifChargeTask ? '是' : '否' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="设备状态">
        <el-tag :type="state.robotPram.online ? '' : 'info'">
          {{ state.robotPram.online ? '在线' : '不在线' }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
			<span class="dialog-footer">
				<el-button type="primary" @click="closeDialog">关 闭</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { getPatrolRobotStatus } from '/@/api/devices';

// 定义变量内容
const state = reactive({
  robotPram: {} as RobotParameters,
  dialog: {
    isShowDialog: false,
  }
});
// 打开弹窗
const openDialog = async (productId: string) => {
  const elMsg = ElMessage({
    type: 'warning',
    message: '获取设备参数中...',
    duration: 0,
  })
  try {
    const { payload } = await getPatrolRobotStatus(productId);
    state.dialog.isShowDialog = true;
    state.robotPram = <RobotParameters><unknown>payload.status;
    elMsg.close();
  } catch(err) {
    elMsg.close();
  }
};
const closeDialog = () => {
	state.dialog.isShowDialog = false;
};

defineExpose({
	openDialog,
});
</script>

import { SpeciesKey } from '/@/views/species/type';

// # 模型管理，识别类型选项
export const AI_Model_Type = [
  { label: '野生动物', value: 0 },
  { label: '鸟类', value: 1 },
  { label: '水生物种', value: 21 },
  // { label: '昆虫', value: 2 },
];

// # 模型管理，任务类型选项
export const AI_Task_Type = [
  { label: '分类', value: 0 },
  { label: '检测分类', value: 1 },
];

// # 设备管理，坐标系类型 0：bd09 1：wgs84 2：GCJ02
export const Coordinate_Type: EmptyObjectType = {
  0: 'bd09',
  1: 'wgs84',
  2: 'GCJ02',
};
// 设备厂商
export const Manufacturer: EmptyObjectType = {
  1: '海康威视',
  2: '大华',
  3: '和普威视',
  4: '天地伟业',
}

// # 物种资源库，定义它各个子路由对应的物种类型，以便于跳转时查询对应的接口
export const Bird_Tag: SpeciesKey[] = [
  { key: 'precious', label: '珍稀', value: 2 },
  { key: 'specific', label: '特定', value: 1 },
  { key: 'nmg', label: '内蒙古', value: 0 },
];

export const Species_Key: SpeciesKey[] = [
  { key: 'plant', label: '植物', value: 0 },
  { key: 'bird', label: '鸟类', value: 1 },
  { key: 'insect', label: '昆虫', value: 2 },
  { key: 'animal', label: '哺乳类', value: 3 },
  { key: 'fish', label: '鱼类', value: 4 },
  { key: 'amphibians', label: '两栖类', value: 5 },
  { key: 'reptile', label: '爬行类', value: 6 },
];

// 保护等级
export const protectLevelOptions = [
  { label: '一级', value: '一级' },
  { label: '二级', value: '二级' },
  { label: '普通', value: '普通' },
];

// 居留类型
export const residencyTypeOptions = [
  { label: '留鸟', value: 1 },
  { label: '候鸟', value: 2 },
  // { label: '旅鸟', value: 3 },
];

// # 系统管理 - 用户管理，角色选项
export const DEFAULT_ROLE = [
  // {
  //   id: 1, name: '超级管理员', anonRole: false, authcRole: false,
  // },
  {
    id: 2, name: '湿地管理员', anonRole: false, authcRole: false,
  },
  {
    id: 3, name: '湿地成员', anonRole: false, authcRole: false,
  },
];


export interface TimeRadio {
  label: string;
  value: TimeType;
}
export const TIME_RADIO_OPTIONS: TimeRadio[] = [
  { label: '近一月', value: 1 },
  { label: '近三月', value: 2 },
  { label: '近一年', value: 3 },
  { label: '累计', value: 'all' },
];

// 动物 鸟类 
export const ANIMAL_TYPE_OPTIONS = [
  { label: '全部', value: 'all' },
  { label: '动物', value: 0 },
  { label: '鸟类', value: 1 },
  { label: '水生', value: 21 },
  { label: '人', value: 24 },
];

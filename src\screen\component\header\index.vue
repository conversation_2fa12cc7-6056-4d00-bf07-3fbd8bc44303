<template>
	<div class="header-container">
		<div class="h-logo">
			<img src="/src/assets/sd/header/logo-1.png" alt="" />
		</div>
		<div class="h-nav-menus">
			<template v-for="item in filteredMenus" :key="item.label">
				<div class="nav-menu-item" @click="onNavMenuClick(item)">
					<img :src="props.activeMenu === item.key ? item.activePic : item.defaultPic" alt="" />
				</div>
			</template>
		</div>

		<div class="h-right">
			<div>{{ currentDate }}&nbsp;&nbsp;</div>
			<div class="icon" v-if="hasSystemPermission()">
				<el-dropdown :teleported="false" @command="switchToSystem($event)">
					<img src="/src/assets/sd/header/hr-admin.png" alt="" />
					<template #dropdown>
						<el-dropdown-menu>
							<template v-for="value in getAccessibleSystems()" :key="value.title">
								<el-dropdown-item :command="value.systemType">
									<SvgIcon :name="value.icon" :size="20" />
									{{ value.title }}
								</el-dropdown-item>
							</template>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</div>
			<div class="icon" @click="onLogout">
				<img src="/src/assets/sd/header/hr-logout.png" alt="" />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus';
import { useUserInfo } from '/@/stores/userInfo';
import { formatDate } from '/@/utils/formatTime';
import { MenuItem, getFilteredMenus } from '/@/viewsHLJ/dataScreen/config/menuConfig';
import {
	switchToSystem,
	getAccessibleSystems,
	hasSystemPermission,
} from '/@/services/systemService';

const props = defineProps<{
	activeMenu: string;
}>();

const emits = defineEmits<{
	(e: 'onNavMenuClick'): void;
}>();
const route = useRoute();
const router = useRouter();
const userStore = useUserInfo();

// 根据用户权限过滤菜单
const filteredMenus = computed(() => getFilteredMenus());

// 点击菜单
const onNavMenuClick = (menu: MenuItem) => {
	router.push(menu.path);
	emits('onNavMenuClick');
};

// 右侧日期 和 天气
// 当前日期时间
const currentDate = ref('');
const getCurrentDate = () => {
	currentDate.value = formatDate(new Date(), 'YYYY-mm-dd HH:MM WWW');
	window.requestAnimationFrame(getCurrentDate);
};
window.requestAnimationFrame(getCurrentDate);

// 查找指定systemType的路由
const findRouteBySystemType = (routes: RouteItem[], systemType: string): RouteItem | null => {
	for (const route of routes) {
		// 如果没有systemType，则默认为生物多样性系统
		if (
			(systemType === 'biodiversity' && !route.meta?.systemType) ||
			route.meta?.systemType?.includes(systemType)
		) {
			return route;
		}
		if (route.children) {
			const found = findRouteBySystemType(route.children, systemType);
			if (found) return found;
		}
	}
	return null;
};

// 退出登录
const onLogout = () => {
	ElMessageBox({
		closeOnClickModal: false,
		closeOnPressEscape: false,
		title: '提示',
		message: '此操作将退出登录, 是否继续?',
		showCancelButton: true,
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		buttonSize: 'default',
		beforeClose: (action, instance, done) => {
			if (action === 'confirm') {
				instance.confirmButtonLoading = true;
				instance.confirmButtonText = '退出中';
				userStore
					.userLogout()
					.then(() => {
						instance.confirmButtonLoading = false;
						done();
					})
					.catch(() => {
						instance.confirmButtonLoading = false;
					});
			} else {
				done();
			}
		},
	});
};

// 在组件挂载时初始化高亮状态
onMounted(() => {});
</script>

<style lang="scss" scoped>
.header-container {
	height: 100%;
	display: flex;
	justify-content: space-between;
	overflow: visible;
	position: relative;

	&::after {
		content: '';
		display: block;
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		height: vw(2);
		background: linear-gradient(to right, transparent 18%, rgba(75, 239, 124, 0.4) 28%);
	}

	.h-logo {
		width: vw(520);
		height: 100%;

		img {
			position: relative;
			top: 13%;
			z-index: 99;
			height: 100%;
			width: 100%;
		}
	}

	.h-nav-menus {
		flex: 1;
		display: flex;
		align-items: center;

		.nav-menu-item {
			width: vw(180);
			height: vw(48);
			line-height: vw(48);
			text-align: center;
			// margin-left: vw(-5);
			cursor: pointer;
			color: rgba(255, 255, 255, 0.8);
			font-size: vw(20);
			font-family: 'ALiMaMaShuHeiTi';

			img {
				width: 100%;
				height: 100%;
				transition: transform 0.2s;
			}

			&:hover {
				img {
					transform: scale(1.03);
				}
			}
		}
	}

	.h-right {
		padding: vw(18) vw(15);
		display: flex;
		align-items: center;
		font-size: vw(14);
		font-weight: lighter;
		font-family: 'ALiMaMaShuHeiTi';

		.icon {
			width: vw(24);
			height: vw(24);
			margin-left: vw(12);
			cursor: pointer;
			text-align: center;
			border-radius: 4px;

			img {
				width: vw(21);
				position: relative;
				top: vw(4);
			}

			.lingdong {
				width: vw(24);
			}

			&:hover {
				background-color: rgba(75, 239, 124, 0.1);
			}
		}
	}
}
</style>

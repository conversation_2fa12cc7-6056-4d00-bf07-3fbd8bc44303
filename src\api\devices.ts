import request from '/@/utils/request';

/**
 * @method getDevices 获取设备列表
 * @method createDevice 新增
 * @method updateDevice 修改
 * @method deleteDevice 删除
 * @method checkStatus 检查设备状态
 * @method syncPatrolRobots 同步机器人设备系统
 * @method getPatrolRobotStatus 获取巡护机器人概览
 * @methop controlCamera 设备云台控制
 * 
 * # 防火设备
 * @method getFireDevices 分页查询
 * @method createFireDevice 新增
 * @method updateFireDevice 修改
 * @method deleteFireDevice 删除
 */

export function getDevices(params?: Object) {
  return request({
    url: '/devices/nums',
    method: 'get',
    params,
  });
}

export function createDevice(data: Object) {
  return request({
    url: `/devices`,
    method: 'post',
    data,
  });
}

export function updateDevice(data: EmptyObjectType) {
  return request({
    // url: `/devices/${data.id}`, // 单个修改
    url: `/devices/update`, // 多个修改，参数ids
    method: 'put',
    data,
  });
}

export function deleteDevice(id: string) {
  return request({
    url: `/devices/${id}`,
    method: 'delete',
  });
}

export function batchDeleteDevice(ids: string[]) {
  return request({
    url: `/devices/del`,
    method: 'delete',
    data: ids
  });
}

export function checkStatus() {
  return request({
    url: `/devices/check_status`,
    method: 'post',
  });
}

export function syncPatrolRobots() {
  return request({
    url: `/robots/init`,
    method: 'post',
  });
}

export function getPatrolRobotStatus(productId: string) {
  return request({
    url: `/robots/${productId}/status`,
    method: 'get',
  });
}

// 设备云台控制
interface ControlCameraParams {
  deviceRtsp: string;
  commandType: number;
  manufacturer: number | null;
}
/**
 * 操作类型
 * 2：接通灯光电源 3：接通雨刷开关 4：接通风扇开关 5：接通加热器开关 6：接通辅助设备开关 7：接通辅助设备开关 8：设置预置点 9：清除预置点
 * 11：焦距以速度SS变大(倍率变大)  12：焦距以速度SS变小(倍率变小) 13：焦点以速度SS前调 14：焦点以速度SS后调 15：光圈以速度SS扩大 16：光圈以速度SS缩小
 * 21：云台以SS的速度上仰 22：云台以SS的速度下俯 23：云台以SS的速度左转 24：云台以SS的速度右转
 * 25：云台以SS的速度上仰和左转 26：云台以SS的速度上仰和右转 27：云台以SS的速度下俯和左�� 28：云台以SS的速度下俯和右转 29：云台以SS的速度左右自动扫描
 * 30：将预置点加入巡航序列 31：设置巡航点停顿时间 32：设置巡航速度 33：将预置点从巡航序列中删除
 * 34：开始记录 35：停止记录 36：开始 37：开始巡航 38：停止巡航 39：快球转到预置点
 */
export function controlCamera(data: ControlCameraParams) {
  return request({
    url: '/camera/ctrl',
    method: 'post',
    data,
  });
}


// 实时视频播放地址，`ws://{项目ip}/zlmediakit-server/monitor/{设备id}.live.flv`
export function generatePlayUrl(deviceId: string) {
  return import.meta.env.MODE === 'development' ?
    `ws://sd.nbzhu.cn/zlmediakit-server/monitor/${deviceId}.live.flv` :
    `ws://${window.location.host}/zlmediakit-server/monitor/${deviceId}.live.flv`;
}


// # 防火设备
export function getFireDevices(params?: Object) {
  return request({
    url: '/devices/group/fire/protection',
    method: 'get',
    params,
  });
}



export function createFireDevice(data: Object) {
  return request({
    url: `/devices/group/fire/protection`,
    method: 'post',
    data,
  });
}

export function updateFireDevice(data: EmptyObjectType) {
  return request({
    url: '/devices/group/fire/protection',
    method: 'put',
    data,
  });
}

export function deleteFireDevice(params: Object) {
  return request({
    url: `/devices/group/fire/protection`,
    method: 'delete',
    params,
  });
}

export const getMonitorDevices = (params: any) => {
  return request({
    url: '/api/monitor-devices',
    method: 'get',
    params,
  });
}

export const getOtherDevices = (params: any) => {
  return request({
    url: '/api/other-devices',
    method: 'get',
    params,
  });
}
@import '/src/theme/mixins/index.scss';
@import '/src/theme/utils.scss';

// 详情页面公共样式
// 用于 rankDetail 和 atlas 等详情页面的统一样式

// 页面容器 (不设置padding，由各页面自己控制)
.detail-page-container {
	width: 100%;
	height: 100%;
  padding: 0 vw(40);
}

// 标题区域 (包含返回按钮和面包屑)
.detail-page-title {
	margin: vh(20) 0;
	display: flex;
	align-items: center;
	
	.back {
		width: vw(22);
		cursor: pointer;
		margin-right: vw(10);
		transform: rotate(180deg);
	}
}

// 筛选区域 (包含筛选组件和按钮组)
.detail-page-filters {
	margin: vh(20) 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	
	.filter-group {
		display: flex;
		align-items: center;
	}
	
	.btn-group {
		display: flex;
		align-items: center;
	}
}

// 表格容器
.detail-page-table-box {
	display: flex;
	justify-content: center;
	width: 100%;
	flex-wrap: wrap;

	:deep(.el-table) {
		width: 100%;
		height: calc(100% - vw(42));
		border: 1px solid rgba(151, 151, 151, 1);
		background-color: transparent !important;
		--el-table-border-color: none;
		--el-table-current-row-bg-color: linear-gradient(
			to bottom,
			rgba(65, 169, 127, 0.9),
			rgba(65, 169, 127, 0.35)
		);

		.el-loading-mask {
			background-color: transparent;
		}
		
		.current-row {
			background-image: linear-gradient(to bottom, #000, rgba(65, 169, 127, 0.35));
		}

		tr {
			background-color: transparent !important;
		}

		tr:hover > td {
			background-color: transparent !important;
		}

		&::-webkit-scrollbar {
			width: 10px;
		}
		&::-webkit-scrollbar-thumb {
			background-color: #22d69f;
			border-radius: 0;
			border-left: 6px solid transparent;
		}
		&::-webkit-scrollbar-track-piece {
			background-color: transparent;
		}
	}
	
	.btn {
		display: block;
		width: 18px;
		height: 20px;
		cursor: pointer;
	}
}

// 详情页面分页样式
.detail-page-pagination {
	:deep(.el-pagination) {
		margin-top: vw(15);
		justify-content: center;
		
		.el-popper.is-light .el-popper__arrow::before {
			border-top-color: transparent !important;
			border-left-color: transparent !important;
		}
		
		.el-input__wrapper {
			border: 1px solid rgb(22, 90, 67) !important;
			background-color: transparent !important;
			box-shadow: none !important;
			padding: 0 11px !important;
		}
		
		.el-input__inner {
			color: rgba(255, 255, 255, 1);
		}

		.is-active {
			background-color: transparent !important;
			color: #fff !important;
			&::before {
				opacity: 0.65 !important;
			}
		}
		
		.el-pagination__total,
		.el-pagination__jump {
			color: rgba(255, 255, 255, 0.65);
		}
		
		.btn-prev,
		.btn-next,
		.number,
		.more {
			position: relative;
			background: transparent;
			color: #fff !important;
			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-image: linear-gradient(
					to bottom,
					rgba(87, 242, 91, 0.3),
					rgba(87, 242, 91, 0.3)
				);
				opacity: 0.2;
			}
		}
		
		.el-pager li {
			background-color: inherit !important;
		}

		.btn-next.is-disabled,
		.btn-next:disabled,
		.btn-prev.is-disabled,
		.btn-prev:disabled,
		.el-pager li.is-disabled,
		.el-pager li:disabled {
			background-color: inherit !important;
		}
		
		.el-popper {
			border: 1px solid $sd-screen-success !important;
			background-color: #0c1e29 !important;
			z-index: 2000;

			.el-select-dropdown__item {
				color: rgba(255, 255, 255, 1);
				padding: 0 10px !important;
			}

			.el-select-dropdown__item.hover,
			.el-select-dropdown__item:hover {
				background-color: transparent !important;
				color: $sd-screen-success;
			}

			.el-select-dropdown__item.selected {
				color: $sd-screen-success;
				font-weight: 700;
			}
		}

		.el-popper.is-light .el-popper__arrow::before {
			border: 1px solid $sd-screen-success !important;
			border-top-color: transparent !important;
			border-left-color: transparent !important;
			background: #010b07 !important;
		}
	}
}

// 加载遮罩公共样式
.detail-page-loading {
	:deep(.el-loading-mask) {
		background-color: transparent !important;
	}
	
	:deep(.el-loading-spinner .path) {
		stroke: $sd-screen-success !important;
	}
}
.no-data {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Atlas 页面特有的内容区域样式
.detail-page-content {
	// 滚动条样式
	:deep(.scrollbar__thumb) {
		background-color: $sd-screen-success;
	}
	
	// 物种项目容器
	
}

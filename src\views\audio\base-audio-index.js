
import AudioWorkletUrl from './base-audio-worklet.js'
import PCMPlayer from './pcm_player'

import { alaw } from 'alawmulaw'
// import { downloadUsingAnchorElement } from '@/utils/file-util'
import { ElMessage } from 'element-plus';

const isDebugger = false // 调试完设置false
let audioWorkletNode = null
let audioBuffer = []
let audioSize = 0
let WsInstance

const playSampleRate = 16000
const channels = 1

const constraints = {
  // audio: {
  //   channelCount: 1,
  //   sampleRate: 8000,
  //   sampleSize: 8,
  //   volume: 1.0
  // },
  audio: true,
  video: false
}
let audioContextInstance = null
let pcmPlayerInstance = null
let stream = null

let isMonitor = false
let isIntercom = false

// let flag = false

function convertInt16ToFloat32(int16Array) {
  const inputSamples = int16Array.length
  const output = new Float32Array(inputSamples)
  for (let i = 0; i !== inputSamples; ++i) {
    // output[i] = int16Array[i] / 32768
    const value = int16Array[i]
    // console.log(value, 'value')
    output[i] = value < 0 ? value / 32768 : value / 32767
  }
  return output
}

function float32ToInt16(float32) {
  const output = new Int16Array(float32)
  for (let i = 0, len = float32.length; i < len; i++) {
    const s = Math.max(-1, Math.min(1, float32[i]))
    output[i] = (s < 0 ? s * 0x8000 : s * 0x7FFF)
  }
  return output
}

function canRecording() {
  // debugger
  return navigator.mediaDevices
}

// function throwError(message) {
//   console.log('Error:' + message)
//   throw new Error(message)
// }

function audioTip() {
  const content =
    '<div>1.打开chrome浏览器，在地址栏输入 chrome://flags/#unsafely-treat-insecure-origin-as-secure</div>' +
    '<div>2.输入框中填写需要开启的ip（包含端口），譬如 http://**************，多个以逗号分隔</div>' +
    '<div>3.将Insecure origins treated as secure 切换成 enable 状态</div>' +
    '<div>4.在页面右下角显示Relaunch按钮，点击该按钮浏览器自动重启，并进入原有页面</div>' +
    '<div>5.检查chrome浏览器 设置->安全和隐私设置->网站设置->近期活动（选择对应ip）是否开启麦克风权限(需注意)</div>' +
    '<div>6.特别说明：台式电脑请使用头戴式的麦克风</div>'
  return content
}

function handleError(err) {
  // debugger
  let msg
  switch (err.code || err.name) {
    case 'NotAllowedError':
      // throwError('请打开操作系统和浏览器当前页面的录音权限')
      msg = '请打开操作系统和浏览器当前页面的录音权限'
      break
    default:
      // throwError('获取录音权限失败:' + (err.code || err.name))
      msg = '获取录音权限失败:' + (err.code || err.name)
      break
  }
  return msg
}

async function createAudioSource(context) {
  return context.createMediaStreamSource(stream)
}

async function onWsOpen() {
  // debugger
  if (!isIntercom) {
    return
  }
  window.AudioContext = window.AudioContext || window.webkitAudioContext
  const audioContext = new AudioContext({ sampleRate: 8000 })
  try {
    // console.log(AudioWorkletUrl)
    await audioContext.audioWorklet.addModule(AudioWorkletUrl)
  } catch (e) {
    console.log(e)
    return
  }
  audioWorkletNode = new window.AudioWorkletNode(audioContext, 'audio-processor')
  audioWorkletNode.port.onmessage = event => {
    audioBuffer = event.data.buffer
    audioSize = event.data.size
    const tempUint8Array = encodePcm()
    // debugger
    if (isDebugger) {
      // 调试下载录音文件用, { type: 'audio/pcm' }
      stopAudio()
      // const blob = new Blob([tempUint8Array])
      // const url = window.URL.createObjectURL(blob)
      // downloadUsingAnchorElement(url, 'g711a')
    } else {
      // ws 发送
      WsInstance.send(tempUint8Array)

      /* const blob = new Blob([tempUint8Array], { type: 'audio/g711a' })
      const url = window.URL.createObjectURL(blob)
      downloadUsingAnchorElement(url, 'g711a')
      stopAudio()*/
    }
  }
  const source = await createAudioSource(audioContext)
  // Connect the source node to the worklet
  source.connect(audioWorkletNode)
  // Create a splitter for the visualization
  // const splitter = audioContext.createChannelSplitter(source.channelCount)
  // Connect the worklet to the splitter
  // audioWorkletNode.connect(splitter)
  // Connect the worklet to the destination output (this is what you hear)
  audioWorkletNode.connect(audioContext.destination)

  audioContextInstance = audioContext
}

function decodeAudio(blob) {
  /* if (!flag) {

    flag = tru console.time('t')
    console.log(new Date().getTime(), 'timeBegin')e
  }*/
  // const step = 160
  blob.arrayBuffer().then((bytes) => {
    const audioData = new Uint8Array(bytes)
    const step = audioData.length
    for (let i = 0; i < audioData.byteLength; i += step) {
      // console.log(audioData.length)
      // const int16ArrayData = BaseWebassembly.decodeG711a(audioData.slice(i, i + step))
      // eslint-disable-next-line no-undef

      const int16ArrayData = alaw.decode(audioData.slice(i, i + step))
      const float32ArrayData = convertInt16ToFloat32(int16ArrayData)
      // debugger
      pcmPlayerInstance.feed(float32ArrayData)
    }
  })
}

function handleWsBlob(blob) {
  if (!isMonitor && !isIntercom) {
    return
  }
  decodeAudio(blob)
}

async function openIntercom(url) {
  /* const [auth, msg] = await audioAuthority()
  if (auth) {
    // 离线调式录音
    if (isDebugger) {
      onWsOpen().then(r => {})
    } else {
      isIntercom = true
      setupAudioGraph(url)
    }
    return [auth]
  } else {
    return [auth, msg]
  }*/
  // 离线调式录音
  if (isDebugger) {
    onWsOpen().then(r => { })
  } else {
    isIntercom = true
    setupAudioGraph(url)
  }
}

function closeIntercom() {
  isIntercom = false
}

async function openMonitor(url) {
  isMonitor = true
  setupAudioGraph(url)
}

function closeMonitor() {
  isMonitor = false
}

async function audioAuthority() {
  let msg
  // 判断是否是http环境
  if (!canRecording()) {
    msg = audioTip()
    // throw new Error('参照http录音配置')
    return [false, msg]
  } else {
    // 判断权限
    try {
      stream = await navigator.mediaDevices.getUserMedia(constraints)
    } catch (err) {
      msg = handleError(err)
      return [false, msg]
    }
  }
  return [true, stream]
}

async function createWsInstance(url) {
  // debugger
  WsInstance = new Ws(url)
  WsInstance.subscribe('onWsOpen', onWsOpen)
  WsInstance.subscribe('handleWsBlob', handleWsBlob)
  try {
    console.log('11111111111111');
    await WsInstance.start()
  } catch (e) {
    ElMessage.error('websocket连接建立失败，请检查网络正常后重试')
  }
}

function setupAudioGraph(url) {
  createWsInstance(url)
  pcmPlayerInstance = new PCMPlayer(channels, playSampleRate)
}

function stopAudio() {
  // debugger
  if (pcmPlayerInstance) {
    pcmPlayerInstance.close()
    pcmPlayerInstance = null
  }
  if (audioContextInstance) {
    try {
      audioContextInstance.close()
    } catch (error) {
      console.log('error closing context', error)
    }
    audioContextInstance = null

    stream.getTracks().forEach(track => track.stop())
  }
  isMonitor = false
  isIntercom = false
  if (WsInstance) {
    WsInstance.close()
    WsInstance = null
  }
}

// 调试用 begin

function getRawData() {
  // 合并
  const data = new Float32Array(audioSize)
  let offset = 0
  for (let i = 0; i < audioBuffer.length; i++) {
    // debugger
    data.set(audioBuffer[i], offset)
    offset += audioBuffer[i].length
  }
  return data
}

function getPcmBuffer() { // pcm buffer 数据
  // debugger
  const bytes = getRawData()
  const buffer = new ArrayBuffer(bytes.length * 2)
  const tempOutput = new DataView(buffer)
  return new Blob([tempOutput], { type: 'audio/pcm' })
}

function getG711aBuffer() {
  // const tempUint8Array = encodePcm()
  // const buffer = new ArrayBuffer(tempUint8Array.length)
  // const tempOutput = new DataView(buffer)
  // return new Blob([tempOutput], { type: 'audio/g711a' })
}

// pcm 文件
const pcmSrc = () => {
  return window.URL.createObjectURL(getPcmBuffer())
}

// g711a 文件
const g711aSrc = (g711aData) => {
  return window.URL.createObjectURL(getG711aBuffer())
}

// channelNum {Number} 声道数
// sampleRate {Number} PCM数据的采样率
// samples {int16Array} 16位有符号的PCM数据
const encodeMp3 = (samples, channelNum = 1, sampleRate = 8000) => {
  // debugger
  const buffer = []
  let size = 0
  // 128是指输出的MP3音频的比特率bitrate，单位是kbps
  // eslint-disable-next-line no-undef
  const mp3enc = new lamejs.Mp3Encoder(channelNum, sampleRate, 128)
  let remaining = samples.length
  const maxSamples = 1152
  for (let i = 0; remaining >= maxSamples; i += maxSamples) {
    const mono = samples.subarray(i, i + maxSamples)
    const mp3buf = mp3enc.encodeBuffer(mono)
    if (mp3buf.length > 0) {
      const tempInt8Array = new Int8Array(mp3buf)
      size += tempInt8Array.length
      buffer.push(tempInt8Array)
    }
    remaining -= maxSamples
  }
  const flushData = mp3enc.flush()
  if (flushData.length > 0) {
    const tempInt8Array = new Int8Array(flushData)
    size += tempInt8Array.length
    buffer.push(tempInt8Array)
  }
  // debugger
  // 推送
  return mergeInt8Array(buffer, size)
  // 调试用，下载编码后的MP3
  /* const blob = new Blob(buffer, { type: 'audio/mp3' })
  const bUrl = window.URL.createObjectURL(blob)
  return bUrl*/
  // console.log('Blob created, URL:', bUrl)
  /* window.myAudioPlayer = document.createElement('audio')
  window.myAudioPlayer.src = bUrl
  window.myAudioPlayer.setAttribute('controls', '')
  window.myAudioPlayer.play()*/
}

const encodePcm = () => {
  // debugger
  const bytes = getRawData()
  const tempOutBuffer = float32ToInt16(bytes)
  /* const buffer = new ArrayBuffer(bytes.length * 2)
  const tempOutput = new DataView(buffer)
  // 调试用
  for (let i = 0, offset = 0; i < bytes.length; i++, offset += 2) {
    // 保证采样帧的值在-1到1之间
    const s = Math.max(-1, Math.min(1, bytes[i]))
    // 将32位浮点映射为16位整形 值
    // 16位的划分的是 2^16=65536，范围是-32768到32767
    //  获取到的数据范围是[-1,1]之间，所以要转成16位的话，需要负数*32768，正数*32767，就可以得到[-32768，32767]范围内的数据
    // 第三个参数，true 含义是  是否是小端字节序 这里设置为true
    tempOutput.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true)
  }
  const tempOutBuffer = new Int16Array(tempOutput.buffer)*/

  // console.log(tempOutBuffer)
  // 转g711a
  // return BaseWebassembly.encodeG711a(tempOutBuffer)
  // eslint-disable-next-line no-undef
  return alaw.encode(tempOutBuffer)
  // return encodeMp3(tempOutBuffer)
}
function mergeInt8Array(buffer, size) {
  // 合并
  const data = new Int8Array(size)
  let offset = 0
  for (let i = 0; i < buffer.length; i++) {
    // debugger
    data.set(buffer[i], offset)
    offset += buffer[i].length
  }
  return data
}


class Ws {
  constructor(url) {
    this.url = url;
    this.ws = null;
    this.eventHandlers = {
      onWsOpen: null,
      handleWsBlob: null
    };
  }

  // 订阅事件
  subscribe(eventName, handler) {
    if (eventName in this.eventHandlers) {
      this.eventHandlers[eventName] = handler;
    }
  }

  // 启动 WebSocket 连接
  async start() {
    return new Promise((resolve, reject) => {
      if (this.ws) {
        this.ws.close(); // 如果已经存在连接，则关闭旧连接
      }
      this.ws = new WebSocket(this.url);

      this.ws.onopen = () => {
        if (this.eventHandlers.onWsOpen) {
          this.eventHandlers.onWsOpen();
        }
        resolve();
      };

      this.ws.onmessage = (event) => {
        if (this.eventHandlers.handleWsBlob) {
          this.eventHandlers.handleWsBlob(event.data);
        }
      };

      this.ws.onerror = (error) => {
        reject(error);
      };

      this.ws.onclose = () => {
        console.log('WebSocket connection closed');
      };
    });
  }

  // 发送消息
  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(data);
    } else {
      console.error('WebSocket is not connected');
    }
  }

  // 关闭连接
  close() {
    if (this.ws) {
      this.ws.close();
    }
  }
}


// 调试用 end

export {
  setupAudioGraph,
  stopAudio,
  pcmSrc,
  encodePcm,
  openIntercom,
  closeIntercom,
  openMonitor,
  closeMonitor,
  g711aSrc,
  onWsOpen,
  encodeMp3,
  audioAuthority
}

<template>
  <div class="species">
    <div
      class="species-item animate__animated"
      :class="item.isNewData ? animationName : ''"
      v-for="(item, $index) in recentMonitorEvents"
      :key="item.eventId"
      @animationend="onNewDataAnimationend"
    >
      <div class="picture">
        <el-image
          :src="item.pictureUrl || item.oriPictureThumbnailUrl || ''" fit="cover"
          :preview-src-list="previewSrcList"
          :initial-index="$index"
          :preview-teleported="true"
          title="查看大图"
        >
          <template #placeholder>
            <div class="image-placeholder">
              <el-icon class="is-loading">
                <ele-Loading />
              </el-icon>
            </div>
          </template>
          <template #error>
            <div class="load-error">
              <img
                class="small-img-error"
                src="/src/assets/fullScreen/small-load-error.png"
                title="加载失败"
                alt=""
              />
            </div>
          </template>
        </el-image>
      </div>
      <div class="desc">
        <div class="name">{{ item.alias }}</div>
        <div>
          <img src="/src/assets/sd/data-screen/recent-dw.png" alt="">
          点位：{{ item.deviceName }}
        </div>
        <div>
          <img src="/src/assets/sd/data-screen/recent-sj.png" alt="">
          时间：{{ item.discoveredTime }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch, computed } from 'vue';

const props = defineProps<{
  animationName: string;
  recentMonitorEvents: RecentMonitorEvent[];
}>();

const previewSrcList = computed(() => {
  return props.recentMonitorEvents?.map((item: RecentMonitorEvent) => {
    return item.pictureUrl || item.oriPictureUrl;
  })
});


const onNewDataAnimationend = (event: Event) => {
  const animationEle = event.target as HTMLDivElement;
  if (animationEle?.classList.contains(props.animationName)) {
    animationEle?.classList.remove(props.animationName);
  }
};

watch(() => props.recentMonitorEvents, () => {
  const speciesEle = document.querySelector('.species');
  speciesEle?.scrollTo({
    top: 0,
    left: 0,
    behavior: 'smooth',
  })
});
</script>

<style lang="scss" scoped>
@import '/src/theme/mixins/index.scss';

.species {
  max-height: vw(492);
  // @include sd-scrollBar;
  .species-item {
    display: flex;
    overflow: hidden;
    .picture {
      width: vw(108);
      height: vw(108);
      .el-image {
        width: 100%;
        height: 100%;
        cursor: pointer;
        transition: transform 0.2s;
      }
      .el-image:hover {
        transform: scale(1.02);
      }
    }
    .desc {
      flex: 1;
      background: linear-gradient(to right, rgba(33, 99, 89, 0.3) 10%, rgba(30, 41, 56, 0), transparent);
      padding: vw(10);
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .name {
        font-size: vw(18);
      }
      img {
        width: vw(13);
        vertical-align: middle;
      }
    }
  }
  .species-item + .species-item {
    margin-top: vw(20);
  }
}
</style>
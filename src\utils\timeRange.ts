import dayjs from 'dayjs';

/**
 * 根据TimeType计算对应的时间范围
 * @param timeType 时间类型：1(近一月) | 2(近三月) | 3(近一年) | 'all'(累计)
 * @returns 时间范围对象 { startDateTime: string, endDateTime: string }
 */
export function calculateTimeRange(timeType: TimeType): { startDateTime: string; endDateTime: string } {
  // 如果是累计，返回空时间范围
  if (timeType === 'all') {
    return {
      startDateTime: '',
      endDateTime: ''
    };
  }

  const now = dayjs();
  const endDateTime = now.format('YYYY-MM-DD 23:59:59');
  let startDateTime = '';

  switch (timeType) {
    case 1: // 近一月
      startDateTime = now.subtract(1, 'month').format('YYYY-MM-DD 00:00:00');
      break;
    case 2: // 近三月
      startDateTime = now.subtract(3, 'month').format('YYYY-MM-DD 00:00:00');
      break;
    case 3: // 近一年
      startDateTime = now.subtract(1, 'year').format('YYYY-MM-DD 00:00:00');
      break;
    default:
      // 默认情况返回空时间范围
      return {
        startDateTime: '',
        endDateTime: ''
      };
  }

  return {
    startDateTime,
    endDateTime
  };
}

/**
 * 根据TimeType获取时间范围的显示文本
 * @param timeType 时间类型
 * @returns 显示文本
 */
export function getTimeRangeLabel(timeType: TimeType): string {
  switch (timeType) {
    case 1:
      return '近一月';
    case 2:
      return '近三月';
    case 3:
      return '近一年';
    case 'all':
      return '累计';
    default:
      return '';
  }
}

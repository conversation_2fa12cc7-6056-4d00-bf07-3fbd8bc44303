// 高德地图图层
import TileLayer from 'ol/layer/Tile.js';
import XYZ from 'ol/source/XYZ.js';
import { customTileLoadFun } from '../utils';

type MapColor = 'gray' | 'blue' | 'darkgreen' | 'black';

export const gaodekey = 'ee95e52bf08006f63fd29bcfbcf21df0';

/**
 * 生成地图资源
* @param style 6：影像 7：矢量图层+标注  8：标注  
*/
const layerSource = (style: number, mapColor?: MapColor) => {
  return new XYZ({
    // projection: gcj02Mecator,
    url: `http://wprd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=256&scale=1&style=${style}&x={x}&y={y}&z={z}`,
    wrapX: false,
    tileLoadFunction: (imageTile: any, src: string) => {
      if (mapColor) {
        customTileLoadFun(imageTile, src, mapColor);
      } else {
        imageTile.getImage().src = src;
      }
    },
  });
};

export default [
  new TileLayer({
    visible: true,
    source: layerSource(7, 'darkgreen'),
  }),
];

<template>
	<div class="event-details-detail-container" v-if="visible">
		<div class="main">
			<span class="colse" @click="onClose"></span>
			<div class="left-box">
				<img class="title-img" src="/src/assets/eventDetails/dig/tupian.png" alt="" />
				<div class="big-img" v-if="props.perviewPicList.length">
					<el-image
						v-if="props.perviewPicList[picIndex]"
						class="thumbnail"
						fit="cover"
						:src="props.perviewPicList[picIndex]"
						:preview-src-list="props.perviewPicList"
						:initial-index="picIndex"
						lazy
						@show="handleImageShow"
						@close="handleImageClose"
						@switch="handleImageSwitch"
					>
						<template #placeholder>
							<div class="image-placeholder">
								<el-icon class="is-loading">
									<ele-Loading />
								</el-icon>
							</div>
						</template>
						<template #error>
							<div class="load-error">
								<img
									class="big-img-error"
									src="/src/assets/fullScreen/big-load-error.png"
									title="加载失败"
									alt=""
								/>
							</div>
						</template>
					</el-image>

					<div class="prev" @click="onPrev()"></div>
					<div class="next" @click="onNext()"></div>
				</div>
			</div>
			<div class="right-box">
				<img class="title-img" src="/src/assets/eventDetails/dig/xinxi.png" alt="" />
				<div class="info-box">
					<div>
						<span
							v-if="info?.monitorEventDetails && info.monitorEventDetails.length"
							v-for="res in info.monitorEventDetails"
							:key="res.recResult"
						>
							{{ info.pointName }}
							{{ res.recResult }}
							<template v-if="info.eventType < 2">
								{{ res.recResultCnt && `（${res.recResultCnt}）` }}
							</template>
						</span>
					</div>
					<div>
						<img width="13" height="13" src="/src/assets/eventDetails/dig/leixing.png" alt="" />
						{{ info.parentEventTypeName }}
					</div>
					<div>
						<img width="13" height="13" src="/src/assets/eventDetails/dig/shichang.png" alt="" />
						{{ extractTimePart(info.recTime, 'MM-DD HH:mm:ss') }}
					</div>
					<div>
						<img width="13" height="15" src="/src/assets/eventDetails/dig/zuobiao.png" alt="" />
						{{ info.longitude }} {{ info.latitude }}
					</div>
				</div>
				<img class="title-img" src="/src/assets/eventDetails/dig/weizhi.png" alt="" />
				<div class="location-box">
					<el-amap
						style="width: 100%; height: 300px"
						ref="mapRef"
						:center="center"
						viewMode="3D"
						mapStyle="amap://styles/blue"
						:zoom="zoom"
						:rotateEnable="true"
						:pitchEnable="true"
						:animateEnable="mapAnimateEnable"
						:features="['bg', 'point', 'road', 'building']"
						@init="mapInit"
					>
						<el-amap-marker
							:position="marker.position"
							:draggable="false"
							:zIndex="marker.zIndex"
							:offset="[-26, -81]"
						>
							<div>
								<img
									:src="marker.markerUrl"
									width="52"
									height="81"
									style="object-fit: contain; display: block"
									:alt="marker.recResult"
									draggable="false"
								/>
								<div class="marker-label">
									{{ marker.recResult }}
								</div>
							</div>
						</el-amap-marker>
					</el-amap>
				</div>
			</div>

			<div class="artwork-download">
				<el-button type="primary" @click.stop="handleArtworkDownload">下载原图</el-button>
			</div>
		</div>

		<div v-if="dragData.show" v-drag class="viewer-remark">
			{{ dragData.remark }}
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick, watchEffect, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { extractTimePart } from '/@/utils/formatTime';
const props = defineProps({
	perviewPicList: { type: Array, required: true },
	perviewPicIndex: { type: Number, required: true },
	eventDatas: { type: Array, required: true },
	markers: { type: Array<MarkerInfo>, required: true },
});
// const center = ref<number[]>();
const zoom = ref<number>(15);
const amap = ref<AMap.Map>();
const visible = ref(false);
const mapAnimateEnable = ref(false);
const mapInit = (m: AMap.Map) => {
	amap.value = m;
};

const picIndex = ref(0);
watchEffect(() => {
	try {
		if (props.perviewPicIndex) {
			picIndex.value = props.perviewPicIndex;
		}
	} catch (error) {
		console.log(error);
	}
});

const info: any = computed(() => {
	return props.eventDatas[picIndex.value];
});
const marker = computed(() => {
	return props.markers[picIndex.value];
});
const center = computed(() => {
	const position = marker.value.position;
	if (position.length < 2) {
		return ElMessage.error('位置异常');
	} else {
		return position;
	}
});
const dragData = ref({
	remark: '',
	show: false,
});
const handleImageShow = () => {
	console.log('handleImageShow', props.perviewPicIndex);
	const item: any = props.eventDatas[props.perviewPicIndex];

	if (item.groupEventType === 1) {
		dragData.value.show = true;
		dragData.value.remark =
			(item.monitorEventDetails?.length && item.monitorEventDetails[0]?.remark) || '';
	}
};
const handleImageClose = () => {
	dragData.value.show = false;
};
const handleImageSwitch = (index: number) => {
	const item: any = props.eventDatas[index];
	if (item.groupEventType === 1) {
		dragData.value.show = true;
		dragData.value.remark =
			(item.monitorEventDetails?.length && item.monitorEventDetails[0]?.remark) || '';
	} else {
		dragData.value.show = false;
	}
};

const onPrev = () => {
	picIndex.value--;
	if (picIndex.value <= 0) {
		picIndex.value = props.perviewPicList.length - 1;
	}
};

const onNext = () => {
	picIndex.value++;
	if (picIndex.value > props.perviewPicList.length - 1) {
		picIndex.value = 0;
	}
};

const onClose = () => {
	visible.value = false;
};

const onOpen = (index: number) => {
	visible.value = true;
	picIndex.value = index;
};

const handleArtworkDownload = () => {
	let image = new Image();
	image.src = info.value.oriPictureUrl;
	image.setAttribute('crossOrigin', 'anonymous');
	image.onload = function () {
		let canvas = document.createElement('canvas');
		canvas.width = image.width;
		canvas.height = image.height;
		let context = canvas.getContext('2d');
		context!.drawImage(image, 0, 0, image.width, image.height);
		let url = canvas.toDataURL('image/png');
		let a = document.createElement('a');
		let event = new MouseEvent('click');
		a.download =
			`${info.value.pointName} ${info.value.monitorEventDetails[0].recResult}-${info.value.recTime}` ||
			'下载图片名称';
		a.href = url;
		a.dispatchEvent(event);
	};
};

onMounted(() => {});
onUnmounted(() => {
	console.log('1111111111');
	amap.value?.destroy();
});
defineExpose({
	onOpen,
});
</script>

<style lang="scss" scoped>
.user-select {
	-webkit-user-select: none; /* Safari */
	-moz-user-select: none; /* Firefox */
	-ms-user-select: none; /* IE/Edge */
	user-select: none; /* 标准语法 */
}
.event-details-detail-container {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 99;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.3);
	display: flex;
	justify-content: center;
	align-items: center;
	.main {
		position: relative;
		width: 1512px;
		height: 672px;
		background-color: rgba(0, 0, 0, 1);
		background-image: url('/src/assets/eventDetails/dig/dig-bg.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		padding: 32px 30px;
		display: flex;

		.colse {
			@extend .user-select;
			position: absolute;
			top: 24px;
			right: 24px;
			width: 18px;
			height: 18px;
			z-index: 999;
			background-image: url('/src/assets/eventDetails/dig/close.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			cursor: pointer;
		}
		.title-img {
			width: 66px;
			height: 29px;
			margin-bottom: 11px;
		}
		.left-box {
			width: 1008px;
			height: 100%;
			margin-right: 16px;
			.big-img {
				position: relative;
				width: 100%;
				height: calc(100% - 66px);
				.thumbnail {
					width: 100%;
					height: 100%;
					.image-placeholder {
						width: 100%;
						height: 100%;
						display: flex;
						justify-content: center;
						align-items: center;
						.is-loading {
							color: #22d69f;
							font-size: 20px;
						}
					}
				}
				.p-n-button {
					@extend .user-select;
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 44px;
					height: 44px;
					border-radius: 50%;
					background-repeat: no-repeat;
					background-size: 100% 100%;
					cursor: pointer;
				}
				.prev {
					@extend .p-n-button;
					left: 27px;
					background-image: url('/src/assets/eventDetails/dig/prev.png');
				}
				.next {
					@extend .p-n-button;
					right: 27px;
					background-image: url('/src/assets/eventDetails/dig/next.png');
				}
			}
		}
		.right-box {
			flex: 1;
			.info-box {
				width: 428px;
				height: 159px;
				padding: 10px 10px 0;
				color: #fff;
				display: flex;
				flex-direction: column;
				margin-bottom: 20px;
				background-image: url('/src/assets/eventDetails/dig/xinxi-bg.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
				div {
					font-size: 14px;
					height: 20px;
					line-height: 20px;
					margin-bottom: 16px;
					&:first-child {
						height: 25px;
						font-size: 18px;
						line-height: 25px;
						margin-bottom: 20px;
					}
					&:last-child {
						margin-bottom: 0;
					}
				}

				img {
					margin-right: 6px;
					vertical-align: middle;
				}
			}

			.location-box {
				padding: 12px;
				height: 322px;
				background-image: url('/src/assets/eventDetails/dig/weizhi-bg.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;

				.marker-label {
					color: #fff;
					font-size: 12px;
					text-align: center;
					white-space: nowrap;
				}
			}
		}

		.artwork-download {
			position: absolute;
			right: 32px;
			bottom: 10px;
			&:deep(.el-button) {
				position: relative;
				background-color: transparent;
				&:last-child {
					background-image: linear-gradient(
						to bottom,
						rgba(24, 96, 71, 0.65),
						rgba(65, 169, 127, 0.65)
					);
				}
			}
		}
	}
}
</style>

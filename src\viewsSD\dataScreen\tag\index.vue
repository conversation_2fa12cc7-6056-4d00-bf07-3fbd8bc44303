<template>
	<div class="main-container" :class="state.mainType === 1 ? 'map' : ''">
		<LeftCon
			:animalTag="state.animalTag"
			:animalNameList="state.selectedNames"
			:timeType="state.dateRange"
		></LeftCon>
		<!-- # -->
		<div class="center-container">
			<div class="filter-container">
				<!-- select  -->
				<el-select
					class="data-screen-select"
					v-model="state.selectedNames"
					placeholder="请选择"
					:collapse-tags="true"
					:multiple="true"
					:teleported="false"
				>
					<el-option v-for="item in state.speciesOptions" :key="item.animalId" :value="item.name">
						<div class="custom-option">
							<span>{{ item.name }}</span>
							<el-radio
								class="data-screen-radio"
								:model-value="state.selectedNames.includes(item.name)"
								:label="true"
							>
							</el-radio>
						</div>
					</el-option>
				</el-select>
				<!-- date range -->
				<DateRange :dateRange="state.dateRange" @dateRangeChange="onDateRangeChange"></DateRange>
				<!-- toggle button -->
				<ToggleButton :mainType="state.mainType" @mainTypeChange="onMainTypeChange"></ToggleButton>
			</div>
			<Map
				v-show="state.mainType === 1"
				:animalTag="state.animalTag"
				:animalNameList="state.selectedNames"
				:timeType="state.dateRange"
			></Map>
			<Images
				v-show="state.mainType === 2"
				:animalTag="state.animalTag"
				:animalNameList="state.selectedNames"
				:timeType="state.dateRange"
			></Images>
		</div>
		<!-- # -->
		<RightCon
			:animalTag="state.animalTag"
			:animalNameList="state.selectedNames"
			:timeType="state.dateRange"
		></RightCon>
	</div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue';
import { useRoute } from 'vue-router';
import { Bird_Tag } from '/@/utils/constants';
import { getAnimals } from '/@/api/species';
import type { Species } from '/@/views/species/type';
// 子组件
import LeftCon from './leftCon.vue';
import RightCon from './rightCon.vue';
import DateRange from '../component/dateRange/index.vue';
import ToggleButton from '../component/toggleButton/index.vue';
import Map from '../component/map/index.vue';
// import Map from '../component/map/index2.vue';
import Images from '../component/images/index.vue';

const route = useRoute();
const state = reactive({
	animalTag: 0, // 物种类型 0:内蒙古鸟 1:特殊 2:珍稀
	speciesOptions: [] as Species[],
	selectedNames: [] as string[],
	dateRange: 1 as number | null, // 时间类型 1.近一月 2.近三月 3.近一年 null.累计
	mainType: 1, // 1地图 2图集
});

// 获取物种列表
const getSpeciesOptions = async () => {
	const query = {
		tag: state.animalTag,
	};
	const { payload } = await getAnimals(query);
	state.speciesOptions = payload;
};
// 日期范围改变
const onDateRangeChange = (value: number | null) => {
	state.dateRange = value;
};
// 切换
const onMainTypeChange = (value: number) => {
	state.mainType = value;
};

watch(
	() => route.path,
	(value: string) => {
		Bird_Tag.forEach((item) => {
			if (value.includes(item.key)) {
				state.animalTag = item.value;
			}
		});
		getSpeciesOptions();
	},
	{ immediate: true }
);
</script>

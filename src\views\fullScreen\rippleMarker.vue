<template>
	<div class="circle-marker-content">
		<div class="item item1"></div>
		<div class="item item2"></div>
		<div class="item item3"></div>
	</div>
</template>

<script lang="ts" setup name="rippleMarker">
import { ref } from 'vue';
import { onMounted, onUnmounted, reactive } from 'vue';
</script>

<style lang="scss" scoped>
$circleColor: #fff;
$circleColorShadow: #ff0000;

.circle-marker-content {
	height: 16px;
	width: 16px;
	border-radius: 100%;
	text-align: center;
	background: $circleColorShadow;
	box-shadow: 0 0 14px $circleColor;
	// transform-style: preserve-3d;
	// perspective: 1200px;
	// transform-origin: center;
	// transform: rotateX(10deg) rotateY(70deg);
	@keyframes scaless {
		0% {
			transform: scale(1);
			opacity: 1;
		}
		100% {
			transform: scale(4);
			opacity: 0;
		}
	}

	.item {
		width: 100%;
		height: 100%;
		position: absolute;
		border-radius: 100%;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		opacity: 0.8;
	}

	.item:before {
		content: '';
		position: absolute;
		left: -1px;
		top: -1px;
		display: inline-block;
		width: 100%;
		height: 100%;
		border-radius: 100%;
		opacity: 0.6;
		background-color: $circleColorShadow;
		box-shadow: 0 0 14px $circleColor;
		animation: scaless 4s infinite cubic-bezier(0, 0, 0.49, 1.02);
	}

	.item1:before {
		animation-delay: 0s;
	}

	.item2:before {
		animation-delay: 2s;
	}

	.item3:before {
		animation-delay: 4s;
	}
}
</style>

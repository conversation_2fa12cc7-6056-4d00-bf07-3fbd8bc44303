import { nextTick } from 'vue';
import '/@/theme/loading.scss';

/**
 * 页面全局 Loading
 * @method start 创建 loading
 * @method done 移除 loading
 */
export const NextLoading = {
  // 创建 loading
  start: () => {
    const bodys: Element = document.body;
    const div = <HTMLElement>document.createElement('div');
    div.setAttribute('class', 'loader-wrapper');
    // const htmls = `
    // 	<div class="loader-wrapper-box">
    // 		<div class="loader-wrapper-box-warp">
    // 			<div class="loader-wrapper-box-item"></div>
    // 			<div class="loader-wrapper-box-item"></div>
    // 			<div class="loader-wrapper-box-item"></div>
    // 			<div class="loader-wrapper-box-item"></div>
    // 			<div class="loader-wrapper-box-item"></div>
    // 			<div class="loader-wrapper-box-item"></div>
    // 			<div class="loader-wrapper-box-item"></div>
    // 			<div class="loader-wrapper-box-item"></div>
    // 			<div class="loader-wrapper-box-item"></div>
    // 		</div>
    // 	</div>
    // `;
    const htmls = `
			<div class="loader"></div>
			<div class="loader-section section-left"></div>
			<div class="loader-section section-right"></div>
			<div class="loader-title">正在加载系统资源，请耐心等待</div>
		`;
    div.innerHTML = htmls;
    bodys.insertBefore(div, bodys.childNodes[0]);
    window.nextLoading = true;
  },
  // 移除 loading
  done: (time: number = 0) => {
    nextTick(() => {
      setTimeout(() => {
        window.nextLoading = false;
        const el = <HTMLElement>document.querySelector('.loader-wrapper');
        el?.parentNode?.removeChild(el);
      }, time);
    });
  },
};

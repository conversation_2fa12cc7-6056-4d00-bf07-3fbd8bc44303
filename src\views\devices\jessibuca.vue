<template>
	<div
		class="player"
		:id="props.deviceId"
		ref="container"
		:style="{ backgroundImage: playShow ? 'url(' + props.bgImg + ')' : '' }"
	>
		<span class="video-title">{{ props.name }}</span>
		<span v-if="playShow" class="video-play" @click="play"></span>
		<el-tooltip
			ref="tooltipRef"
			trigger="click"
			effect="dark"
			placement="top-end"
			popper-class="handle-tooltip"
			:append-to="container"
		>
			<i
				v-show="props.ifControl !== 0 && kongzhiControlShow"
				ref="kongzhiControl"
				class="iconfont icon-kongzhi font16 kongzhiControl"
			></i>
			<template #content>
				<div class="handle">
					<div class="handle-keys">
						<div class="key top" @click="onHandleKeyAction(21)">
							<img src="/src/assets/sd/rvc/handle-top.png" alt="" title="上仰" />
						</div>
						<div class="key bottom" @click="onHandleKeyAction(22)">
							<img src="/src/assets/sd/rvc/handle-bottom.png" alt="" title="下俯" />
						</div>
						<div class="key left" @click="onHandleKeyAction(23)">
							<img src="/src/assets/sd/rvc/handle-left.png" alt="" title="左转" />
						</div>
						<div class="key right" @click="onHandleKeyAction(24)">
							<img src="/src/assets/sd/rvc/handle-right.png" alt="" title="右转" />
						</div>
						<!-- <div class="key motion">
                    <img src="/src/assets/sd/rvc/handle-refresh.png" alt="">
                  </div> -->
						<div class="key add" @click="onHandleKeyAction(11)">
							<img src="/src/assets/sd/rvc/handle-add.png" alt="" title="焦距放大" />
						</div>
						<div class="key reduce" @click="onHandleKeyAction(12)">
							<img src="/src/assets/sd/rvc/handle-reduce.png" alt="" title="焦距缩小" />
						</div>
					</div>
				</div>
			</template>
		</el-tooltip>
	</div>
</template>

<script setup lang="ts">
import { ref, onDeactivated, onUnmounted, onActivated } from 'vue';
import { generatePlayUrl, controlCamera } from '/@/api/devices';

const props = defineProps({
	deviceId: { type: String, required: true },
	name: { type: String, required: true },
	bgImg: { type: String, required: true },
	ifControl: { type: Number, required: true, default: 0 },
	manufacturer: { type: Number, required: true },
	deviceRtsp: { type: String, required: true },
});
const container = ref();
const tooltipRef = ref();
const kongzhiControl = ref();
const kongzhiControlShow = ref(false);
const playShow = ref(true);
let jessibuca: any;
let playUrl: string;
const initJessibuca = () => {
	jessibuca = new (window as any).Jessibuca({
		container: container.value,
		videoBuffer: 0.2, // 缓存时长
		isResize: true,
		isFullResize: true,
		isFlv: true,
		loadingText: '视频加载中，请稍候',
		heartTimeout: 3,
		loadingTimeout: 10,
		decoder: '/jessibuca/decoder.js',
		hasAudio: true,
		debug: false,
		operateBtns: {
			fullscreen: true,
			screenshot: false,
			play: true,
			audio: false,
			record: false,
		},
		forceNoOffscreen: false,
		isNotMute: true,
		useWebFullScreen: true,
		controlAutoHide: true,
	});
	jessibuca.setScaleMode(1);
	jessibuca.on('start', function () {
		console.log('start render');
		kongzhiControlShow.value = true;
	});
	jessibuca.on('pause', function () {
		tooltipRef.value?.hide();
		kongzhiControlShow.value = false;
	});
	jessibuca.on('error', function (error: any) {
		console.log('error', error);
		destoryJessibuca();
		playShow.value = true;
		kongzhiControlShow.value = false;
	});

	let leftElement = container.value.querySelector('.jessibuca-controls-left');
	let rightElement = container.value.querySelector('.jessibuca-controls-right');
	swapChildren(rightElement, leftElement);

	leftElement.appendChild(kongzhiControl.value);

	// tooltipRef.value.onOpen();

	// tooltipRef.value.addEventListener('mouseenter', handleMouseEnter);
	// tooltipRef.value.addEventListener('mouseleave', handleMouseLeave);

	// 创建一个新的div元素
	// controlDiv = document.createElement('div');

	// // 设置新div的类名和样式
	// controlDiv.className = 'jessibuca-controls-item jessibuca-control';
	// // controlDiv.style.display = 'none';

	// // 创建一个i元素，并设置类名
	// var newIcon = document.createElement('i');
	// newIcon.className = 'jessibuca-icon jessibuca-icon-control';

	// // 创建一个span元素，用于包含图标标题提示
	// var newIconTitleTips = document.createElement('span');
	// newIconTitleTips.className = 'icon-title-tips';

	// // 创建一个span元素，用于包含图标标题
	// var newIconTitle = document.createElement('span');
	// newIconTitle.className = 'icon-title';
	// newIconTitle.textContent = '6666';

	// // 将图标标题添加到图标标题提示span中
	// newIconTitleTips.appendChild(newIconTitle);

	// // 将图标和图标标题提示添加到新的div中
	// controlDiv.appendChild(newIcon);
	// controlDiv.appendChild(newIconTitleTips);
	// var lastChild = descendantElement.lastElementChild;
	// // lastChild.insertAdjacentElement('afterend', controlDiv);
	// // descendantElement.appendChild(newDiv);
	// console.log('descendantElement', descendantElement);
};

const play = () => {
	playShow.value = false;
	initJessibuca();
	playUrl = generatePlayUrl(<string>props.deviceId);
	jessibuca.play(playUrl);
	// jessibuca.play('ws://sd.nbzhu.cn/zlmediakit-server/monitor/1858770012462907392.live.flv');
};
// 云台控制，按指令`commandType`控制设备
const onHandleKeyAction = async (commandType: number) => {
	await controlCamera({
		commandType,
		deviceRtsp: props.deviceRtsp,
		manufacturer: props.manufacturer,
	});
};

const destoryJessibuca = () => {
	jessibuca && jessibuca.destroy();
	jessibuca = null;
};

const handleMouseEnter = () => {
	console.log('鼠标移入');
};
const handleMouseLeave = () => {
	console.log('鼠标移出');
};
onActivated(() => {
	playShow.value = true;
});
onDeactivated(() => {
	destoryJessibuca();
});
onUnmounted(() => {
	destoryJessibuca();
});

defineExpose({
	play,
});

function swapChildren(child1: HTMLElement, child2: HTMLElement) {
	// 保存两个子级的父节点
	var parent1 = child1.parentNode;
	var parent2 = child2.parentNode;
	// 交换两个子级
	parent1!.replaceChild(child2, child1);
	parent2!.replaceChild(child1, child2);
	// 将原始的第二个���级移动到第一个子级原来的位置
	parent1!.insertBefore(child2, child1.nextSibling);
}
</script>

<style>
.jessibuca-icon-control {
	background: url('data:image/svg+xml;base64,PHN2ZyB0PSIxNzMzMzg4NTE3NTQ0IiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjExMzgwIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTEwMTYuODgxMzI2IDUyOS4xODQxMSAxMDA4LjM5MzM2OSA1MzcuMjg3NzA3QzEwMDguMjQ5MjM0IDUzNy40NDc4NTcgMTAwOC4wMjUwMjMgNTM3LjQ3OTg4NyAxMDA3Ljg2NDg3MyA1MzcuNjI0MDIzTDg4MC4wMDEwMDEgNjUzLjIzNjQwOUM4NzAuNjQ4MjMzIDY2Mi4xODg4MDIgODU1LjUxNDA0NCA2NjIuMTg4ODAyIDg0Ni4xMjkyNDYgNjUzLjIzNjQwOUw4NDAuNjUyMTExIDY0NS4xNjQ4NDJDODMxLjI5OTM0MyA2MzYuMjI4NDY0IDgzNC4zMjYxODEgNjIxLjcxODg2MSA4NDMuNjc4OTQ5IDYxMi43NjY0NjlMOTIwLjg3MTMxNyA1NDMuNTQ5NTc4IDU0Ni41MzYzNzggNTQzLjU0OTU3OCA1NDYuNTM2Mzc4IDkxOC4yNTI4NjIgNjE1Ljk0NTQ0OSA4NDEuNzAxMDk1QzYyNC44OTc4NDIgODMyLjM0ODMyNyA2MzkuMzc1NDE0IDgyOS4zMDU0NzQgNjQ4LjMyNzgwNyA4MzguNjU4MjQyTDY1Ni4zOTkzNzQgODQ0LjEzNTM3N0M2NjUuMzM1NzUyIDg1My41MjAxNzUgNjY1LjMzNTc1MiA4NjguNjU0MzYzIDY1Ni4zOTkzNzQgODc4LjAwNzEzMkw1MzcuODQwMjI1IDEwMDcuODg4ODk2QzUzNy42ODAwNzUgMTAwOC4wNjUwNjEgNTM3LjYzMjAzIDEwMDguMjU3MjQxIDUzNy40NzE4OCAxMDA4LjQxNzM5MUw1MjkuMzY4MjgzIDEwMTYuODg5MzM0QzUyNC45MzIxMjQgMTAyMS41ODE3MzMgNTE5LjAwNjU2OSAxMDIzLjkwMzkxIDUxMy4xMjkwNTggMTAyMy44NzE4OCA1MDcuMjE5NTE4IDEwMjMuOTAzOTEgNTAxLjM0MjAwOCAxMDIxLjU4MTczMyA0OTYuODQxNzg5IDEwMTYuODg5MzM0TDQ4OC43MzgxOTIgMTAwOC40MTczOTFDNDg4LjU3ODA0MiAxMDA4LjI1NzI0MSA0ODguNTQ2MDEyIDEwMDguMDQ5MDQ2IDQ4OC4zODU4NjIgMTAwNy44ODg4OTZMMzcyLjc4OTQ5IDg4MC4wMDkwMDhDMzYzLjgzNzA5NyA4NzAuNjU2MjQgMzYzLjgzNzA5NyA4NTUuNTIyMDUyIDM3Mi43ODk0OSA4NDYuMTM3MjU0TDM4MC44NjEwNTcgODQwLjY2MDExOUMzODkuNzk3NDM1IDgzMS4zMDczNTEgNDA0LjMwNzAzOCA4MzQuMzUwMjAzIDQxMy4yNTk0MzEgODQzLjcwMjk3Mkw0ODIuNDc2MzIyIDkyMC44OTUzMzkgNDgyLjQ3NjMyMiA1NDMuNTQ5NTc4IDEwMy4xMjg2ODMgNTQzLjU0OTU3OCAxODAuMzIxMDUxIDYxMi43NjY0NjlDMTg5LjY3MzgxOSA2MjEuNzE4ODYxIDE5Mi43MTY2NzIgNjM2LjIyODQ2NCAxODMuMzYzOTA0IDY0NS4xNjQ4NDJMMTc3Ljg4Njc2OSA2NTMuMjM2NDA5QzE2OC41MDE5NzEgNjYyLjE4ODgwMiAxNTMuMzY3NzgyIDY2Mi4xODg4MDIgMTQ0LjAxNTAxNCA2NTMuMjM2NDA5TDE2LjEzNTEyNyA1MzcuNjI0MDIzQzE1Ljk3NDk3NyA1MzcuNDc5ODg3IDE1Ljc2Njc4MSA1MzcuNDQ3ODU3IDE1LjYwNjYzMSA1MzcuMjg3NzA3TDcuMTM0Njg5IDUyOS4xODQxMUMyLjQ0MjI5IDUyNC42ODM4OTEgMC4xMjAxMTMgNTE4LjgwNjM4MSAwLjE1MjE0MyA1MTIuODk2ODQxIDAuMTIwMTEzIDUwNy4wMDMzMTYgMi40NDIyOSA1MDEuMDkzNzc1IDcuMTM0Njg5IDQ5Ni42NTc2MTdMOC42MjQwODUgNDk1LjIzMjI4QzExLjc2MzAyOCA0ODkuOTMxMzExIDE2LjM1OTMzNyA0ODUuNjcxMzE3IDIxLjkwMDUzMiA0ODIuOTE2NzM0TDE0Ni4wMTY4OTEgMzY5LjYyNjUyNUMxNTUuMzY5NjU5IDM2MC42OTAxNDcgMTcwLjUwMzg0NyAzNjAuNjkwMTQ3IDE3OS44ODg2NDYgMzY5LjYyNjUyNUwxODUuMzY1NzggMzc3LjY5ODA5MkMxOTQuNzE4NTQ5IDM4Ni42NTA0ODUgMTkxLjY3NTY5NiA0MDEuMTI4MDU4IDE4Mi4zMjI5MjggNDEwLjA4MDQ1TDEwNS43NzExNiA0NzkuNDg5NTIxIDQ4Mi40NzYzMjIgNDc5LjQ4OTUyMSA0ODIuNDc2MzIyIDEwMy4xNTI3MDYgNDEzLjI1OTQzMSAxODAuMzQ1MDc0QzQwNC4zMDcwMzggMTg5LjY4MTgyNyAzODkuNzk3NDM1IDE5Mi43MjQ2NzkgMzgwLjg2MTA1NyAxODMuMzcxOTExTDM3Mi43ODk0OSAxNzcuODk0Nzc2QzM2My44MzcwOTcgMTY4LjUwOTk3OCAzNjMuODM3MDk3IDE1My4zNzU3OSAzNzIuNzg5NDkgMTQ0LjAyMzAyMkw0ODguMzg1ODYyIDE2LjE1OTE0OUM0ODguNTQ2MDEyIDE1Ljk5ODk5OSA0ODguNTc4MDQyIDE1Ljc3NDc4OSA0ODguNzM4MTkyIDE1LjYzMDY1NEw0OTYuODQxNzg5IDcuMTQyNjk2QzUwMS4zNDIwMDggMi40NTAyOTcgNTA3LjIxOTUxOCAwLjE0NDEzNSA1MTMuMTI5MDU4IDAuMTc2MTY1IDUxOS4wMDY1NjkgMC4xNDQxMzUgNTI0LjkzMjEyNCAyLjQ1MDI5NyA1MjkuMzY4MjgzIDcuMTQyNjk2TDUzMC43OTM2MTkgOC42MzIwOTNDNTM2LjA3ODU3NCAxMS43ODcwNSA1NDAuMzU0NTgyIDE2LjM4MzM1OSA1NDMuMTA5MTY1IDIxLjkyNDU1NEw2NTYuMzk5Mzc0IDE0Ni4wMjQ4OThDNjY1LjMzNTc1MiAxNTUuMzc3NjY3IDY2NS4zMzU3NTIgMTcwLjUxMTg1NSA2NTYuMzk5Mzc0IDE3OS44OTY2NTNMNjQ4LjMyNzgwNyAxODUuMzczNzg4QzYzOS4zNzU0MTQgMTk0LjcyNjU1NiA2MjQuODk3ODQyIDE5MS42OTk3MTggNjE1Ljk0NTQ0OSAxODIuMzMwOTM1TDU0Ni41MzYzNzggMTA1Ljc5NTE4MyA1NDYuNTM2Mzc4IDQ3OS40ODk1MjEgOTE4LjIyODg0IDQ3OS40ODk1MjEgODQxLjY3NzA3MiA0MTAuMDgwNDVDODMyLjMyNDMwNCA0MDEuMTI4MDU4IDgyOS4yOTc0NjYgMzg2LjY1MDQ4NSA4MzguNjUwMjM1IDM3Ny42OTgwOTJMODQ0LjEyNzM2OSAzNjkuNjI2NTI1Qzg1My40OTYxNTMgMzYwLjY5MDE0NyA4NjguNjQ2MzU2IDM2MC42OTAxNDcgODc3Ljk5OTEyNCAzNjkuNjI2NTI1TDEwMDcuODY0ODczIDQ4OC4xODU2NzRDMTAwOC4wNTcwNTMgNDg4LjMyOTgwOSAxMDA4LjI0OTIzNCA0ODguMzkzODY5IDEwMDguMzkzMzY5IDQ4OC41NTQwMTlMMTAxNi44ODEzMjYgNDk2LjY1NzYxN0MxMDIxLjU1NzcxIDUwMS4wOTM3NzUgMTAyMy44Nzk4ODcgNTA3LjAwMzMxNiAxMDIzLjg0Nzg1NyA1MTIuODk2ODQxIDEwMjMuODc5ODg3IDUxOC44MDYzODEgMTAyMS41NTc3MSA1MjQuNjgzODkxIDEwMTYuODgxMzI2IDUyOS4xODQxMVoiIHAtaWQ9IjExMzgxIiBmaWxsPSIjY2NjY2NjIj48L3BhdGg+PC9zdmc+')
		no-repeat 50%;
	background-size: 100% 100%;
}
</style>
<style lang="scss" scoped>
/* @import url(); 引入css类 */
.player {
	width: 100%;
	height: 100%;
	position: relative;
	.video-title {
		position: absolute;
		left: 10px;
		top: 10px;
		color: #fff;
		z-index: 4;
		font-size: 17px;
		text-shadow: 0 0 6px #000;
	}
	.video-play {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: 48px;
		height: 48px;
		background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAACEElEQVRoQ+2ZXStEQRjH/3/yIXwDdz7J+i7kvdisXCk3SiFJW27kglBcSFFKbqwQSa4krykuKB09Naf2Yndn5jgzc06d53Znd36/mWfeniVyHsw5PwqB0DOonYEoijYBlOpAFwCMkHwLDS/9mwhEDUCfAAyTXA4tYSLwC6CtCegegH6S56FETAR+AHRoACcBTJAUWa+RloBAXwAYIrnt0yBNgZi7qtbHgw8RFwLC/QFglOScawlXAjH3gUqrE1cirgVi7mkAYyS/0xbxJSDcdwAGSa6nKeFTIOZeUyL3aYiEEBDuLwDjJGf+KxFKIOY+BdBL8iipSGiBmHtWbbuftiJZERBuOfgGSK7aSGRJIObeUml1ayKSRQHhlgtkiaTcdltGVgUE+ppkV54FaiS78yrwqlLoOI8Cch2XV548W7WRpTVwA6DP9kGUFYEpAOUkT9LQAvtq1M+0udKkQSgBqSlJWWYxKXj8vRACK+o6bbRIdYI+Ba7U7rKjg7L53JdAhWTZBsy0rWuBXZUuNVMg23auBF7UIl2yBbJt70JAoKV6/WwLk6R9mgKSJlJ1kLTxFmkJyCla8UZd15GJQKvyumyJ8gy8DAEvfZoINPqD41EtUjmUgoaJwAaAnjrKebVI34OSq85NBNqlCAWgE0CV5GEWwI3vQlmCbcSinYFCwPEIFDPgeIC1P1/MgHaIHDf4Aydx2TF7wnKeAAAAAElFTkSuQmCC);
		cursor: pointer;
	}
	.el-tooltip__trigger {
		// width: 16px;
		// height: 16px;
		// position: absolute;
		// z-index: 55;
		// bottom: 12px;
		// right: 12px;
		color: #ccc;
		cursor: pointer;
	}
}
.handle-tooltip {
	padding: 0;
	.handle {
		padding: 2px 0;
		&-keys {
			margin: 0 auto;
			width: vw(100);
			height: vw(100);
			background: url('/src/assets/sd/rvc/handle-bg.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			position: relative;
			.key {
				position: absolute;
				cursor: pointer;
				img {
					width: 100%;
					height: 100%;
					vertical-align: middle;
					transform-origin: center;
				}
				&:hover img {
					transform: scale(1.05);
				}
			}
			.key.top {
				width: vw(28);
				top: vw(20);
				left: 50%;
				transform: translateX(-50%);
			}
			.key.bottom {
				width: vw(28);
				bottom: vw(20);
				left: 50%;
				transform: translateX(-50%);
			}
			.key.left {
				height: vw(28);
				left: vw(20);
				top: 50%;
				transform: translateY(-50%);
			}
			.key.right {
				height: vw(28);
				right: vw(20);
				top: 50%;
				transform: translateY(-50%);
			}
			.key.motion {
				width: vw(44);
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
			}
			.key.add {
				width: vw(24);
				right: 0;
				bottom: 0;
			}
			.key.reduce {
				width: vw(24);
				left: 0;
				bottom: 0;
			}
		}
	}
}
</style>

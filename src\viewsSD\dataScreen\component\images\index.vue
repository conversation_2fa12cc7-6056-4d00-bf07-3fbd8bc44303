<template>
	<div class="images">
		<div
			v-for="(item, $index) in images"
			:key="item.id"
			class="image-item animate__animated"
			:class="item.isNewData ? 'animate__zoomIn' : ''"
			@animationend="onNewDataAnimationend"
		>
			<div class="pic">
				<el-image
					:src="item.pictureUrl || ''"
					fit="cover"
					:preview-teleported="true"
					:preview-src-list="previewpictureUrl"
					:initial-index="$index"
				>
					<template #placeholder>
						<div class="image-placeholder">
							<el-icon class="is-loading">
								<ele-Loading />
							</el-icon>
						</div>
					</template>
					<template #error>
						<div class="load-error">
							<img
								class="small-img-error"
								src="/src/assets/fullScreen/small-load-error.png"
								title="加载失败"
								alt=""
							/>
						</div>
					</template>
				</el-image>
				<div class="info">
					<div class="info-top">
						<div>
							<span v-for="rec in item.monitorEventDetails" :key="rec.recResult">
								{{ rec.recResult }}({{ rec.recResultCnt }})
							</span>
						</div>
						<div>{{ item.createTime }}</div>
					</div>
					<div class="info-bottom">
						<img src="/src/assets/sd/data-screen/recent-dw.png" alt="" />
						点位：{{ item.deviceNum }}
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { getMoments } from '/@/api/sd/dataScreen/bird';
import { useInterval, markNewData } from '/@/hooks/useInterval';

const props = defineProps<{
	animalTag?: number;
	animalNameList?: string[];
	timeType: number | null;
}>();

const images = ref<any[]>([]);
const previewpictureUrl = ref<string[]>([]);
// 获取瞬间图集
const getImages = async () => {
	const query = {
		animalTag: props.animalTag,
		animalNameList: props.animalNameList,
		timeType: props.timeType,
		page: 0,
		size: 12,
	};
	previewpictureUrl.value = [];

	const { payload } = await getMoments(query);
	const urls: string[] = [];
	const formatPayload = payload.content.map((item: any) => {
		const pictureUrl =
			item.monitorEventFileModel.pictureUrl || item.monitorEventFileModel.oriPictureThumbnailUrl;
		urls.push(pictureUrl);
		return {
			id: item.id,
			monitorEventDetails: item.monitorEventFileModel.monitorEventDetails || [],
			pictureUrl,
			deviceNum: item.monitorEventFileModel.device.num,
			createTime: item.createTime.slice(5),
		};
	});
	images.value = markNewData(formatPayload, images.value);
	previewpictureUrl.value = urls;
};
useInterval(getImages);

const onNewDataAnimationend = (event: Event) => {
	const animationEle = event.target as HTMLDivElement;
	if (animationEle?.classList.contains('animate__zoomIn')) {
		animationEle?.classList.remove('animate__zoomIn');
	}
};

watch(
	[() => props.animalNameList, () => props.timeType],
	() => {
		getImages();
	},
	{ immediate: true }
);
</script>

<style lang="scss" scoped>
.images {
  height: 100%;
	display: flex;
	flex-wrap: wrap;
	margin-top: vw(60);
	padding: 0 vw(20);
	.image-item {
		width: calc(100% / 4);
		padding: vw(10);
		position: relative;
		animation-duration: 0.5s;
		.pic {
			width: 100%;
			padding-top: 100%;
			position: relative;
			overflow: hidden;
			.el-image {
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				transition: transform 0.3s;
			}
			.el-image:hover {
				transform: scale(1.02);
			}
		}
		.info {
			width: 100%;
			position: absolute;
			bottom: 0;
			background-color: rgba(0, 0, 0, 0.3);
			padding: vw(5);
			&-top {
				display: flex;
				align-items: flex-end;
				justify-content: space-between;
				span {
					font-size: vw(16);
				}
			}
			&-bottom {
				margin-top: vw(6);
				img {
					width: vw(10);
					vertical-align: middle;
				}
			}
		}
	}
}
</style>

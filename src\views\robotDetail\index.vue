<template>
	<div class="task-container fullScreen">
		<img class="fanhui" src="/src/assets/live/fanhui.png" alt="返回" @click="goBack" />
		<div class="title">
			<img src="/src/assets/fullScreen/title-name.png" alt="" draggable="false" />
		</div>
		<div class="mian">
			<div class="task-box">
				<img class="title-img" src="/src/assets/robotManage/jiqirenguanli.png" alt="" />
				<div class="task-bottom">
					<!-- 任务时间 -->
					<div class="task-time">
						<div class="select-title">
							<el-select
								v-model="deviceId"
								placeholder="筛选"
								size="small"
								style="width: 100%"
								:fit-input-width="true"
								:teleported="false"
								@change="handelTimeChange(true)"
							>
								<el-option
									v-for="(key, index) in robotsInfo.keys()"
									:label="robotsInfo.get(key)?.robotName"
									:value="key"
								/>
							</el-select>
						</div>
						<div
							class="select-option scrollbar-lg"
							ref="swiperRef"
							v-infinite-scroll="handelTimeChange"
							style="overflow-y: auto"
							:infinite-scroll-immediate="false"
							:infinite-scroll-disabled="disabled"
							:infinite-scroll-distance="17"
						>
							<div
								class="time-item"
								v-for="time in state.timeData.data"
								:class="state.timeData.activeId === time.planId ? 'active-time-item' : ''"
								@click="handelTimeClick(time)"
							>
								<span class="time-item-name">{{ time.planName }}</span>
								<span class="task-title-time">路线名称：{{ time.lineName || '-' }}</span>
								<span class="task-title-time">开始时间：{{ time.beginTime || '-' }}</span>
								<span class="task-title-time">结束时间：{{ time.endTime || '-' }}</span>
							</div>

							<p
								v-if="state.timeData.loading"
								class="text-loding"
								:class="{ mt80: state.timeData.pageParams.page === 0 }"
								style="color: #22d69f"
							>
								加载中...
							</p>
							<p
								v-else-if="noMore"
								class="text-loding"
								:class="{ mt80: state.timeData.total === 0 }"
								:style="{}"
							>
								{{ state.timeData.total ? '没有更多了' : '暂无数据' }}
							</p>
						</div>
					</div>
					<div class="task-view">
						<div class="task-title">
							<span class="task-name">{{ state.timeData.planName || '-' }}</span>
							<span class="task-title-time">路线名称：{{ state.timeData.lineName || '-' }}</span>
							<span class="task-title-time">开始时间：{{ state.timeData.beginTime || '-' }}</span>
							<span class="task-title-time">结束时间：{{ state.timeData.endTime || '-' }}</span>
							<span class="task-title-time">执行星期：{{ state.timeData.weekly || '-' }}</span>
						</div>
						<div class="task-search">
							<div class="search-input">
								<span class="search-item">
									选择时间：
									<el-date-picker
										v-model="state.tableData.filter.beginDate"
										type="date"
										placeholder="点击选择日期"
										:teleported="false"
										format="YYYY-MM-DD"
										value-format="YYYY-MM-DD HH:mm:ss"
										:clearable="true"
									/>
								</span>
								<span class="button-round" @click="onRefresh(true, false)">搜索</span>
								<span class="button-round" style="margin-left: 30px" @click="onRefresh(true, true)"
									>重置</span
								>
							</div>
						</div>
						<div class="table-box">
							<div class="table">
								<el-table
									:data="state.tableData.data"
									v-loading="state.tableData.loading"
									style="width: 100%"
									highlight-current-row
									height="100%"
									:header-cell-style="{
										color: '#fff',
										background: 'transparent',
										borderBottom: '1px solid rgba(104, 207, 173, 0.45)',
										backgroundImage: 'linear-gradient(to bottom, #000, rgba(65, 169, 127, 0.35))',
									}"
									:cell-style="{
										color: '#fff',
										borderBottom: '1px solid rgba(104, 207, 173, 0.45)',
									}"
									:row-style="{
										color: '#fff',
										borderBottom: '1px solid rgba(104, 207, 173, 0.45)',
									}"
								>
									<el-table-column prop="patrolPlanName" label="任务" width="150" />
									<el-table-column prop="beginTime" label="开始时间" width="165">
										<template v-slot="scope">
											{{ extractTimePart(scope.row.beginTime, 'MM-DD HH:mm:ss') }}
										</template>
									</el-table-column>
									<el-table-column prop="endTime" label="结束时间" width="165">
										<template v-slot="scope">
											{{ extractTimePart(scope.row.endTime, 'MM-DD HH:mm:ss') }}
										</template>
									</el-table-column>

									<el-table-column prop="statistics" label="事件统计" width="85">
										<template v-slot="scope">
											{{ scope.row.statistics.reduce((a: any, b: any) => a + b.alarmCount, 0) }}
										</template>
									</el-table-column>
									<el-table-column prop="statistics[0].alarmCount" label="公园安防" width="85" />
									<el-table-column prop="statistics[1].alarmCount" label="公园植保" width="85" />
									<el-table-column prop="statistics[2].alarmCount" label="公园设施" width="85" />
									<el-table-column prop="statistics[3].alarmCount" label="公园环境" width="85" />
									<el-table-column prop="statistics[4].alarmCount" label="生物多样性" width="95" />
									<el-table-column prop="statistics[5].alarmCount" label="不文明行为" width="95" />
									<el-table-column label="操作">
										<template v-slot="scope">
											<img
												class="btn"
												src="/src/assets/robotManage/chakan.png"
												title="点击查看详情"
												alt=""
												@click="handleDetail(scope)"
											/>
										</template>
									</el-table-column>
								</el-table>
							</div>
							<el-pagination
								class="el-pagination_largeScreen mt10"
								v-model:current-page="state.tableData.pageParams.page"
								:page-sizes="[10, 30, 50]"
								background
								v-model:page-size="state.tableData.pageParams.size"
								layout="total, sizes, prev, pager, next, jumper"
								:total="state.tableData.total"
								:teleported="false"
								@size-change="onHandleSizeChange"
								@current-change="onHandleCurrentChange"
							>
							</el-pagination>
						</div>
					</div>
				</div>
			</div>
			<div class="statistics-box">
				<div class="title-img"></div>
				<div class="robot-info">
					<div class="robot-info-title">
						<span class="robot-info-title-name">
							<el-select
								v-model="deviceId"
								placeholder="筛选"
								size="small"
								style="width: 70%"
								:fit-input-width="true"
								:teleported="false"
								@change="handelTimeChange(true)"
							>
								<el-option
									v-for="(key, index) in robotsInfo.keys()"
									:label="robotsInfo.get(key)?.robotName"
									:value="key"
								/>
							</el-select>
						</span>
						<span
							class="robot-info-live"
							:class="{ disabled: robotsInfo.get(deviceId)?.state === '离线' }"
							@click="handelJumpTo"
						>
							<img
								:src="robotsInfo.get(deviceId)?.state === '离线' ? liveDisabledIcon : liveIcon"
								alt=""
								width="17"
								height="15"
							/>
							<span>实时视频</span>
						</span>
					</div>
					<div class="robot-info-main">
						<span class="robot-info-main-item">
							<span class="robot-info-main-item-value">{{
								robotsInfo.get(deviceId)?.electricity
							}}</span>
							<span class="robot-info-main-item-label">电量</span>
						</span>
						<span class="robot-info-main-item">
							<span class="robot-info-main-item-value">{{ robotsInfo.get(deviceId)?.state }}</span>
							<span class="robot-info-main-item-label">状态</span>
						</span>
						<span class="robot-info-main-item">
							<span class="robot-info-main-item-value">{{
								robotsInfo.get(deviceId)?.currentDuration
							}}</span>
							<span class="robot-info-main-item-label">开机时长</span>
						</span>
						<span class="robot-info-main-item">
							<span class="robot-info-main-item-value">{{
								robotsInfo.get(deviceId)?.currentMileage
							}}</span>
							<span class="robot-info-main-item-label">行驶里程</span>
						</span>
					</div>
				</div>
				<div class="line-box">
					<img
						class="title-img"
						style="width: 66px"
						src="/src/assets/robotManage/luxian.png"
						alt=""
					/>
					<el-amap
						:center="mapCenter"
						:zoom="zoom"
						:animateEnable="mapAnimateEnable"
						:features="['bg', 'point', 'road', 'building']"
						mapStyle="amap://styles/blue"
						@init="initMap"
					>
						<template v-if="patrolLines.length">
							<el-amap-polyline
								ref="polylineRef"
								v-for="(path, $index) in patrolLines"
								:key="$index"
								:reEventWhenUpdate="true"
								:visible="true"
								:editable="false"
								:path="path"
								strokeColor="#54AA8F"
								:strokeOpacity="1"
								:strokeWeight="8"
								strokeStyle="solid"
								lineJoin="round"
							>
							</el-amap-polyline>
						</template>
					</el-amap>
				</div>
			</div>
		</div>
		<DetailDig ref="detailDigRef" :taskId="taskId" :robotSn="robotSn" />
		<div class="footer">
			<img src="/src/assets/fullScreen/footer-bg.png" alt="底部" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import {
	ref,
	reactive,
	onMounted,
	onUnmounted,
	defineAsyncComponent,
	computed,
	watchEffect,
	nextTick,
} from 'vue';
import { extractTimePart } from '/@/utils/formatTime';
import { getPatrolTasksStat, getPatrolPlans, getRobotMaps } from '/@/api/home';
import { getMonitorEventTypes } from '/@/api/monitorEvents';
import { bd09_To_gcj02, gps84_To_gcj02 } from '@vuemap/vue-amap';
import { useRouter, useRoute } from 'vue-router';
import { NextLoading } from '/@/utils/loading';
import { liveInfo } from '/@/stores/fullScreen';
import { storeToRefs } from 'pinia';
import liveDisabledIcon from '/@/assets/fullScreen/robot-info-live-disabled-icon.png';
import liveIcon from '/@/assets/fullScreen/robot-info-live-icon.png';

const DetailDig = defineAsyncComponent(() => import('./detailDig.vue'));
const liveStore = liveInfo();
const { robotsInfo } = storeToRefs(liveStore);

const router = useRouter();
const route = useRoute();
const deviceId = ref(Number(route.query.deviceId));
const robotSn = ref(route.query.robotSn as unknown as string);

const state = reactive<ViewBaseState<MonitorEventRow>>({
	METypeOptions: [],
	tableData: {
		filter: {
			beginDate: '',
			endTime: '',
		},
		data: [],
		loading: false,
		pageParams: {
			page: 1,
			size: 10,
		},
		total: 0,
	},

	timeData: {
		filter: {
			beginDate: '',
		},
		planName: '',
		lineName: '',
		endTime: '',
		weekly: '',
		data: [],
		loading: false,
		pageParams: {
			page: -1,
			size: 6,
		},
		total: 0,
		activeId: '',
	},
	selectAll: false,
});

// 左侧时间
// 没有数据了
const noMore = computed(() => {
	return state.timeData.pageParams.page * state.timeData.pageParams.size >= state.timeData.total;
});
// 禁用下拉加载
const disabled = computed(() => {
	return state.timeData.loading || noMore.value;
});

const handelTimeChange = async (timeChange = false) => {
	state.timeData.loading = true;
	state.tableData.loading = true;
	getLine();
	if (timeChange) {
		state.timeData.data = [];
		state.timeData.total = 0;
		state.timeData.pageParams.page = -1;
		state.timeData.pageParams.size = 10;
		state.timeData.activeId = '';
		state.timeData.planName = '-';
		state.timeData.endTime = '-';
		state.timeData.weekly = '-';
		state.timeData.beginTime = '-';
		state.timeData.lineName = '-';
		state.tableData.filter.beginDate = '';
		robotSn.value = robotsInfo.value.get(deviceId.value)?.robotSn as string;
	}

	state.timeData.pageParams.page += 1;
	const query = {
		page: state.timeData.pageParams.page as number,
		size: state.timeData.pageParams.size as number,
		deviceId: deviceId.value,
	};
	const { payload } = await getPatrolPlans(query);

	if (payload.content.length > 0) {
		handelTimeClick(payload.content[0]);
	} else {
		state.tableData.data = [];
		state.tableData.total = 0;
		state.tableData.loading = false;
	}
	state.timeData.data.push(...payload.content);
	state.timeData.total = payload.totalElements;

	state.timeData.loading = false;
};

const handelJumpTo = () => {
	// console.log(robotsInfo.value[deviceId.value].state);
	if (robotsInfo.value.get(deviceId.value)?.state === '离线') return;
	const currentUrl = window.location.href;
	const hashIndex = currentUrl.indexOf('#/');
	if (hashIndex) {
		const desiredUrl = currentUrl.substring(0, hashIndex + 2);
		window.open(`${desiredUrl}robot-monitor?deviceId=${deviceId.value}`, '_blank');
	}
};

const convertWeekdays = (weekString: string) => {
	const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
	const numberArray = weekString.replace(/\s+/g, '').split(',');
	const result = numberArray.map((weekday: string) => weekdays[parseInt(weekday) - 1]).join('、');

	return result;
};

const handelTimeClick = (_item: any) => {
	console.log(_item);
	state.tableData.filter.beginDate = '';
	state.timeData.activeId = _item.planId;
	state.timeData.lineName = _item.lineName;
	state.timeData.planName = _item.planName;
	state.timeData.weekly = convertWeekdays(_item.weekly);
	state.timeData.beginTime = _item.beginTime;
	state.timeData.endTime = _item.endTime;
	state.tableData.pageParams.page = 1;
	state.tableData.pageParams.size = 10;
	getTableData();
};

const getTableData = async () => {
	state.tableData.loading = true;
	const query = {
		page: state.tableData.pageParams.page - 1,
		size: state.tableData.pageParams.size,
		deviceId: deviceId.value,
		planId: state.timeData.activeId,
		...state.tableData.filter,
	};

	const { payload } = await getPatrolTasksStat(query);

	state.tableData.data = payload.content;
	state.tableData.total = payload.totalElements;
	state.selectAll = false;
	state.tableData.loading = false;
};
const getMETypeOptions = async () => {
	const { payload } = await getMonitorEventTypes();
	state.METypeOptions = payload;
};

const taskId = ref('');
const detailDigRef = ref();

const handleDetail = (scope: any) => {
	console.log(scope);
	taskId.value = scope.row.id;

	const params = {
		startTime: scope.row.beginTime,
		endTime: scope.row.endTime,
		taskId: scope.row.id,
		num: robotsInfo.value.get(deviceId.value)?.robotSn,
		patrolPlanName: scope.row.patrolPlanName,
	};
	detailDigRef.value.handleOpen(params);
};

// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getTableData();
};
const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getTableData();
};

// 全选
const onSelectAll = () => {
	state.selectAll = !state.selectAll;
	state.tableData.data = state.tableData.data.map((item) => ({
		...item,
		checked: state.selectAll,
	}));
};
const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (state.timeData.data.length === 0) return;
	if (resetPage) state.tableData.pageParams.page = 1;
	if (resetFilter) {
		state.tableData.filter.beginDate = '';
	}
	getTableData();
};

// 地图
const map = ref<AMap.Map>();
const zoom = ref(17.49);
const mapCenter = ref<[number, number]>([116.319055, 39.906644]); // 首次加载地图，中心点为机器人位置
const mapAnimateEnable = ref(false); // 地图平移过程中是否使用动画（首次加载不需要）
const polylineRef = ref();
const initMap = (amap: AMap.Map) => {
	map.value = amap;
};

const patrolLines = ref<Array<number[][]>>([]);
const getLine = async () => {
	patrolLines.value = [];
	const { payload } = await getRobotMaps({ robotIds: [deviceId.value] });
	payload.forEach((map: any) => {
		if (map.patrolLines) {
			map.patrolLines.forEach((line: any) => {
				payload[0].patrolLines[0].points;
				const path: number[][] = [];
				line.points.forEach((point: any) => {
					// wgs84转高德
					if (point.longitude && point.latitude) {
						const gcj02 = gps84_To_gcj02(point.longitude, point.latitude);
						path.push([gcj02.lng, gcj02.lat]);
					}
				});
				patrolLines.value.push(path);
			});
		}
	});
};

watchEffect(() => {
	if (map.value && patrolLines.value.length) {
		nextTick(async () => {
			if (!map.value) return;
			map.value.setFitView(undefined, true, [10, 10, 10, 10]);
		});
	}
});
// 返回大屏页面
const goBack = () => {
	router.push('/full-screen');
};
onMounted(async () => {
	handelTimeChange();
	getMETypeOptions();
	NextLoading.done();
});

onUnmounted(() => {});

defineExpose({});
</script>

<style lang="scss" scoped>
.bg-lg {
	background-image: linear-gradient(to bottom, #186047, #41a97f);
}
.task-container {
	position: relative;
	height: 100%;
	background-color: #000000;
	color: rgba(255, 255, 255, 1);
	.mian {
		height: calc(100%);
		padding: 100px 21px 59px 39px;
		display: flex;

		.title-img {
			display: block;
			width: 127px;
			height: 29px;
			margin-bottom: 20px;
		}

		.task-box {
			width: calc(100% - 354px);
			height: 100%;

			.task-title-time {
				display: flex;
				align-items: center;
				height: 20px;
				line-height: 20px;
				font-size: 14px;
				margin-left: 15px;
			}

			.task-bottom {
				height: calc(100% - 29px - 20px);
				display: flex;
				.task-time {
					border: 1px solid rgba(104, 207, 173, 0.5);
					width: 216px;
					height: 100%;
					color: #fff;
					font-size: 14px;
					.select-title {
						position: relative;
						height: 64px;
						display: flex;
						align-items: center;
						background-repeat: no-repeat;
						background-size: 100% 100%;
						background-image: url('/src/assets/task/select-time-bg.png');

						padding-left: 38px;

						&::before {
							content: '';
							width: 19px;
							height: 17px;
							position: absolute;
							top: 50%;
							left: 20px;
							transform: translateY(-50%);
							background-repeat: no-repeat;
							background-size: 100% 100%;
							background-image: url('/src/assets/robotManage/jiqiren.png');
						}

						&:deep(.el-select-dropdown__item.selected) {
							background-color: transparent !important;
							color: rgb(104, 207, 173);
						}

						// &:deep(.el-input__prefix) {
						// 	display: none;
						// }
						&:deep(.el-popper.is-light .el-popper__arrow::before) {
							border: 1px solid rgba(104, 207, 173, 1) !important;
							border-bottom-color: transparent !important;
							border-right-color: transparent !important;
							background: #010b07 !important;
						}

						&:deep(.el-form-item__label) {
							color: #fff;
							font-size: 14px;
						}

						&:deep(.el-select .el-input__inner::placeholder) {
							font-size: 14px;
						}

						&:deep(.el-input__wrapper) {
							border: none;
							background-color: transparent !important;
							box-shadow: none !important;
							box-shadow: 0 0 0 0px transparent inset !important;
							padding: 0 11px !important;
						}
						&:deep(.el-input__inner) {
							--el-input-inner-height: 30px !important;
							color: rgba(255, 255, 255, 1);
						}
						&:deep(.el-select .el-input.is-focus .el-input__wrapper) {
							box-shadow: 0 0 0 0px transparent inset !important;
						}
						&:deep(.el-select .el-input__inner::placeholder) {
							// color: rgba(255, 255, 255, 1);
							font-size: 14px;
						}

						.el-icon :deep(svg) {
							color: rgba(255, 255, 255, 1);
						}

						&:deep(.el-select) {
							--el-select-input-focus-border-color: none !important;
						}
					}
					.select-option {
						height: calc(100% - 64px);
						overflow-y: auto;

						.mt80 {
							margin-top: 80px;
						}
						.text-loding {
							text-align: center;
							font-size: 14px;
							color: #bbb;
						}

						.active-time-item {
							background-repeat: no-repeat;
							background-size: 100% 100%;
							background-image: url('/src/assets/task/active-time-item-bg.png');
						}
						.time-item {
							height: 113px;
							display: flex;
							flex-direction: column;
							justify-content: center;
							padding: 5px 0 5px 5px;
							.time-item-name {
								height: 22px;
								line-height: 22px;
								font-size: 16px;
								margin-bottom: 10px;
							}
							.task-title-time {
								margin: 0;
								&:first-child {
									margin-bottom: 10px;
								}
							}
						}
					}
				}

				.task-view {
					width: calc(100% - 216px);
					padding: 0 23px;

					.task-title {
						display: flex;
						align-items: center;
						height: 25px;
						color: rgba(255, 255, 255, 1);
						line-height: 25px;
						font-size: 18px;
						margin-bottom: 25px;
						.task-name {
							margin-right: 20px;
						}
					}

					.task-search {
						color: #fff;
						&:deep(.el-tag) {
							@extend .bg-lg;
							color: #fff;
						}
						&:deep(.el-select-dropdown__item.selected) {
							background-color: transparent !important;
							color: rgb(104, 207, 173);
						}

						&:deep(.el-input__prefix) {
							// display: none;
						}
						.search-input {
							margin-bottom: 18px;
							.search-item {
								margin-right: 80px;
								&:deep(.el-select, .el-input) {
									margin-left: 15px;
								}
								&:deep(.el-input__suffix) {
									width: 30px;
								}
								&:deep(.el-input__inner::placeholder) {
									// color: rgba(255, 255, 255, 1);
									font-size: 14px;
								}
							}
						}
						.button-round {
							display: inline-block;
							width: 108px;
							height: 34px;
							border-radius: 54px;
							color: #fff;
							line-height: 34px;
							text-align: center;
							cursor: pointer;
							@extend .bg-lg;
							-webkit-user-select: none; /* Safari */
							-moz-user-select: none; /* Firefox */
							-ms-user-select: none; /* IE/Edge */
							user-select: none; /* 标准语法 */
						}
						.search-btns {
							display: flex;
							justify-content: space-between;
							margin-bottom: 18px;
						}

						&:deep(.el-select-dropdown__item.selected) {
							background-color: transparent !important;
							color: rgb(104, 207, 173);
						}

						&:deep(.el-input__prefix) {
							// display: none;
						}
						&:deep(.el-form-item__label) {
							color: #fff;
							font-size: 14px;
						}

						&:deep(.el-select .el-input__inner::placeholder) {
							color: #767876 !important;
							font-size: 14px;
						}

						&:deep(.el-input__wrapper) {
							border-radius: 50px !important;
							border: 1px solid rgba(104, 207, 173, 1) !important;
							background-color: transparent !important;
							box-shadow: none !important;
							padding: 0 11px !important;
						}
						&:deep(.el-input__inner) {
							--el-input-inner-height: 30px !important;
							color: rgba(255, 255, 255, 1);
						}
						&:deep(.el-select .el-input.is-focus .el-input__wrapper) {
							box-shadow: none !important;
						}
						&:deep(.el-select .el-input__inner::placeholder) {
							color: rgba(255, 255, 255, 1);
							font-size: 14px;
						}

						.el-icon :deep(svg) {
							color: rgba(255, 255, 255, 1);
						}

						&:deep(.el-select) {
							--el-select-input-focus-border-color: none !important;
						}
					}
					.table-box {
						width: 100%;
						height: calc(100% - 100px);
						display: flex;
						flex-wrap: wrap;
						// overflow-y: auto;

						.table {
							width: 100%;
							height: calc(100% - 42px);
							&:deep(.el-loading-mask) {
								background-color: rgba(0, 0, 0, 0.7);
								.path {
									stroke: #68cfad;
								}
							}
							:deep(.current-row) {
								background-image: linear-gradient(to bottom, #000, rgba(65, 169, 127, 0.35));
							}
							:deep(.el-table) {
								--el-table-current-row-bg-color: linear-gradient(
									to bottom,
									#000,
									rgba(65, 169, 127, 0.35)
								);
							}

							.btn {
								display: block;
								width: 18px;
								height: 20px;
								cursor: pointer;
							}
						}

						.el-table {
							border: 1px solid #68cfad; /* 设置表格边框颜色和宽度 */
							background-color: transparent !important;
							--el-table-border-color: none;
						}
						:deep(.el-table tbody tr:hover > td) {
							background-color: transparent !important;
						}

						&:deep(.el-table tr) {
							background-color: transparent !important;
						}
						&::-webkit-scrollbar {
							width: 10px;
						}
						&::-webkit-scrollbar-thumb {
							background-color: #22d69f; /* 滑块的背景颜色 */
							border-radius: 0; /* 滑块的圆角 */
							border-left: 6px solid transparent; /* 左边透明边框，占滚动条宽度的一半 */
							// background-clip: content-box; /* 使背景色不延伸到边框 */
						}
						&::-webkit-scrollbar-track-piece {
							background-color: transparent;
						}
					}
				}
			}
		}
		.statistics-box {
			width: 354px;
			.robot-info {
				position: relative;
				height: 274px;
				margin-bottom: 17px;
				background-image: url('/src/assets/robotManage/jiqirenguanli-bg.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
				position: relative;
				padding: 45px 10px 0;
				&::before {
					content: '';
					position: absolute;
					top: 0.9vh;
					left: 24px;
					width: 86px;
					height: 2.69vh;
					background-image: url('/src/assets/fullScreen/bia.png');
					background-repeat: no-repeat;
					background-size: 100%;
				}

				.robot-info-title {
					height: 27px;
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 13px;
					margin-bottom: 13px;
					.robot-info-title-name {
						height: 2.96vh;
						color: rgba(255, 255, 255, 1);
						font-size: 15px;
						padding-left: 20px;

						&:deep(.el-input__wrapper) {
							border: none;
							background-color: transparent !important;
							box-shadow: none !important;
						}
						&:deep(.el-input__inner) {
							color: rgba(255, 255, 255, 1);
						}
						&:deep(.el-select .el-input.is-focus .el-input__wrapper) {
							box-shadow: none !important;
						}
						&:deep(.el-select .el-input__inner::placeholder) {
							color: rgba(255, 255, 255, 1);
							font-size: 14px;
						}

						:deep(.el-select__caret) {
							color: rgba(104, 207, 173, 1);
						}

						&:deep(.el-select) {
							--el-select-input-focus-border-color: none !important;
						}
					}
					.robot-info-live {
						color: rgba(255, 255, 255, 1);
						width: 109px;
						height: 100%;
						background-image: url('/src/assets/fullScreen/robot-info-live.png');
						background-repeat: no-repeat;
						background-size: 100% 100%;
						display: flex;
						justify-content: center;
						align-items: center;
						font-style: 14px;
						cursor: pointer;
						img {
							margin-right: 6px;
						}
					}
					.disabled {
						background-image: url('/src/assets/fullScreen/robot-info-live-disabled.png');
						cursor: not-allowed;
						color: #999;
					}
				}
				.robot-info-main {
					position: relative;
					height: calc(100% - 53px - 28px);
					display: flex;
					// padding-top: 0.65vh;

					.robot-info-main-item {
						flex: 1;
						background-repeat: no-repeat;
						background-size: 100% 100%;
						margin-right: 8px;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: flex-end;
						padding-bottom: 0.9vh;

						.robot-info-main-item-label {
							color: rgba(255, 255, 255, 1);
							font-size: 14px;
							margin-top: 0.833vh;
						}
						&:nth-child(1) {
							background-image: url('/src/assets/fullScreen/dianliang.png');
							color: rgba(151, 206, 255, 1);
							font-size: 20px;
						}
						&:nth-child(2) {
							background-image: url('/src/assets/fullScreen/zhuangtai.png');
							color: rgba(167, 255, 210, 1);
							font-size: 16px;
						}
						&:nth-child(3) {
							background-image: url('/src/assets/fullScreen/kaijishichang.png');
							color: rgba(74, 197, 255, 1);
							font-size: 16px;
						}
						&:nth-child(4) {
							background-image: url('/src/assets/fullScreen/licheng.png');
							margin-right: 0px;
							color: rgba(248, 206, 107, 1);
							font-size: 16px;
						}
					}
				}
			}
			.line-box {
				height: calc(100% - 340px);
				padding: 14px 23px 25px;
				background-image: url('/src/assets/robotManage/luxian-bg.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
				.el-vue-amap-container {
					height: calc(100% - 23px - 29px);
				}
			}
		}
	}

	.fanhui {
		width: 84px;
		height: 35px;
		object-fit: cover;
		position: absolute;
		top: 45px;
		left: 40px;
		z-index: 10;
		cursor: pointer;
	}
	.title {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 80px;
		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.footer {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 187px;
		pointer-events: none;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;

		img {
			display: block;
			width: 100%;
			height: 33px;
			object-fit: cover;
		}
	}
}

:deep(.el-popper.is-light .el-popper__arrow::before) {
	border: 1px solid rgba(104, 207, 173, 1) !important;
	background: #010b07 !important;
}
.search-item {
	:deep(.el-popper.is-light .el-popper__arrow::before) {
		border-bottom-color: transparent !important;
		border-right-color: transparent !important;
	}
}
:deep(.el-picker-panel) {
	background: #000;
}

:deep(.el-input__wrapper) {
	background-color: transparent;
	box-shadow: none;
	// .el-input__prefix-inner {
	// 	width: 18px;
	// 	.el-icon {
	// 		position: relative;
	// 		svg {
	// 			display: none;
	// 		}
	// 		&::before {
	// 			content: '';
	// 			width: 18px;
	// 			height: 18px;
	// 			position: absolute;
	// 			top: 50%;
	// 			left: 0;
	// 			transform: translateY(-50%);
	// 			background-repeat: no-repeat;
	// 			background-size: 100% 100%;
	// 			background-image: url('/src/assets/task/time.png');
	// 		}
	// 	}
	// }
	.el-input__inner {
		text-align: center;
		color: rgba(104, 207, 173, 1);
		font-size: 14px;
	}
}

:deep(.el-popper) {
	.el-icon {
		color: #fff;
	}
	.el-date-picker {
		--el-datepicker-active-color: #68cfad;
		--el-datepicker-hover-text-color: #68cfad;
	}

	.el-date-picker__header-label {
		color: #fff;
	}

	.el-date-table th {
		color: #fff;
		border-bottom: solid 1px #606266;
	}
	.el-date-table td.next-month,
	.el-date-table td.prev-month {
		color: var(--el-text-color-regular);
	}

	.el-picker-panel {
		color: #fff;
	}

	.el-date-table td.today .el-date-table-cell__text {
		color: #68cfad;
		font-weight: 700;
	}
	.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
		color: #fff !important;
	}
}
</style>

<template>
	<div class="left-container">
		<!-- stat -->
		<div class="module stat">
			<div class="module-title">
				<span
					><img class="species" src="/src/assets/sd/data-screen/species.png" alt="" />物种统计</span
				>
			</div>
			<div class="module-cont stat-cont">
				<div class="stat-item" v-for="item in stat" :key="item.key">
					<div class="stat-item-left">
						<span class="count">{{ item.count }}</span>
						<span class="text">{{ item.unit }}</span>
					</div>
					<div class="stat-item-right">
						<div class="info">
							<span>{{ item.label }}占全部鸟类的</span>
							<span class="ratio text-sd">{{ item.ratio }}%</span>
						</div>
						<el-progress
							class="data-screen-progress"
							:stroke-width="20"
							striped
							:percentage="item.ratio"
							:show-text="false"
							color="rgba(49, 237, 181, 0.6)"
						/>
					</div>
				</div>
			</div>
		</div>
		<!-- recent monitor -->
		<div class="module recent-monitor">
			<div class="module-title">
				<span
					><img class="device" src="/src/assets/sd/data-screen/device.png" alt="" />最近发现
				</span>
				<img @click="onToMore" src="/src/assets/sd/data-screen/more.png" alt="" />
			</div>
			<div class="module-cont recent-monitor-cont">
				<RecentMonitor
					:recentMonitorEvents="recentMonitorEvents"
					animationName="animate__fadeInLeft"
				></RecentMonitor>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
const router = useRouter();
const route = useRoute();
import { getSpeciesStat, getSpeciesRatio, getRecentDiscovered } from '/@/api/sd/dataScreen/bird';
import { useInterval, markNewData } from '/@/hooks/useInterval';

import RecentMonitor from '../component/recentMonitor/index.vue';

const props = defineProps<{
	animalTag: number;
	animalNameList: string[];
	timeType: number | null;
}>();

// 物种统计
const stat = ref([
	{
		key: 'speciesType',
		label: '发现种类',
		unit: '种',
		count: 0,
		ratiokey: 'selectedRatiosOfSpeciesType',
		ratio: 0,
	},
	{
		key: 'speciesFrequency',
		label: '发现频次',
		unit: '次',
		count: 0,
		ratiokey: 'selectedRatiosOfSpeciesFrequency',
		ratio: 0,
	},
	{
		key: 'speciesNum',
		label: '发现数量',
		unit: '只',
		count: 0,
		ratiokey: 'selectedRatiosOfSpeciesNum',
		ratio: 0,
	},
]);
const getStat = async () => {
	const query = {
		animalNameList: props.animalNameList,
		animalTag: props.animalTag,
		timeType: props.timeType,
	};
	const { payload } = await getSpeciesStat(query);
	stat.value.forEach((item) => {
		item.count = payload[item.key] || 0;
	});
};
// 物种占比
const getRatio = async () => {
	const query = {
		animalNameList: props.animalNameList,
		animalTag: props.animalTag,
		timeType: props.timeType,
	};
	const { payload } = await getSpeciesRatio(query);
	stat.value.forEach((item) => {
		item.ratio = payload[item.ratiokey] ? Number((payload[item.ratiokey] * 100).toFixed(2)) * 1 : 0;
	});
};

// 最近发现
const onToMore = () => {
	router.push({ name: 'DataScreenAtlas', query: { pagePath: route.params?.tag } });
};
const recentMonitorEvents = ref<RecentMonitorEvent[]>([]);
const getRecentMonitorEvents = async () => {
	const query = {
		animalNameList: props.animalNameList,
		animalTag: props.animalTag,
		timeType: props.timeType,
		rankCount: 4,
	};
	const { payload } = await getRecentDiscovered(query);
	recentMonitorEvents.value = markNewData(payload.content, recentMonitorEvents.value, 'eventId');
};
useInterval(getRecentMonitorEvents);

watch(
	[() => props.animalNameList, () => props.timeType],
	() => {
		getStat();
		getRatio();
		recentMonitorEvents.value = [];
		getRecentMonitorEvents();
	},
	{ immediate: true }
);
</script>

<style lang="scss" scoped>
.stat .stat-cont {
	.stat-item {
		width: 100%;
		display: flex;
		align-items: flex-end;
		margin-bottom: vw(25);
		&-left {
			width: vw(150);
			height: vw(58);
			background: url('/@/assets/sd/data-screen/box-bg.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			vertical-align: bottom;
			display: flex;
			justify-content: space-between;
			align-items: flex-end;
			padding: vw(10) vw(10) vw(10) vw(20);
			.count {
				flex: 1;
				font-size: vw(36);
				font-family: 'ALiMaMaShuHeiTi';
			}
			.text {
				color: $sd-screen-primary;
				font-size: vw(12);
			}
		}
		&-right {
			flex: 1;
			padding-left: vw(20);
			.info {
				display: flex;
				justify-content: space-between;
				align-items: flex-end;
				font-size: vw(12);
				margin-bottom: vw(12);
				.ratio {
					font-family: 'ALiMaMaShuHeiTi';
					font-size: vw(18);
				}
			}
		}
	}
}
</style>

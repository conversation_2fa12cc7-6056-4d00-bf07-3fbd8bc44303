<template>
	<div class="layout-padding model-container">
		<el-form :model="state.ruleForm">
			<el-form-item v-if="pageType === '1'" label="红外设备" prop="deviceId" required>
				<el-select v-model="state.ruleForm.deviceId" placeholder="请选择" clearable>
					<el-option
						v-for="item in deviceOptions"
						:key="item.id"
						:label="`设备编号：${item.num}，名称：${item.channelName || item.name}`"
						:value="item.id"
					/>
				</el-select>
			</el-form-item>
			<el-form-item label="识别模型" prop="aiModelId" required>
				<el-radio-group v-model="state.ruleForm.aiModelId">
					<el-radio v-for="item in aiModels.data" :key="item.id" :label="item.id">{{
						item.name
					}}</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="图像类型" prop="recType" required>
				<el-radio-group v-model="state.ruleForm.recType">
					<el-radio :label="0">图片识别</el-radio>
					<el-radio :label="1">视频识别</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="handleUpload">
					<template #icon>
						<el-icon><ele-Upload /></el-icon>
					</template>
					本地上传
				</el-button>
				<div class="tips ml10">
					{{
						state.ruleForm.recType === 0 ? '图片支持PNG、JPG、JPEG，且最多只能选择5张图片。' : ''
					}}
				</div>
				<input
					ref="uploadInputRef"
					style="opacity: 0"
					type="file"
					:multiple="state.ruleForm.recType === 0"
					:accept="state.ruleForm.recType === 0 ? 'image/jpg,image/png,image/jpeg' : 'video/*'"
					@change="onUpload"
				/>
			</el-form-item>
		</el-form>

		<el-card
			class="result-card"
			v-loading="state.loading"
			element-loading-text="文件上传中..."
			@dragover="onDragAreaOver"
			@drop="onDropArea"
		>
			<template #header>
				<div
					class="first-level-title font15"
					:class="state.titleText.indexOf('...') !== -1 ? 'recoging' : ''"
				>
					{{ state.titleText }}
				</div>
			</template>
			<!-- 视频 -->
			<video
				v-if="state.showVideo"
				id="videoEle"
				:src="state.localVideoUrl"
				controls
				autoplay
			></video>
			<!-- 图片 -->
			<el-row v-else-if="state.resultList.length > 0" :gutter="20">
				<el-col
					:xs="24"
					:sm="12"
					:md="8"
					:lg="6"
					:xl="6"
					v-for="(item, $index) in state.resultList"
					:key="item.id"
				>
					<div class="result-pictures-item">
						<!-- 
              isRecogFail：是否识别失败
              存在pictureUrl，即识别完成；否则正在识别中
             -->
						<el-image
							:src="item.pictureUrl || item.localUrl"
							fit="cover"
							:class="item.pictureUrl ? 'active' : ''"
							:preview-src-list="perviewPicList"
							:initial-index="$index"
						/>
						<div v-if="item.recResult" class="recResults-box">
							<template v-if="item.recResult.length === 0"> 未发现物种 </template>
							<template v-else>
								<span
									v-for="rec in item.recResult"
									:key="rec.recResult"
									@click="onAssSpecies(rec.recResult)"
								>
									{{ rec.recResult }}（{{ rec.recResultCnt }}）
								</span>
							</template>
						</div>
						<div class="status">
							<el-icon class="success" v-if="item.pictureUrl"><ele-SuccessFilled /></el-icon>
							<el-icon class="loading" v-else-if="!item.isRecogFail && item.localUrl"
								><ele-Loading
							/></el-icon>
							<el-icon v-else><ele-Close /></el-icon>
						</div>
					</div>
				</el-col>
			</el-row>
			<el-empty v-else description="请选择文件进行上传" />
		</el-card>

		<SpeciesDialog ref="speciesDialogRef"></SpeciesDialog>
	</div>
</template>

<script setup lang="ts" name="Recog">
import { ref, reactive, computed, watch, defineAsyncComponent, onDeactivated } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { uploadFile, readerFile } from '/@/api/upload';
import { doRecog, getRecogResult } from '/@/api/recognize';
import { useDevices } from '/@/hooks/useDevices';
import { useAiModels } from '/@/hooks/useAiModels';

const SpeciesDialog = defineAsyncComponent(
	() => import('/@/views/monitorEvents/speciesDialog.vue')
);

const devices = useDevices();
const aiModels = reactive(useAiModels());
const route = useRoute();
const uploadInputRef = ref<HTMLInputElement>();
const state = reactive<ModelState>({
	ruleForm: {
		deviceId: '',
		aiModelId: '',
		recType: 0,
	},
	deviceId: '', // 选择的设备id

	titleText: '识别结果',
	loading: false,
	fileIds: [], // 上传文件ids
	showVideo: false,
	localVideoUrl: '', // 视频识别时本地视频url
	monitorId: '', // 本次识别记录id
	resultList: [], // 识别结果列表
	intervalTimer: null,
});

onDeactivated(() => {
	resetData();
});
// pageType: 1红外设备 2测试设备
const pageType = computed(() => {
	return route.path.includes('test') ? '2' : '1';
});
const deviceOptions = computed(() => {
	return devices.data.value.filter((item: DeviceRow) => item.sourceType === 2);
});
// 默认选中第一个模型
watch(
	() => aiModels.data,
	(val) => {
		state.ruleForm.aiModelId = val && val.length > 0 ? val[0].id : '';
	}
);

// 本地上传
const handleUpload = async () => {
	// 红外设备，deviceId不能为空
	if (pageType.value === '1' && !state.ruleForm.deviceId) {
		ElMessage.error('请先选择设备');
		return;
	}
	uploadInputRef.value?.click();
};
const onUpload = async (event: Event | null, dragFiles?: FileList) => {
	const files = event
		? ((<HTMLInputElement>event.target).files as FileList)
		: (dragFiles as FileList);
	if (files.length > 5) {
		ElMessage.error('最多只能选择5张图片哦');
		return;
	}
	// 重置数据
	resetData();

	try {
		state.loading = true;
		const formData = new FormData();
		const localPicUrls: string[] = []; // 图片识别时，已上传图片本地url集合
		for (const file of files) {
			formData.append('file', file);

			// 如果是图片，读取图片，生成本地图片url；
			if (state.ruleForm.recType === 0) {
				const res = await readerFile(file);
				// console.log('res', res);
				if (typeof res === 'string') localPicUrls.push(res);
			} else {
				// 如果是视频，生成本地视频url；
				state.localVideoUrl = URL.createObjectURL(file);
			}
		}
		const { payload } = await uploadFile(formData);
		state.loading = false;
		ElMessage.success('文件上传成功，正在识别中');

		const fileIds: string[] = []; // 已上传文件ids
		const temp: CustomResult[] = []; // 预览图片列表
		payload.forEach((item: any, $index: number) => {
			fileIds.push(item.id);
			temp.push({
				pictureId: item.id,
				localUrl: localPicUrls[$index], // 本地图片url
				isRecogFail: false, // 是否识别失败
			});
		});
		state.fileIds = fileIds;
		if (state.ruleForm.recType === 0) {
			state.showVideo = false;
			state.resultList = temp;
		} else {
			state.showVideo = true;
			state.resultList = [];
		}
		// 清除文件输入的值，相当于取消选择文件
		(uploadInputRef.value as HTMLInputElement).value = '';
		onDoRecog();
	} catch (e) {
		state.loading = false;
		(uploadInputRef.value as HTMLInputElement).value = '';
	}
};
// 拖拽上传
const onDragAreaOver = (event: DragEvent) => {
	// 阻止默认行为，允许放置文件
	event.preventDefault();
	event.stopPropagation();
	(<DataTransfer>event.dataTransfer).dropEffect = 'move';
};
const onDropArea = (event: DragEvent) => {
	event.preventDefault();
	event.stopPropagation();
	// 文件上传处理器，dataTransfer
	const files = event.dataTransfer?.files as FileList;
	onUpload(null, files);
};
// 识别
const onDoRecog = async () => {
	const data = {
		aiModelId: state.ruleForm.aiModelId,
		fileIds: state.fileIds,
		deviceId: pageType.value === '1' ? state.ruleForm.deviceId : null,
		recType: state.ruleForm.recType,
	};
	state.titleText = '正在识别中...';
	try {
		const { payload } = await doRecog(data);
		// 保存本次识别记录id，后续定时获取识别结果
		state.monitorId = payload.monitorId;
		onGetRecogResult();
		initIntervalTimer();
	} catch (e) {
		resetData();
	}
};
// 获取识别结果
const onGetRecogResult = async () => {
	const data = {
		monitorId: state.monitorId,
		aiModelId: state.ruleForm.aiModelId,
		fileIds: state.fileIds,
		deviceId: pageType.value === '1' ? state.ruleForm.deviceId : null,
		recType: state.ruleForm.recType,
	};
	const { payload } = await getRecogResult(data);
	// end为true，识别完成，清除定时器
	const results = payload.monitorEvents;
	if (payload.end && results.length === 0) {
		state.resultList.forEach((item: CustomResult) => (item.isRecogFail = true));
		ElMessage.error('文件识别失败，请检查模型后重新上传');
		state.titleText = '识别结果';
		clrearIntervalTimer();
		return;
	}
	if (payload.end) {
		ElMessage.success('识别结束');
		state.titleText = '识别结果';
		clrearIntervalTimer();
	}
	if (results.length === 0) return;
	// pictures中monitorEventDetails有数据时代表识别到物种
	if (state.ruleForm.recType === 0) {
		results.forEach((item: MResultItem) => {
			const index = state.resultList.findIndex(
				(f: CustomResult) => f.pictureId === item.oriPictureId
			);
			const { pictures } = item;
			state.resultList.splice(index, 1, {
				...state.resultList[index],
				pictureUrl:
					pictures[0].monitorEventDetails.length > 0
						? pictures[0].pictureUrl
						: pictures[0].oriPictureUrl,
				recResult: pictures[0].monitorEventDetails,
			});
		});
	} else {
		// 视频识别时，停止播放本地视频url，更新结果
		state.showVideo = false;
		state.resultList = results[0].pictures.map((item: MPictureItem) => ({
			pictureUrl: item.monitorEventDetails.length > 0 ? item.pictureUrl : item.oriPictureUrl,
			recResult: item.monitorEventDetails,
			isRecogFail: false,
		}));
	}
};
// 点击查看大图
const perviewPicList = computed(() => {
	return state.resultList.map((item: CustomResult) => item.pictureUrl || item.localUrl);
});

const resetData = () => {
	state.titleText = '识别结果';
	state.showVideo = false;
	state.fileIds = [];
	state.resultList = [];
	state.monitorId = '';
	clrearIntervalTimer();
};
const initIntervalTimer = () => {
	clrearIntervalTimer();
	state.intervalTimer = setInterval(onGetRecogResult, 3000);
};
const clrearIntervalTimer = () => {
	if (state.intervalTimer) {
		clearInterval(state.intervalTimer);
		state.intervalTimer = null;
	}
};

// 识别结果链接物种百科
const speciesDialogRef = ref<InstanceType<typeof SpeciesDialog>>();
// @param name(识别结果名称)，根据识别结果查询关联物种百科数据
const onAssSpecies = async (name: string) => {
	speciesDialogRef.value?.getAssSpecies(name);
};
</script>

<style lang="scss" scoped>
.model-container {
	display: flex;
	flex-direction: column;
	padding-left: 20px !important;

	:deep(.result-card) {
		flex: 1;
		.first-level-title {
			&::before {
				margin-right: 10px;
			}
			&.recoging::before {
				animation: loading-rotate 3s linear infinite;
			}
		}

		.el-card__body {
			height: calc(100% - 50px);
			overflow-y: auto;
		}
		#videoEle {
			height: 100%;
		}
		.result-pictures-item {
			overflow: hidden;
			margin-bottom: 20px;
			position: relative;
			& > .el-image {
				width: 100%;
				padding-top: 75% !important;
				& > img {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					object-fit: cover;
					opacity: 0.5;
					transition: all 0.3s ease;
					cursor: pointer;
				}
				&:hover > img {
					transform: scale(1.05);
				}
				&.active > img {
					opacity: 1;
				}
			}
			.recResults-box {
				width: 100%;
				position: absolute;
				bottom: 0;
				left: 0;
				color: #fff;
				background-color: rgba(0, 0, 0, 0.6);
				padding: 5px;
				& > span:hover {
					cursor: pointer;
					color: var(--el-color-primary);
				}
			}
			.status {
				position: absolute;
				width: 50px;
				height: 50px;
				border-radius: 50%;
				right: -25px;
				top: -25px;
				background-color: rgba(0, 0, 0, 0.6);
				.el-icon {
					position: absolute;
					left: 7px;
					bottom: 7px;
					color: #fff;
					font-size: 16px;
				}
				.el-icon.loading {
					color: #fff;
					animation: loading-rotate 3s linear infinite;
				}
				.el-icon.success {
					color: var(--el-color-success-light-3);
				}
			}
		}
	}

	.tips {
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: var(--el-text-color-secondary);
		font-size: 12px;
		.iconfont {
			display: block;
			font-size: 40px;
			margin-bottom: 10px;
		}
	}
	.left-image-box {
		height: 100%;
		border: 1px dashed var(--el-border-color);
		box-shadow: 0 0 4px 2px #f5f4f4;
		& > img {
			width: 100%;
			height: 100%;
			object-fit: contain;
		}
	}
}
</style>

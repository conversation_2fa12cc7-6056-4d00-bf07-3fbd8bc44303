<template>
	<div class="right-container">
		<!-- species frequency -->
		<div class="module species-frequency">
			<div class="module-title">
				<span
					><img class="paiming" src="/src/assets/sd/data-screen/paiming.png" alt="" />物种频次排名
				</span>
			</div>

			<div class="module-cont species-frequency-cont">
				<div class="rank-list">
					<div class="rank-item rank-title">
						<span class="rank">名次</span>
						<span class="name">排名</span>
						<span class="progress">频次占比</span>
						<span class="count">频次</span>
					</div>

					<div class="legend">
						<span> <i class="legend-specific"></i>珍稀</span>
						<span> <i class="legend-precious"></i>特定</span>
						<span v-if="IsCompanyNmg"> <i class="legend-nmg"></i>内蒙古</span>
					</div>
					<div class="rank-item" v-for="(item, $index) in speciesFrequencyRanks" :key="$index">
						<span class="rank">{{ $index + 1 }}</span>
						<span class="name">
							<span>{{ item.speciesAlias }}</span>
							<i v-if="item.tags === '珍稀鸟类'" class="legend-specific"></i>
							<i v-else-if="item.tags === '特殊鸟类'" class="legend-precious"></i>
							<i v-else-if="item.tags === '内蒙古鸟类'" class="legend-nmg"></i>
							<!-- <span v-if="item.tags" class="tag-tooltips">
								<el-tooltip effect="dark" placement="bottom" :content="item.tags">
									<ele-InfoFilled />
								</el-tooltip>
							</span> -->
						</span>
						<span class="progress">
							<el-progress
								class="data-screen-progress"
								:stroke-width="15"
								striped
								:percentage="item.ratio"
								color="rgba(49, 237, 181, 0.8)"
							/>
						</span>
						<div class="count">{{ item.speciesFrequency }}</div>
					</div>
				</div>
			</div>
		</div>
		<!-- monitor trend -->
		<div class="module monitor-trend">
			<div class="module-title">
				<span
					><img class="trend" src="/src/assets/sd/data-screen/trend.png" alt="" />种群栖息趋势
				</span>
			</div>

			<div class="module-cont monitor-trend-cont">
				<Echarts id="populationTrendChart" ref="populationTrendChartRef"></Echarts>
			</div>
		</div>
		<!-- residence-type -->
		<div class="module residence-type">
			<div class="module-title">
				<span
					><img class="juliu" src="/src/assets/sd/data-screen/juliu.png" alt="" />居留类型
				</span>
			</div>

			<div class="residence-type-cont">
				<Echarts id="residencyTypeChart" ref="residencyTypeChartRef"></Echarts>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import {
	mergeLineOptions,
	mergePieOptions,
	ECOption,
	chartLinearColors,
	transDate,
} from '/@/components/echarts/mergeOptions';
import {
	getSpeciesPopulationTrend,
	getResidencyStat,
	getSpeciesFrequencyRank,
} from '/@/api/sd/dataScreen/bird';
import Echarts from '/@/components/echarts/echarts.vue';
import { IsCompanyNmg } from '/@/hooks/useConfig';

const props = defineProps<{
	timeType: number | null;
}>();

// 物种频次排名
const speciesFrequencyRanks = ref<SpeciesEvent[]>([]);
const getSpeciesFrequencyRanks = async () => {
	const query = {
		timeType: props.timeType,
	};
	const { payload } = await getSpeciesFrequencyRank(query);
	speciesFrequencyRanks.value = payload.slice(0, 5).map((item: SpeciesEvent) => {
		return {
			...item,
			ratio: Number(((item.speciesFrequency / item.totalSpeciesFrequency) * 100).toFixed(1)),
			// tags: item.tags.replace(/鸟类/g, '').split('、'),
		};
	});
};

// 种群栖息趋势
const populationTrendChartRef = ref<InstanceType<typeof Echarts>>();
const getPopulationMonitorTrend = async () => {
	const query = {
		timeType: props.timeType,
	};
	const { payload } = await getSpeciesPopulationTrend(query);
	const chartData = payload.map((et: SpeciesEvent) => {
		const date = transDate(props.timeType, et.date);
		return [date, et.speciesNum];
	});
	const options: ECOption = {
		dataset: {
			source: chartData,
		},
		color: [chartLinearColors[0]],
		series: [
			{
				type: 'line',
				name: '发现种群数量',
				symbol: 'circle',
				symbolSize: 6,
				showSymbol: false,
				areaStyle: {
					color: chartLinearColors[2],
				},
			},
		],
	};
	populationTrendChartRef.value?.resetOption(mergeLineOptions(options));
};

// 居留类型
const residencyTypeChartRef = ref<InstanceType<typeof Echarts>>();
const getResidencyTypeStat = async () => {
	const query = {
		timeType: props.timeType,
	};
	const { payload } = await getResidencyStat(query);
	const options = {
		tooltip: {
			formatter: (params: any) => {
				return `发现${params.name}：${params.value['value']}次`;
			},
		},
		dataset: {
			source: payload.map((item: { typeText: string; num: number }) => ({
				name: item.typeText,
				value: item.num,
			})),
		},
	};
	residencyTypeChartRef.value?.resetOption(mergePieOptions(options));
};

watch(
	() => props.timeType,
	() => {
		getPopulationMonitorTrend();
		getResidencyTypeStat();
		getSpeciesFrequencyRanks();
	},
	{ immediate: true }
);
</script>

<style lang="scss" scoped>
.recent-monitor .species {
	min-height: vw(236) !important;
	max-height: vw(236) !important;
}
.monitor-trend .monitor-trend-cont {
	height: vh(260);
}
.residence-type .residence-type-cont {
	height: vh(220);
}

.species-frequency .species-frequency-cont {
	overflow: visible;
	.rank-list {
		.legend {
			height: vh(37);
			display: flex;
			justify-content: flex-end;
			align-items: center !important;

			span {
				font-size: vw(12);
				margin-right: vw(30);
			}
		}
		i {
			display: inline-block;
			font-style: normal;
			width: 10px;
			height: 10px;
			border-radius: 50%;
			margin-right: vw(5);
		}
		.legend-specific {
			background-color: #ffda00;
		}
		.legend-precious {
			background-color: #fc74d9;
		}
		.legend-nmg {
			background-color: #5af6e8;
		}
		.rank-title {
			height: vw(43);
			font-size: vw(14) !important;
			background: url('/@/assets/sd/data-screen/list-title.png');
			background-repeat: no-repeat;
			background-size: 88% 100%;
			background-position: center;
			padding-bottom: 0 !important;
		}
		width: calc(100% + vw(40));
		position: relative;
		left: vw(-20);
		min-height: vw(245);
		max-height: vw(245);
		background: url('/@/assets/sd/data-screen/rank-bg.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		// background: linear-gradient(to bottom, transparent, rgba(33, 227, 221, 0.2) 40%, rgba(79, 229, 181, 0.2) 60%, transparent);
		.rank-item {
			display: flex;
			align-items: center;
			padding: 0 vw(36) vw(15);
			text-align: center;

			.name {
				width: vw(100);
				span {
					padding-right: vw(5);
				}
				.tag-tooltips {
					display: inline-block;
					width: vw(16);
					height: vw(16);
					vertical-align: text-top;
					margin-left: vw(4);
					color: $sd-screen-primary;
					cursor: pointer;
				}
			}

			.rank {
				width: vw(30);
			}

			.progress {
				width: vw(150);
			}
			.count {
				flex: 1;
			}
			// .tag {
			//   border: 1px solid $sd-screen-primary;
			//   color: $sd-screen-primary;
			//   font-size: vw(12);
			//   padding: 0 vw(8);
			//   border-radius: vw(8);
			//   margin-left: vw(10);
			// }
		}
	}
}
</style>

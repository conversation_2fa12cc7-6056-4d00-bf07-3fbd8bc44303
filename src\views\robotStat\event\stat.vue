<template>
	<div class="stat-container">
		<div class="zhibiao-stat">
			<div class="zhibiao-stat-item" v-for="(item, index) in zhibiaoData" :key="index">
				<img class="left-box" :src="item.iconUrl" alt="" />
				<div class="right-box">
					<div class="right-box-top">{{ item.label }}</div>
					<div class="right-box-bottom">{{ item.value }}</div>
				</div>
			</div>
		</div>
		<div class="trend">
			<Echarts class="echarts" ref="mileageRef" id="mileage-statistics" />
		</div>
		<div class="stat-info" v-if="pieShow">
			<Echarts class="echarts" ref="pieRef" id="pie-statistics" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from 'vue';
import Echarts from '/@/components/echarts/echarts.vue';
import echartsOption from '/@/hooks/echartsOption';
import other from '/@/utils/other';
import { getRobotsEventsStat } from '/@/api/robotStat';
type Porps = {
	seriesColors: EmptyObjectType;
	date: string;
};
const props = defineProps<Porps>();

const { mileageOption } = echartsOption();
const mileageRef = ref();

const pieShow = ref(true);
const zhibiaoData = reactive([
	{
		iconUrl: other.getStaticImag('fullScreen/robotStat/event/leiji.png'),
		label: '事件数',
		value: 0,
	},
	{
		iconUrl: other.getStaticImag('fullScreen/robotStat/event/benzhou.png'),
		label: '事件数',
		value: 0,
	},
	{
		iconUrl: other.getStaticImag('fullScreen/robotStat/event/benyue.png'),
		label: '事件数',
		value: 0,
	},
	{
		iconUrl: other.getStaticImag('fullScreen/robotStat/event/bennian.png'),
		label: '事件数',
		value: 0,
	},
]);

const pieRef = ref<typeof Echarts>();
interface Statistics {
	count: number;
	name: string;
	percent?: number;
	value: number;
	eventLevel: number;
	color: string;
	eventType: number;
}
// 事件类型
const handleOverviewStat = (allDatalegend: string[], allData: any) => {
	const icon = {
		align: 'left',
		width: 11,
		height: 11,
	};
	const option = {
		backgroundColor: 'transparent',
		tooltip: {
			trigger: 'item',
			formatter: (params: any) => {
				if (params.data.subEventStatistics) {
					let html = ``;
					// const total = params.data.subEventStatistics.reduce(
					//   (accumulator: any, item: { count: number }) => accumulator + item.count,
					//   0
					// );
					params.data.subEventStatistics.forEach((item: any) => {
						// html += `<div>${item.name}：${Number(((item.count / total) * 100).toFixed(2))}%</div>`;
						html += `<div>${item.name}：${item.count}</div>`;
					});
					return html;
				}
				return `${params.name}：${params.value}`;
			},
			textStyle: {
				color: '#fff',
			},
			backgroundColor: 'rgba(50,50,50,0.7)',
			borderColor: 'rgba(104, 207, 173, 1)',
		},
		legend: {
			show: true,
			type: 'scroll',
			orient: 'vertical',
			right: 8,
			padding: [30, 0, 0, 0],
			top: 'center',
			itemGap: 8,
			icon: 'none',
			pageIconColor: '#67cfac',
			pageIconSize: [12, 12],
			pageTextStyle: {
				color: '#fff',
			},
			textStyle: {
				color: '#fff',
				fontSize: 12,
				rich: {
					0: {
						...icon,
						backgroundColor: props.seriesColors[0], // 设置线的颜色
					},
					1: {
						...icon,
						backgroundColor: props.seriesColors[1], // 设置线的颜色
					},
					2: {
						...icon,
						backgroundColor: props.seriesColors[2], // 设置线的颜色
					},
					3: {
						...icon,
						backgroundColor: props.seriesColors[3], // 设置线的颜色
					},
					4: {
						...icon,
						backgroundColor: props.seriesColors[4], // 设置线的颜色
					},
					5: {
						...icon,
						backgroundColor: props.seriesColors[5], // 设置线的颜色
					},
					6: {
						...icon,
						backgroundColor: props.seriesColors[6], // 设置线的颜色
					},
					7: {
						...icon,
						backgroundColor: props.seriesColors[7], // 设置线的颜色
					},
					8: {
						...icon,
						backgroundColor: props.seriesColors[8], // 设置线的颜色
					},
					9: {
						...icon,
						backgroundColor: props.seriesColors[9], // 设置线的颜色
					},
					10: {
						...icon,
						backgroundColor: props.seriesColors[10], // 设置线的颜色
					},
					11: {
						...icon,
						backgroundColor: props.seriesColors[11], // 设置线的颜色
					},
					13: {
						...icon,
						backgroundColor: props.seriesColors[13], // 设置线的颜色
					},
					15: {
						...icon,
						backgroundColor: props.seriesColors[15], // 设置线的颜色
					},
					16: {
						...icon,
						backgroundColor: props.seriesColors[16], // 设置线的颜色
					},
					17: {
						...icon,
						backgroundColor: props.seriesColors[17], // 设置线的颜色
					},
					uname: {
						align: 'left',
						width: 65,
						verticalAlign: 'bottom',
					},
					unum: {
						width: 45,
						align: 'center',
						verticalAlign: 'middle',
					},
					middle: {
						width: 110,
						height: 1,
						backgroundColor: 'rgba(255, 255, 255, .2)',
						verticalAlign: 'middle',
					},
					square: {
						width: 8,
						height: 8,
						backgroundColor: 'rgba(255, 255, 255, .2)',
						borderWidth: 0,
						verticalAlign: 'middle',
					},
				},
			},
			data: allDatalegend,
			formatter(name: string) {
				const nameItem = allData.find((item: Statistics) => item.name === name);
				if (nameItem) {
					// return `{${nameItem.eventType}|} {uname|${name}}{unum|${nameItem.percent}%}\n{middle|}{square|}`;
					return `{${nameItem.eventType}|} {uname|${name}}{unum|${nameItem.count}}\n{middle|}{square|}`;
				}
			},
		},
		series: [
			{
				name: '统计信息',
				type: 'pie',
				left: -140,
				radius: ['28%', '40%'],
				color: props.seriesColors,
				label: {
					show: true,
					color: '#fff',
					position: 'outside',
					formatter: (params: any) => {
						return `{green|${params.data.percent}%}\n${params.name}`;
					},
					rich: {
						green: {
							padding: [3, 0],
							color: '#67cfac',
						},
					},
				},
				labelLine: {
					length: 9,
					length2: 5,
					showAbove: true,
				},
				data: allData,
			},
			{
				name: '内部虚线',
				type: 'pie',
				silent: true, // 不响应和触发鼠标事件
				left: -140,
				radius: ['21%', '22%'],
				color: ['#606664'],
				labelline: {
					show: false,
				},
				label: {
					show: false,
				},
				itemStyle: {
					normal: {
						color: (a: { data: number }) => {
							if (a.data == 1) {
								return '#606664';
							}
							if (a.data == 1) {
								return 'transparent';
							}
						},
					},
				},
				data: [
					2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1,
					2, 1,
				],
			},
			{
				name: '内部实线',
				type: 'pie',
				silent: true,
				left: -140,
				radius: ['5%', '6%'],
				color: ['#67cfac'],
				labelline: {
					show: false,
				},
				label: {
					show: false,
				},
				data: [{ value: 222, name: '内部实线' }],
			},
		],
	};
	nextTick(() => {
		pieRef.value?.resetOption(option);
	});
};
// 获取指标统计 和 事件趋势
const onGetRobotsEventsStat = async (params: { eventType?: number; groupEventType?: number }) => {
	const query = {
		...params,
	};
	const { payload } = await getRobotsEventsStat(query);
	// 右侧指标统计
	const { total, week, month, year } = payload.overview;
	zhibiaoData[0].value = total;
	zhibiaoData[1].value = week;
	zhibiaoData[2].value = month;
	zhibiaoData[3].value = year;
	// 事件趋势
	const xData: string[] = [];
	const sData: number[] = [];
	payload.trend.forEach((item: any) => {
		xData.push(item.time.substring(5, 10));
		sData.push(item.count || 0);
	});
	nextTick(() => {
		const option = mileageOption(xData, sData);
		option.series.name = '事件数量';
		mileageRef.value?.resetOption(option);
	});
};
defineExpose({
	handleOverviewStat,
	onGetRobotsEventsStat,
	pieShow,
});
</script>

<style lang="scss" scoped>
%pt45 {
	padding-top: vh(45);
}
%bg-r-s {
	background-repeat: no-repeat;
	background-size: 100% 100%;
}
.stat-container {
	width: 355px;
	height: 100%;
	background-color: transparent;

	.zhibiao-stat {
		@extend %bg-r-s;
		padding: vh(45) vw(20) 0 0;
		height: vh(216);
		background-image: url('/src/assets/fullScreen/robotStat/event/zhibiao.png');
		display: flex;
		flex-wrap: wrap;
		&-item {
			width: 50%;
			height: 50%;
			display: flex;
			align-items: center;
			.left-box {
				width: vw(80);
				height: vh(65);
				object-fit: cover;
			}

			.right-box {
				flex: 1;
				height: 100%;
				&-top {
					height: 50%;
					line-height: 4.5;
					color: rgba(152, 247, 215, 1);
					font-size: vw(12);
				}

				&-bottom {
					height: 50%;
					line-height: 1;
					font-size: vw(24);
				}
			}
		}
	}

	.trend {
		@extend %bg-r-s;
		@extend %pt45;
		padding-bottom: vh(20);
		margin: vh(20) 0;
		height: vh(345);
		background-image: url('/src/assets/fullScreen/robotStat/event/shijianqushi.png');
		padding-right: vh(14);
	}

	.stat-info {
		@extend %bg-r-s;
		@extend %pt45;
		background-image: url('/src/assets/fullScreen/robotStat/event/shijianleixing.png');
		height: calc(100% - vh(345) - vh(216) - vh(40));
	}
}
</style>

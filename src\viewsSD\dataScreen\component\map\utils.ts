import { bd09_To_gps84, bd09_To_gcj02, gps84_To_bd09, gps84_To_gcj02, gcj02_To_bd09, gcj02_To_gps84 } from '@vuemap/vue-amap';
// import { transform } from 'ol/proj';
import { getDistance } from 'ol/sphere';

type MapColor = 'gray' | 'blue' | 'darkgreen' | 'black';

/**
 * 转换经纬度
 * @param coord 经纬度数组
 * @param current 当前（coord）经纬度类型
 * @param target 目标经纬度类型
 * @returns 
 */
// 坐标系类型 0：bd09  1：wgs84  2：GCJ02
const transCoordinateMap: { [key: string]: Function } = {
  '0-1': bd09_To_gps84,
  '0-2': bd09_To_gcj02,
  '1-0': gps84_To_bd09,
  '1-2': gps84_To_gcj02,
  '2-0': gcj02_To_bd09,
  '2-1': gcj02_To_gps84,
}
export function transCoordinate(coord: [number, number], current: number, target: number) {
  if (!coord[0] && !coord[1]) return [0, 0];
  current = current || 2;
  if (current === target) return coord;
  const { lng, lat } = transCoordinateMap[(`${current}-${target}`) as string](coord[0], coord[1]);
  return [lng, lat];
}

// export function gps84_To_ol (coordinate: [number, number]) {
//   return transform(coordinate, 'EPSG:4326', 'EPSG:3857');
// }

/**
 * 自定义图层主题色，当切片加载时，遍历每个像素点，根据mapColor转换颜色值
 * @param imageTile 
 * @param src 图片地址
 * @param mapColor 图层颜色
 */
export function customTileLoadFun(imageTile: any, src: string, mapColor: MapColor) {
  let img = new Image();
  img.setAttribute('crossOrigin', 'anonymous')
  img.onload = function () {
    const canvas = document.createElement('canvas');
    const w = img.width;
    const h = img.height;
    canvas.width = w;
    canvas.height = h;
    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
    // ctx.filter = 'grayscale(100%) invert(100%) sepia(20%) hue-rotate(125deg) saturate(1200%) brightness(100%)';
    // ctx.filter = 'grayscale(100%) invert(100%) sepia(20%) hue-rotate(135deg) saturate(1000%) brightness(80%)';
    ctx.drawImage(img, 0, 0, w, h, 0, 0, w, h);

    const imageData = ctx.getImageData(0, 0, w, h);
    const { data } = imageData;
    for (let i = 0; i < data.length; i += 4) {
      const gray = 0.3 * data[i] + 0.59 * data[i + 1] + 0.11 * data[i + 2];
      // 灰色
      if (mapColor === 'gray') {
        data[i] = gray
        data[i + 1] = gray;
        data[i + 2] = gray;
      }
      // 蓝色
      if (mapColor === 'blue') {
        const gray = 0.3 * data[i] + 0.59 * data[i + 1] + 0.11 * data[i + 2];
        data[i] = 55 - gray
        data[i + 1] = 255 - gray;
        data[i + 2] = 305 - gray;
      }
      // 绿色
      if (mapColor === 'darkgreen') {
        const gray = 0.3 * data[i] + 0.59 * data[i + 1] + 0.11 * data[i + 2];
        data[i] = 33 - gray
        data[i + 1] = 255 - gray;
        data[i + 2] = 255 - gray;
      }
      // 黑色
      if (mapColor === 'black') {
        const gray = 0.3 * data[i] + 0.59 * data[i + 1] + 0.11 * data[i + 2];
        data[i] = 255 - gray
        data[i + 1] = 255 - gray;
        data[i + 2] = 255 - gray;
      }
    }
    ctx.putImageData(imageData, 0, 0);

    imageTile.getImage().src = canvas.toDataURL('image/png');
  }
  img.src = src;
}

// 点击设备点位时，根据鼠标位置，自适应window
const screenWidth = window.innerWidth || document.body.clientWidth;
const screenHeight = window.innerHeight || document.body.clientHeight;

export function calculateWindowOffset(event: MouseEvent, type: 'video' | 'rank' = 'rank') {
  const xBoundary = type === 'video' ? 0.55 : 0.625; // x最大位置比
  const yBoundary = type === 'video' ? 0.42 : 0.715; // y最大位置比
  let eventX = event.clientX + 40;
  let eventY = event.clientY;
  let panX = 0, panY = 0;

  if (eventX / screenWidth > xBoundary) {
    panX = screenWidth * xBoundary - eventX;
    eventX = screenWidth * xBoundary;
  }
  if (eventY / screenHeight > yBoundary) {
    panY = screenHeight * yBoundary - eventY;
    eventY = screenHeight * yBoundary;
  }

  return {
    eventX,
    eventY,
    panX,
    panY,
  };
}

/**
 * 生成包含{min}至{max}之间的随机整数
 * @param min 最小值
 * @param max 最大值
 * @returns 随机数
 */
export const randomInteger = (min = 0, max = 50) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};


/**
 * 生成二次贝塞尔曲线插值点，公式B(t) = (1−t)^2*P0 + 2(1−t)*t*P1 + t^2 * P2
 * @param start 起始点坐标
 * @param end 控制点坐标
 * @returns 
 */
export function createBezierCurvePoints(P0: number[], P2: number[]): [number, number][] {
  const height = getDistance(P0, P2) / (400000); // 米
  // 控制点
  const P1 = {
    x: (P0[0] + P2[0]) / 2,
    y: (P0[1] + P2[1]) / 2 + height,
  };

  const points: [number, number][] = [];
  for (let t = 0; t <= 1;) {
    const Btx = Math.pow(1 - t, 2) * P0[0] + 2 * (1 - t) * t * P1.x + Math.pow(t, 2) * P2[0];
    const Bty = Math.pow(1 - t, 2) * P0[1] + 2 * (1 - t) * t * P1.y + Math.pow(t, 2) * P2[1];
    points.push([Btx, Bty]);
    t = t + 0.01;
    // 浮点数运算可能会导致精度问题
    t = parseFloat(t.toFixed(2));
  }

  return points;
}

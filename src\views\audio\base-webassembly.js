const env = {
  memoryBase: 102400,
  tableBase: 0,
  memory: new WebAssembly.Memory({
    initial: 256, maximum: 256
  }),
  table: new WebAssembly.Table({
    initial: 0,
    maximum: 0,
    element: 'anyfunc'
  })
}
class BaseWebassembly {
  static setPath(path) {
    BaseWebassembly.path = path
    return BaseWebassembly
  }

  static async getSingleton() {
    // debugger
    if (!BaseWebassembly.singleton) {
      console.log(BaseWebassembly.singleton)
      BaseWebassembly.singleton = await BaseWebassembly.loadWebAssembly(BaseWebassembly.path)
    }
    return BaseWebassembly.singleton
  }

  static loadWebAssembly = async(url) => {
    const instance = await fetch(url).then((response) => {
      return response.arrayBuffer()
    }).then((bytes) => {
      return WebAssembly.instantiate(bytes, { env: env })
    }).then((instance) => {
      return instance.instance.exports
    })
    BaseWebassembly.memory = new Uint8Array(env.memory.buffer)
    return instance
  };

  static copyToMemory(data) {
    BaseWebassembly.memory.set(new Uint8Array(data.buffer, data.byteOffset, data.byteLength))
  }

  static encodeG711a(data) {
    BaseWebassembly.copyToMemory(data)
    BaseWebassembly.singleton._encodeG711a(data.byteLength, 0, data.byteLength >>> 1)
    return new Uint8Array(env.memory.buffer, data.byteLength, data.byteLength >>> 1)
  }

  static decodeG711a(data) {
    BaseWebassembly.copyToMemory(data)
    BaseWebassembly.singleton._decodeG711a(data.byteLength, 0, data.byteLength)
    return new Int16Array(env.memory.buffer, data.byteLength, data.byteLength)
  }
}

export default BaseWebassembly

/* BaseWebassembly.setPath('g711a/audio.wasm').getSingleton().then(wasm => {
  vueInstance = that
  if (isDebugger) {
    onWsOpen().then(r => {})
  } else {
    WsInstance = new Ws(url, undefined, false)
    WsInstance.subscribe('onWsOpen', onWsOpen)
    WsInstance.subscribe('handleWsBlob', handleWsBlob)
    WsInstance.start()
  }
})*/

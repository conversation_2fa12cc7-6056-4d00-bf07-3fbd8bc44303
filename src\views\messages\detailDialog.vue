<template>
	<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="680px" align-center>
		<template v-if="state.data.monitorEventFileModel">
			<el-image
				style="width: 100%; height: 382px"
				:src="state.data.monitorEventFileModel.pictureUrl || state.data.monitorEventFileModel.oriPictureUrl"
				:zoom-rate="1.2"
				:max-scale="7"
				:min-scale="0.2"
				:preview-src-list="[state.data.monitorEventFileModel.pictureUrl || state.data.monitorEventFileModel.oriPictureUrl]"
				:initial-index="4"
				fit="contain"
			/>
		</template>
		<template v-else-if="state.data.deviceModel">
			<el-descriptions title="" :column="1">
				<el-descriptions-item label="设备名称:">{{ state.data.deviceModel.name || '-' }}</el-descriptions-item>
				<el-descriptions-item label="通道编号:">{{ state.data.deviceModel.channelNo || '-' }}</el-descriptions-item>
				<el-descriptions-item label="视频地址:">{{ state.data.deviceModel.previewUrl || '-' }}</el-descriptions-item>
			</el-descriptions>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';

type Data = {
	deviceModel: {
		name: string;
		channelNo: string;
		previewUrl: string;
	} | null;
	monitorEventFileModel: {
		pictureUrl: string;
		oriPictureUrl: string;
	} | null;
};
// 用reactive时数据回显失败
const state = ref({
	dialog: {
		isShowDialog: false,
		type: '',
		title: '消息详情',
	},
	data: {} as Data,
	filter: {
		targetId: null,
		targetType: null,
		text: '',
	},
	deviceIsMultiple: false,
	deviceIsDisabled: false,
});
const openDialog = (data: Data) => {
	state.value.data = data;
	state.value.dialog.isShowDialog = true;
};

const onCancel = () => {
	state.value.dialog.isShowDialog = false;
};

defineExpose({
	openDialog,
});
</script>

<style>
.el-table .warning-row {
	--el-table-tr-bg-color: var(--el-color-warning-light-9);
}

.el-descriptions__label {
	font-weight: 700;
}
</style>

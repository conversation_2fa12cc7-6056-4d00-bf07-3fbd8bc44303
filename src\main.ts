import { createApp } from 'vue';
import pinia from '/@/stores/index';
import App from '/@/App.vue';
import router from '/@/router';
import { directive } from '/@/directive/index';
import other from '/@/utils/other';
import { CountTo } from 'vue3-count-to';

import ElementPlus from 'element-plus';
import '/@/theme/index.scss';

import VueAMap, { initAMapApiLoader } from '@vuemap/vue-amap';
import '@vuemap/vue-amap/dist/style.css';
// import BaiduMap from 'vue-baidu-map-3x'
import VueAMapLoca from '@vuemap/vue-amap-loca';

initAMapApiLoader({
  key: '0cc1891d36108c34b308b973c65cbe8f',
  securityJsCode: 'bfcf28a848531e66d320129b92107012', // 新版key需要配合安全密钥使用
  Loca: {
    version: '2.0.0'
  } // 如果需要使用loca组件库，需要加载Loca
  // plugins: ['AMap.Geolocation']
})

const app = createApp(App);

directive(app);
other.elSvg(app);

app.use(pinia)
  .use(router)
  .use(ElementPlus)
  .use(VueAMap)
  // .use(BaiduMap, {
  //   ak: 'dL2aIAzoABItzyuuXgsNtScRmF0MNGwO',
  // })
  .use(VueAMapLoca)
  .component('CountTo', CountTo)
  .mount('#app');

<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<el-form-item label="机器人">
					<el-select
						v-model="searchOptions.filter.deviceId"
						placeholder="请选择机器人"
						style="width: 100%"
						:fit-input-width="true"
						:teleported="false"
						placement="bottom-start"
						clearable
					>
						<el-option
							v-for="(key, index) in robotsInfo.keys()"
              :key="index"
							:label="robotsInfo.get(key)?.robotName"
							:value="key"
						/>
					</el-select>
				</el-form-item>
			</template>

			<template #searchBtns>
				<el-button @click="onSynchronous" :loading="syncLoading">
					<template #icon>
						<el-icon><ele-Refresh /></el-icon>
					</template>
					同步
				</el-button>
			</template>
		</ViewSearch>

		<Table v-bind="tableOptions" ref="tableRef" @pageChange="onPageChange">
			<template #treeProps="{ row }">
				<el-table :data="row.patrolLines" row-key="id" style="width: 100%">
					<el-table-column label="路线编号" align="center" prop="lineNo" :width="160" />
					<el-table-column label="路线名称" align="center" prop="lineName" flex="1" />
				</el-table>
			</template>
			<template #mapPicUrl="{ row }">
				<el-image
					style="width: 100px; height: 50px"
					:src="row.mapPicUrl"
					:preview-teleported="true"
					:preview-src-list="[row.mapPicUrl]"
					:initial-index="0"
					fit="contain"
					lazy
					:z-index="99999"
				>
				</el-image>
			</template>
			<template #deviceId="{ row }">
				{{ robotsInfo.get(row.deviceId)?.robotName }}
			</template>
		</Table>
	</div>
</template>

<script setup lang="ts" name="AiModels">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getRobotMaps } from '/@/api/home';
import { initPatrolLines } from '/@/api/patrol';
import { liveInfo } from '/@/stores/fullScreen';
import { storeToRefs } from 'pinia';
const liveStore = liveInfo();
const { robotsInfo } = storeToRefs(liveStore);

// 引入异步组件
const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));

const tableRef = ref<InstanceType<typeof Table>>();
const searchOptions = reactive({
	filter: {
		deviceId: null,
	},
});
const tableOptions: GlobalTableOptions<AiModelRow> = reactive({
	data: [],
	header: [
		{
			title: '地图名称',
			key: 'mapName',
			isCheck: true,
		},
		{
			title: '机器人名称',
			key: 'deviceId',
			isCheck: true,
		},
		{
			title: '地图图片',
			key: 'mapPicUrl',
			isCheck: true,
		},
	],
	config: {
		loading: true,
		isSelection: false,
		isSerialNo: true,
		isOperate: false,
		operateWidth: 150, // 操作列宽
		total: 0, // 总条数
		treeProps: { children: 'patrolLines', hasChildren: 'hasChildren' },
	},
	isPagination: false,
});

onMounted(() => {
	initTableData();
});

const initTableData = async () => {
	tableOptions.config.loading = true;
	try {
		const query = {
			robotIds: searchOptions.filter.deviceId ? [searchOptions.filter.deviceId] : undefined,
		};
		if (!searchOptions.filter.deviceId) {
			delete query.robotIds;
		}

		const { payload } = await getRobotMaps(query);

		tableOptions.config.loading = false;
		tableOptions.data = payload;
		// .map((item: any) => {
		// 	return {
		// 		...item,
		// 		hasChildren: item.patrolLines.length > 0,
		// 	};
		// });
	} catch (e) {
		tableOptions.config.loading = false;
		tableOptions.config.total = 0;
		tableOptions.data = [];
	}
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetFilter) {
		searchOptions.filter.deviceId = null;
	}
	if (resetPage) {
		tableRef.value?.pageReset();
	} else {
		initTableData();
	}
	tableRef.value?.clearSelection();
};

const onPageChange = async (page: { pageNum: number; pageSize: number }) => {
	if (tableOptions.pageParams) {
		tableOptions.pageParams.page = page.pageNum;
		tableOptions.pageParams.size = page.pageSize;
	}
	initTableData();
};
const syncLoading = ref(false);
const onSynchronous = () => {
	syncLoading.value = true;
	initPatrolLines()
		.then(() => {
			ElMessage.success('同步成功');
		})
		.catch(() => {
			ElMessage.error('同步失败');
		})
		.finally(() => {
			syncLoading.value = false;
		});
};

// const onBatchDelete = () => {
//   ElMessageBox({
//     title: '提示',
//     message: '此操作将永久删除，是否继续?',
//     type: 'warning',
//     showCancelButton: true,
//   }).then(async () => {
//     const ids = tableRef?.value?.selectRows.map((item) => item.id) as string[];
//     await batchDeleteAiModel(ids);
//     ElMessage.success('删除成功');
//     onRefresh();
//     onResetAiModels();
//   })
//     .catch(() => { })
// }

// const onDelRow = (rowId: string) => {
//   ElMessageBox({
//     title: '提示',
//     message: '此操作将永久删除，是否继续?',
//     type: 'warning',
//     showCancelButton: true,
//   }).then(async () => {
//     await deleteAiModel(rowId);
//     ElMessage.success('删除成功');
//     onRefresh();
//     onResetAiModels();
//   })
//     .catch(() => { })
// };
</script>
<style>
:deep(.image-table) {
	.el-table__cell {
		position: static !important;
	}
}
</style>

import { RouteRecordRaw } from 'vue-router';

// window.systemConfig = sd

export const homeRoute = {
  path: '/screen',
  name: 'SDDataScreen',
  redirect: '/screen/index',
  meta: {
    title: '大屏一览',
    isHide: false,
    isKeepAlive: true,
    isAffix: true,
    icon: 'iconfont icon-shouye',
  },
};

export const staticRoutes: Array<RouteRecordRaw> = [
  {
    path: '/screen',
    name: 'SDDataScreenLayout',
    redirect: '/screen/index',
    component: () => import('/@/viewsSD/dataScreen/index.vue'),
    meta: {
      title: '大屏一览',
      isKeepAlive: false,
    },
    children: [
      {
        path: 'index',
        name: 'DataScreenDevice',
        component: () => import('/@/screen/index.vue'),
        meta: {
          title: '大屏一览',
        },
      },
      {
        path: 'atlas',
        name: 'DataScreenAtlas',
        component: () => import('/@/screen/atlas/index.vue'),
        meta: {
          title: '感知历史',
          breadcrumb: {
            parents: [
              {
                path: '/screen/index',
                title: '大屏一览',
              },
              {
                match: (query: Record<string, any>) => query.pagePath === 'species' && query.fromRank === 'true',
                path: '/screen/rankDetail',
                title: '发现物种统计',
                keepQuery: ['pagePath', 'eventType'],
                parent: {
                  path: '/screen/index',
                  title: '大屏一览'
                },
              },
            ]
          }
        },
      },
      {
        path: 'rankDetail',
        name: 'DataScreenRankDetail',
        component: () => import('/@/screen/rankDetail/index.vue'),
        meta: {
          title: '发现物种统计',
          breadcrumb: {
            parents: [
              {
                path: '/screen/index',
                title: '大屏一览',
              },
            ]
          }
        },
      },
      // {
      //   path: 'bird',
      //   name: 'DataScreenBird',
      //   component: () => import('/@/viewsSD/dataScreen/bird/index.vue'),
      // },
      // {
      //   path: 'bird/:tag',
      //   name: 'DataScreenTag',
      //   component: () => import('/@/viewsSD/dataScreen/tag/index.vue'),
      // },
      // {
      //   path: 'atlas',
      //   name: 'DataScreenAtlas',
      //   component: () => import('/@/viewsSD/dataScreen/atlas/index.vue'),
      // },
    ],
  },
];
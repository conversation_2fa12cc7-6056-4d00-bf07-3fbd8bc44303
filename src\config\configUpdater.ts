import fs from 'fs';
import { resolve } from 'path';
import { projectConfigs } from './project.config';

export const updateConfig = (config: typeof projectConfigs[keyof typeof projectConfigs]) => {
  const configPath = resolve(process.cwd(), 'public/config.ts');
  const configContent = `// @ts-nocheck
/**
 * 全局配置文件
 * 配置项说明：
 * system: 系统类型，\`default\` 后台项目，\`sd\` 湿地大屏+后台项目，\`robot\` 机器人大屏+后台项目
 * company: 所属单位：nmg：内蒙古（锡盟）、other（其他）
 * theme: 主题配置
 * primary: 主题色，绿色版（#104E47）、蓝色版（#409eff）
 * menuBar: 菜单背景色，绿色版（#104E47）、蓝色版（#304156）
 * menuBarColor: 默认菜单字体颜色
 * menuBarActiveColor: 默认菜单高亮背景色
 * menuActiveColor: 默认菜单激活色，绿色版（#22D69F）、蓝色版（#409eff）
 * loadingBg: 全局加载动画背景色，绿色版（#104E47）、蓝色色版（#7171C6）
 * tableHeaderColor: 表头背景色，绿色版（rgba(16, 78, 71, 0.1)）、蓝色版（#f5f7fa）  
*/
window.GLOBAL_CONFIG = {
  system: '${config.system}', 
  company: '${config.company}', 
  primary: '${config.theme.primary}',
  menuBar: '${config.theme.menuBar}',
  menuBarColor: '${config.theme.menuBarColor}',
  menuBarActiveColor: '${config.theme.menuBarActiveColor}',
  menuActiveColor: '${config.theme.menuActiveColor}',
  loadingBg: '${config.theme.loadingBg}',
  tableHeaderColor: '${config.theme.tableHeaderColor}',
};`;
  fs.writeFileSync(configPath, configContent);
}; 
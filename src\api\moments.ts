import request from '/@/utils/request';

/**
 * 获取瞬间信息
 * @param params 
 * @returns 
 */
export function getMoments(params?: Object) {
  return request({
    url: `/moments`,
    method: 'get',
    params,
  });
}
/**
 * 新建瞬间
 * @param data 
 * @returns 
 */
export function creadteMoments(data?: Object) {
  return request({
    url: `/moments`,
    method: 'post',
    data,
  });
}
/**
 * 批量删除瞬间
 * @param data 
 * @returns 
 */
export function deleteBatchMoments(ids: string[]) {
  return request({
    url: `/moments/del`,
    method: 'delete',
    data: ids,
  });
}
/**
 * 批量审核瞬间
 * @param data 
 * @returns 
 */
export function auditBatchMoments(data: { id: string, audit: 1 }[]) {
  return request({
    url: `/moments/audit`,
    method: 'put',
    data,
  });
}
/**
 * 批量删除
 * @param id 
 * @returns 
 */
export function deleteByEventId(id: string) {
  return request({
    url: `/moments/event/${id}`,
    method: 'delete',
  });
}
/**
 * 删除某一个
 * @param id 
 * @returns 
 */
export function deleteMoments(id: string) {
  return request({
    url: `/moments/${id}`,
    method: 'delete',
  });
}
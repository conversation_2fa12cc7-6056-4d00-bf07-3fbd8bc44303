<template>
	<!-- 
		红外设备：sourceType=2，修改设备名称、编号、坐标类型、位置坐标（创建同样）
		监控设备：sourceType=3，修改设备名称、编号、厂商、坐标类型、位置坐标、视频地址（创建同样）
		萤石云设备：sourceType=1，修改坐标（不手动创建）
		巡检设备：sourceType=4，不能修改（也不手动创建）
	-->
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		width="580px"
		align-center
	>
		<el-form
			ref="dialogFormRef"
			:model="state.ruleForm"
			:rules="rules"
			label-width="100px"
			size="large"
		>
			<template v-if="state.ruleForm.sourceType !== 1">
				<el-form-item label="设备编号" prop="num">
					<el-input
						v-model="state.ruleForm.num"
						placeholder="请输入设备编号（例：1921687165）"
						clearable
					></el-input>
				</el-form-item>
				<el-form-item label="设备名称" prop="name">
					<el-input v-model="state.ruleForm.name" placeholder="请输入设备名称" clearable></el-input>
				</el-form-item>
			</template>
      
      <!-- 仅监控设备可管理生产厂商 -->
      <el-form-item v-if="state.ruleForm.sourceType === 3" label="生产厂商" prop="manufacturer">
        <el-radio-group v-model="state.ruleForm.manufacturer">
          <el-radio v-for="(item, key) in Manufacturer" :key="key" :label="Number(key)">
            {{ item }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="坐标类型" prop="coordinateType">
				<el-radio-group v-model="state.ruleForm.coordinateType">
					<el-radio v-for="(item, key) in Coordinate_Type" :key="key" :label="Number(key)">
						{{ item }}
					</el-radio>
				</el-radio-group>
			</el-form-item>

			<el-form-item label="经度" prop="longitude">
				<el-input
					v-model="state.ruleForm.longitude"
					placeholder="请输入经度（例：116.40）"
					clearable
				></el-input>
			</el-form-item>
			<el-form-item label="纬度" prop="latitude">
				<el-input
					v-model="state.ruleForm.latitude"
					placeholder="请输入纬度（例：39.92）"
					clearable
				></el-input>
			</el-form-item>

			<!-- 仅监控设备存在视频地址 -->
			<el-form-item v-if="state.ruleForm.sourceType === 3" label="视频地址" prop="previewUrl">
				<el-input
					v-model="state.ruleForm.previewUrl"
					type="textarea"
					placeholder="请输入视频地址，例：rtsp://xxxxxx"
					clearable
				></el-input>
			</el-form-item>
		</el-form>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="onCancel">取 消</el-button>
				<el-button type="primary" @click="onSubmit">{{ state.dialog.submitTxt }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, nextTick } from 'vue';
import { ElMessage, ElNotification } from 'element-plus';
import type { FormInstance, NotificationParams } from 'element-plus';
import { createDevice, updateDevice } from '/@/api/devices';
import { Coordinate_Type, Manufacturer } from '/@/utils/constants';

const emits = defineEmits<{
	(e: 'refresh', resetPage?: boolean, resetFilter?: boolean): void;
	(e: 'resetDevices'): void;
	(e: 'onCreateMonitor', newDevice: { id: string; sourceType: number }): void;
}>();
const props = defineProps<{
	// 1监控设备/萤石云设备 2红外设备 4巡护设备
	deviceType: number;
}>();

// 定义变量内容
const dialogFormRef = ref<FormInstance>();
const state = reactive<DeviceDialogState>({
	ruleForm: {
		name: '',
		num: '',
    manufacturer: 1,
    coordinateType: 1,
		longitude: null,
		latitude: null,
		previewUrl: '',
		sourceType: 0, // 1萤石云设备 2红外设备 3监控设备 4巡护设备
	},
	editDeviceIds: [], // 修改时的设备ids，如果当前行是树形顶级，取当前行channels中的所有id；否则取当前行的id
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
});
// 校验经纬度
const reg = /^-?\d+(\.\d+)?$/;
const validLongitude = (rule: any, value: string, callback: Function) => {
	if (value && !reg.test(value)) {
		callback(new Error('请输入有效的经度'));
		return;
	}
	if (value && !(Number(value) >= -180 && Number(value) <= 180)) {
		callback(new Error('经度范围在 -180 到 180 之间'));
		return;
	}
	callback();
};
const validLatitude = (rule: any, value: string, callback: Function) => {
	if (value && !reg.test(value)) {
		callback(new Error('请输入有效的纬度'));
		return;
	}
	if (value && !(Number(value) >= -90 && Number(value) <= 90)) {
		callback(new Error('纬度范围在 -90 到 90 之间'));
		return;
	}
	callback();
};
const rules = {
	name: { required: true, message: '设备名称不能为空', trigger: 'blur' },
	num: { required: true, message: '设备编号不能为空', trigger: 'blur' },
  manufacturer: { required: true, message: '生产厂商不能为空', trigger: 'blur' },
	previewUrl: { required: true, message: '视频地址不能为空', trigger: 'blur' },
	longitude: [
		// { required: true, message: '经度不能为空', trigger: 'blur' },
		{ validator: validLongitude, trigger: 'blur' },
	],
	latitude: [
		// { required: true, message: '纬度不能为空', trigger: 'blur' },
		{ validator: validLatitude, trigger: 'blur' },
	],
};

// 打开弹窗
const openDialog = (row?: DeviceRow) => {
	state.dialog.isShowDialog = true;
	nextTick(() => {
		dialogFormRef.value?.resetFields();
		if (row && row.name) {
			state.dialog.type = 'edit';
			state.dialog.title = '修改设备';
			state.dialog.submitTxt = '修 改';
      // 回显
      state.ruleForm.name = row.name;
      state.ruleForm.num = row.num;
      state.ruleForm.coordinateType = row.coordinateType;
      state.ruleForm.longitude = row.longitude;
      state.ruleForm.latitude = row.latitude;
      state.ruleForm.previewUrl = row.previewUrl;
      state.ruleForm.sourceType = row.sourceType;
      state.ruleForm.manufacturer = row.manufacturer;
			state.editDeviceIds = row.isParentNode ? row.channels.map((item: DeviceRow) => item.id) : [row.id];
		} else {
			state.dialog.type = 'add';
			state.dialog.title = '新增设备';
			state.dialog.submitTxt = '新 增';
      state.ruleForm.sourceType = props.deviceType === 1 ? 3 : props.deviceType;
      state.ruleForm.manufacturer = 1;
      state.ruleForm.previewUrl = '';
			state.editDeviceIds = [];
		}
	});
};
const closeDialog = () => {
	state.dialog.isShowDialog = false;
};
const onCancel = () => {
	closeDialog();
};
const onSubmit = () => {
	dialogFormRef.value?.validate(async (valid: boolean) => {
		if (!valid) return;
		if (state.dialog.type === 'add') {
			const { payload } = await createDevice(state.ruleForm);
			ElMessage.success('新增成功');
			emits('refresh', true, true);
			emits('resetDevices');
			closeDialog();
			// 手动新增设备后，提示是否立即添加监测任务。
			const notice = ElNotification(<NotificationParams>{
				title: '新设备已创建',
				dangerouslyUseHTMLString: true,
				message: '<div><strong>是否立即添加监测任务</strong><div class="yesBtn">去创建</div></div>',
				duration: 5000,
				type: 'success',
				customClass: 'custom-notification',
				onClick: function (event: { target: { className: string } }) {
					if (event.target.className !== 'yesBtn') return;
					emits('onCreateMonitor', {
						id: payload.id,
						sourceType: payload.sourceType,
					});
					notice.close();
				},
			});
			return;
		}
    const data = {
      ids: state.editDeviceIds,
      ...state.ruleForm,
      sourceType: null,
    }
		await updateDevice(data);
		ElMessage.success('修改成功');
		emits('refresh');
		emits('resetDevices');
		closeDialog();
	});
};

defineExpose({
	openDialog,
});
</script>

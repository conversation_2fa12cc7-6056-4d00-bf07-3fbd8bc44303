import { da } from "element-plus/es/locale";

type MapColor = 'gray' | 'blue' | 'darkgreen' | 'black'
type LayerName = 'areaPolyline' | 'device' | 'heatmap' | 'trackLine';

declare interface RecentMonitorEvent {
  alias: string;
  deviceName: string;
  discoveredTime: string;
  eventDetailId: string;
  eventFileId: string;
  eventId: string;
  // image: string;
  name: string;
  pictureUrl: string;
  oriPictureThumbnailUrl: string;
  oriPictureUrl: string;
  isNewData: boolean;
}

declare interface SpeciesEvent {
  date: string;
  speciesAlias: string;
  speciesName: string;
  speciesNum: number;
  speciesType: number;
  speciesFrequency: number;
  [key: string]: string;

  tagNumMap: number;
  tags: string;
  totalSpeciesFrequency: number;
}

declare type HeatMapData = {
  lng: number;
  lat: number;
  count: number;
}[];
declare interface HeatMapDataSet {
  data: HeatMapData;
  max: number;
}

declare interface Material {
  date?: string;
  pictureCount: number;
  totalCount: number;
  videoCount: number;
  videoDuration: number;
}

interface Track {
  LatLonList?: string;
  speciesName: string;
  speciesFrequency: number;
}

interface TrackResult {
  [key: string]: {
    LatLonList: {
      originPos: [number, number];
      offsetPos: [number, number];
    }[];
    speciesFrequency: number;
  }[]
}

interface TrackLine {
  LatLonList: [number, number][];
  speciesFrequency: number[];
  lineColor: string;
}

interface TrackFinalResult {
  [key: string]: TrackLine[];
}

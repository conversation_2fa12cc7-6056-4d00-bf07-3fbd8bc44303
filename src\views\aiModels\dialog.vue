<template>
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		width="480px"
		align-center
	>
		<el-form
			ref="dialogFormRef"
			:model="state.ruleForm"
			label-width="80px"
			size="large"
			label-position="right"
		>
			<el-form-item
				label="识别类型"
				prop="recType"
				:rules="[{ required: true, message: '识别类型不能为空', trigger: 'blur' }]"
			>
				<el-radio-group v-model="state.ruleForm.recType">
					<el-radio v-for="item in props.modelTypes" :key="item.id" :label="item.eventType">
						{{ item.name }}
					</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item
				label="模型名称"
				prop="name"
				:rules="[{ required: true, message: '模型名称不能为空', trigger: 'blur' }]"
			>
				<el-input v-model="state.ruleForm.name" placeholder="请输入" clearable></el-input>
			</el-form-item>

			<el-form-item
				label="模型简介"
				prop="describe"
				:rules="[{ required: true, message: '模型简介不能为空', trigger: 'blur' }]"
			>
				<el-input v-model="state.ruleForm.describe" placeholder="请输入" clearable></el-input>
			</el-form-item>

			<el-form-item
				label="版本"
				prop="versionName"
				:rules="[
					{ required: true, message: '模型版本不能为空', trigger: 'blur' },
					{ validator: validVersionName, trigger: 'blur' },
				]"
			>
				<el-input
					v-model="state.ruleForm.versionName"
					placeholder="请输入（例：1.0）"
					clearable
				></el-input>
			</el-form-item>

			<el-form-item
				label="URL"
				prop="apiUrl"
				:rules="[
					{ required: true, message: '模型URL不能为空', trigger: 'blur' },
					{ validator: validApiUrl, trigger: 'blur' },
				]"
			>
				<el-input
					v-model="state.ruleForm.apiUrl"
					placeholder="请输入（例：http://127.0.0.1:5003/recog/beasts）"
					clearable
				></el-input>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="onCancel">取 消</el-button>
				<el-button type="primary" @click="onSubmit">{{ state.dialog.submitTxt }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import type { FormInstance } from 'element-plus';
import { createAiModel, updateAiModel } from '/@/api/aiModels';
import { ElMessage } from 'element-plus';

const emits = defineEmits(['refresh', 'resetAiModels']);

const dialogFormRef = ref<FormInstance>();
const state: AiModelDialogState = reactive({
	ruleForm: {
		name: '',
		describe: '',
		versionName: '',
		apiUrl: '',
		recType: '',
		taskType: '',
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
});

const props = defineProps<{
	modelTypes: Array<{ id: number; eventType: number; name: string }>;
}>();

const openDialog = (row?: AiModelRow) => {
	state.dialog.isShowDialog = true;
	nextTick(() => {
		dialogFormRef.value?.resetFields();
		if (row && row.id) {
			state.dialog.type = 'edit';
			state.dialog.title = '修改模型';
			state.dialog.submitTxt = '修 改';
			state.ruleForm = {
				id: row.id,
				name: row.name,
				describe: row.describe,
				versionName: row.versionName,
				apiUrl: row.apiUrl,
				recType: row.recType,
				taskType: row.taskType,
			};
		} else {
			state.dialog.type = 'add';
			state.dialog.title = '新增模型';
			state.dialog.submitTxt = '新 增';
			state.ruleForm = {
				name: '',
				describe: '',
				versionName: '',
				apiUrl: '',
				recType: '',
				taskType: '',
			};
		}
	});
};

const validVersionName = (rule: any, value: string, callback: Function) => {
	const reg = /^\d+(\.\d+)?$/;
	if (!reg.test(value)) {
		callback(new Error('请输入有效的版本，如1.0'));
		return;
	}
	callback();
};
const validApiUrl = (rule: any, value: string, callback: Function) => {
	const reg = /^http(s)?:\/\/(\d{1,3})(\.?\d{1,3}){3}(:\d+)?(\/[a-zA-Z\-]+)+$/;
	if (!reg.test(value)) {
		callback(new Error('请输入有效的URL, 如http://127.0.0.1:5003/recog/beasts'));
		return;
	}
	callback();
};

const onCancel = () => {
	state.dialog.isShowDialog = false;
};

const onSubmit = () => {
	dialogFormRef.value?.validate((valid: boolean) => {
		if (!valid) return;

		if (state.dialog.type === 'add') {
			createAiModel(state.ruleForm).then(() => {
				ElMessage.success('新增成功');
				onCancel();
				emits('refresh', true, true);
				emits('resetAiModels');
			});
			return;
		}
		updateAiModel(state.ruleForm).then(() => {
			ElMessage.success('修改成功');
			onCancel();
			emits('refresh');
			emits('resetAiModels');
		});
	});
};

defineExpose({
	openDialog,
});
</script>

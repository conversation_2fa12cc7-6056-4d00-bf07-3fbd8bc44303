<template>
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		width="480px"
		align-center
	>
		<el-form
			ref="dialogFormRef"
			:model="state.ruleForm"
			label-width="80px"
			size="large"
			label-position="right"
		>
			<el-form-item
				v-if="!state.userId"
				label="旧密码"
				prop="oldPass"
				:rules="[{ required: true, message: '旧密码不能为空', trigger: 'blur' }]"
			>
				<el-input
					v-model="state.ruleForm.oldPass"
					type="password"
					show-password
					placeholder="请输入"
					clearable
				></el-input>
			</el-form-item>

			<el-form-item
				label="新密码"
				prop="newPass"
				:rules="[
					{ required: true, message: '新密码不能为空', trigger: 'blur' },
					{ validator: validateNewPass, trigger: 'blur' },
				]"
			>
				<el-input
					v-model="state.ruleForm.newPass"
					type="password"
					show-password
					placeholder="请输入"
					clearable
				></el-input>
			</el-form-item>

			<el-form-item
				label="确认新密码"
				prop="checkPass"
				:rules="[
					{ required: true, message: '确认密码不能为空', trigger: 'blur' },
					{ validator: validateCheckPass, trigger: 'blur' },
				]"
			>
				<el-input
					v-model="state.ruleForm.checkPass"
					type="password"
					show-password
					placeholder="请输入"
					clearable
				></el-input>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="onCancel">取 消</el-button>
				<el-button type="primary" @click="onSubmit">{{ state.dialog.submitTxt }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import type { FormInstance } from 'element-plus';
import { ElMessage } from 'element-plus';
import { changePassword, resetPassword } from '/@/api/users';
import { Session } from '/@/utils/storage';
import { validatePassword as verifyPassword } from '/@/utils/toolsValidate';

const dialogFormRef = ref<FormInstance>();
const state: ResetPwdDialogState = reactive({
	ruleForm: {
		oldPass: '',
		newPass: '',
		checkPass: '',
	},
	userId: '',
	dialog: {
		isShowDialog: false,
		type: '',
		title: '重置密码',
		submitTxt: '重 置',
	},
});

const openDialog = (userId?: string | undefined) => {
	state.dialog.isShowDialog = true;
	nextTick(() => {
		dialogFormRef.value?.resetFields();
		state.userId = userId;
	});
};
const validateNewPass = (rule: any, value: any, callback: any) => {
	if (!value) {
		callback(new Error('密码不能为空'));
	} else if (!verifyPassword(value)) {
		callback(new Error('密码长度在6~32之间，且必须包含字母和数字'));
	} else {
		callback();
	}
};
const validateCheckPass = (rule: any, value: any, callback: any) => {
	if (!value) {
		callback(new Error('确认密码不能为空'));
	} else if (!verifyPassword(value)) {
		callback(new Error('密码长度在6~32之间，且必须包含字母和数字'));
	} else if (value !== state.ruleForm.newPass) {
		callback(new Error('两次密码不一致，请重新输入'));
	} else {
		callback();
	}
};
const onCancel = () => {
	state.dialog.isShowDialog = false;
};
const onSubmit = () => {
	dialogFormRef.value?.validate((valid: boolean) => {
		if (!valid) return;
		if (state.userId) {
			const data = {
				userId: state.userId,
				newPass: state.ruleForm.newPass,
			};
			changePassword(data).then(() => {
				ElMessage.success('用户密码重置成功');
				onCancel();
			});
		} else {
			const data = {
				oldPass: state.ruleForm.oldPass,
				newPass: state.ruleForm.newPass,
			};
			resetPassword(data).then(() => {
				ElMessage.success('密码重置成功，请重新登录');
				onCancel();
				Session.clear();
				window.location.reload();
			});
		}
	});
};
defineExpose({
	openDialog,
});
</script>

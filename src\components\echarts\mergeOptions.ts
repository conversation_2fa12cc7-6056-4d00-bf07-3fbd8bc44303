// 图表配置
import type { ComposeOption, Color } from 'echarts/core';
import type { BarSeriesOption, LineSeriesOption, PieSeriesOption, PictorialBarSeriesOption } from 'echarts/charts';
import type { TitleComponentOption, TooltipComponentOption, GridComponentOption, DatasetComponentOption } from 'echarts/components';
import _ from 'lodash';

export type ECOption = ComposeOption<
  | PieSeriesOption 
  | BarSeriesOption
  | LineSeriesOption
  | PictorialBarSeriesOption
  | TitleComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | DatasetComponentOption
>;

// # 图表公共配置项
export const chartColors: Color[] = [
  '#22D69F', // 绿
  '#4AC6FB', // 蓝
  '#F8CE6B', // 黄
  '#FD8136', // 橙
  '#A23BFF', // 紫
  '#EB49F9', // 玫紫
  '#FF1515', // 大红
];
export const chartLinearColors: Color[] = [
  // 黄 - 粉
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [{
      offset: 0, color: 'rgba(255, 224, 93, 0.8)' // 0% 处的颜色
    }, {
      offset: 1, color: 'rgba(253, 117, 218, 0.8)' // 100% 处的颜色
    }],
    global: false,
  },
  // 绿 - 蓝
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 1,
    y2: 0,
    colorStops: [{
      offset: 0, color: 'rgba(136, 232, 130, 0.8)' // 0% 处的颜色
    }, {
      offset: 1, color: 'rgba(22, 226, 231, 0.8)' // 100% 处的颜色
    }],
    global: false,
  },
  // 区域渐变
  // 黄 - 粉
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [{
      offset: 0, color: 'rgba(210, 107, 188, 0.2)' // 0% 处的颜色
    }, {
      offset: 1, color: 'rgba(198, 181, 81, 0)' // 100% 处的颜色
    }],
    global: false,
  },
  // 绿 - 蓝
  {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [{
      offset: 0, color: 'rgba(136, 232, 130, 0.2)' // 0% 处的颜色
    }, {
      offset: 1, color: 'rgba(22, 226, 231, 0)' // 100% 处的颜色
    }],
    global: false,
  },
];
// 饼图
const pieCommonOptions: ECOption  = {
  legend: {
    show: false,
    orient: 'vertical',
    top: 'middle',
    right: 0,
    itemGap: 10,
    itemWidth: 0,
    itemHeight: 0,
    textStyle: {
      color: '#fff',
      rich: {
        icon0: {
          width: 11,
          height: 11,
          backgroundColor: chartColors[0],
        },
        icon1: {
          width: 11,
          height: 11,
          backgroundColor: chartColors[1],
        },
        icon2: {
          width: 11,
          height: 11,
          backgroundColor: chartColors[2],
        },
        icon3: {
          width: 11,
          height: 11,
          backgroundColor: chartColors[3],
        },
        // 自定义图例名称
        cname: {
          width: 60,
          padding: [0, 0, 0, 4],
          overflow: 'truncate',
          verticalAlign: 'bottom',
        },
        cnum: {
          width: 29,
          verticalAlign: 'middle',
          align: 'center',
        },
        // 自定义线条
        line: {
          width: 100,
          height: 1,
          backgroundColor: 'rgba(255, 255, 255, .5)',
        },
        // 自定义方形块
        square: {
          width: 8,
          height: 8,
          backgroundColor: 'rgba(255, 255, 255, .5)',
        },
      },
    },
  },
  tooltip: {
    trigger: 'item',
    axisPointer: {
      type: 'shadow',
    },
    textStyle: {
      color: '#fff',
    },
    backgroundColor: 'rgba(50, 50, 50, 0.7)',
    borderColor: 'rgba(104, 207, 173, 1)',
    formatter: (params: any) => {
      return `${params.name}：${params.value['value']}`;
    },
  },
  color: chartColors,
  series: [
    {
      type: 'pie',
      name: '',
      showEmptyCircle: true,
      // center: ['32%', '50%'],
      center: ['50%', '50%'],
      radius: ['36%', '50%'],
      label: {
        position: 'outside',
        formatter: (params) => {
          return `{${params.dataIndex}|${params.percent}%}\n${params.name}`
        },
        rich: {
          0: { color: chartColors[0] as string },
          1: { color: chartColors[1] as string },
          2: { color: chartColors[2] as string },
          3: { color: chartColors[3] as string },
          4: { color: chartColors[4] as string },
          5: { color: chartColors[5] as string },
        },
        color: '#fff',
        fontSize: 12,
        lineHeight: 16,
      },
      labelLine: {
        // length: 4,
      },
    },
    {
      type: 'pie',
      name: '内部虚线圆',
      // center: ['32%', '50%'],
      center: ['50%', '50%'],
      radius: ['23%', '30%'],
      silent: true,
      label: { show: false },
      labelLine: { show: false },
      itemStyle: {
        color: 'rgba(255, 255, 255, 0.6)',
        borderColor: '#0C1E29',
        borderWidth: 6,
      },
      data: [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10],
    },
    {
      type: 'pie',
      name: '内部实线圆',
      // center: ['32%', '50%'],
      center: ['50%', '50%'],
      radius: ['6%', '6.5%'],
      silent: true,
      label: { show: false },
      labelLine: { show: false },
      itemStyle: {
        color: 'rgba(104, 207, 173, 0.4)',
      },
      data: [10],
    },
  ],
};
// 折线图
const lineCommonOptions: ECOption  = {
  grid: {
    top: '16%',
    left: 0,
    right: '2%',
    bottom: '8%',
    containLabel: true,
  },
  legend: {
    right: 0,
    itemGap: 20,
    itemWidth: 18,
    itemHeight: 3,
    icon: 'roundRect',
    borderRadius: 4,
    textStyle: {
      color: '#fff',
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'line',
      axis: 'auto',
      lineStyle: {
        color: '#44605F',
      },
    },
    textStyle: {
      color: '#fff',
    },
    backgroundColor: 'rgba(50, 50, 50, 0.7)',
    borderColor: 'rgba(104, 207, 173, 1)',
  },
  xAxis: {
    type: 'category',
    axisLine: {
      show: true,
      lineStyle: { 
        color: '#44605F',
      },
    },
    axisTick: { show: false },
    axisLabel: {
      show: true,
      color: 'rgba(255, 255, 255, 0.5)',
    },
    boundaryGap: false,
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: true,
      lineStyle: { 
        color: '#44605F',
      },
    },
    axisTick: { show: false },
    axisLabel: {
      show: true,
      color: 'rgba(255, 255, 255, 0.5)',
    },
    splitLine: { show: false },
  },
};
// 柱状图
const barCommonOptions: ECOption  = {
  grid: {
    left: 0,
    right: 0,
    bottom: '8%',
    containLabel: true,
  },
  legend: {
    right: 0,
    itemGap: 20,
    itemWidth: 18,
    itemHeight: 3,
    icon: 'roundRect',
    borderRadius: 4,
    textStyle: {
      color: '#fff',
    }
  },
  tooltip: {
    trigger: 'item',
    axisPointer: {
      type: 'shadow',
    },
    textStyle: {
      color: '#fff',
    },
    backgroundColor: 'rgba(50, 50, 50, 0.7)',
    borderColor: 'rgba(104, 207, 173, 1)',
  },
  xAxis: {
    type: 'category',
    axisLine: {
      show: true,
      lineStyle: { 
        color: '#44605F',
      },
    },
    axisTick: { show: false },
    axisLabel: {
      show: true,
      color: 'rgba(255, 255, 255, 0.5)',
      formatter: (value: string) => {
        return value.slice(6);
      },
    },
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: true,
      lineStyle: { 
        color: '#44605F',
      },
    },
    axisTick: { show: false },
    axisLabel: {
      show: true,
      color: 'rgba(255, 255, 255, 0.5)',
    },
    splitLine: { show: false },
  },
  color: chartLinearColors,
};

// 水波图
// const options = {
//   series: [{
//     name: props.birdType?.label + '鸟类占比',
//     type: 'liquidFill',
//     radius: 100,
//     data: [0.6],
//     label: {
//       color: '#fff',
//       fontSize: 18,
//     },
//     itemStyle: {
//       // color: 'red',
//     },
//     backgroundStyle: {
//       color: 'transparent',
//       borderWidth: 1,
//       borderColor: 'rgba(73, 147, 122, 0.5)',
//     },
//     outline: {
//       show: false,
//     },
//     color: [{
//       type: 'linear',
//       x: 0,
//       y: 1,
//       x2: 0,
//       y2: 0,
//       colorStops: [
//         { offset: 1, color: ['#6a7feb'] },
//         { offset: 0, color: ['#27e5f1'] },
//       ],
//       global: false,
//     }],
//   }]
// };
// nextTick(() => {
//   stat.value.forEach((item, $index) => {
//     ((percentChartRef.value) as Chart[])[$index].resetOption(options);
//   });
// });


// # 合并图表 公共配置 和 自定义配置
export function mergePieOptions(options: ECOption) {
  // # options 示例
  // const data = [
  //   { name: '东方', value: 23 },
  //   { name: '中华', value: 453 },
  //   { name: '黑的', value: 43 },
  //   { name: '其他', value: 65 },
  // ];
  // options = {
  //   legend: {
  //     formatter: (name: string) => {
  //       const index = data.findIndex((item) => item.name === name);
  //       return `{icon${index}|}{cname|${name}}{cnum|${data[index]?.value}}\n{line|}{square|}`;
  //     },
  //   },
  //   dataset: {
  //     source: data,
  //   },
  // };
  return _.merge({}, pieCommonOptions, options)
}

export function mergeLineOptions(options: ECOption) {
  // # options 示例
  // options = {
  //   dataset: {
  //     source: [],
  //   },
  //   color: chartLinearColors[0],
  //   series: [
  //     {
  //       type: 'line',
  //       name: '发现数量',
  //       symbol: 'circle',
  //       symbolSize: 6,
  //       showSymbol: false,
  //       areaStyle: {
  //         color: chartLinearColors[2],
  //       },
  //     },
  //   ],
  // }
  return _.merge({}, lineCommonOptions, options);
}

export function mergeBarOptions(options: ECOption) {
  // # options 示例
  // options = {
  //   dataset: {
  //     source: [
  //       ['2021-01-23', 23, 54],
  //       ['2021-01-24', 65, 43],
  //       ['2021-01-25', 76, 65],
  //       ['2021-01-26', 43, 32],
  //     ],
  //   },
  //   series: [
  //     {
  //       type: 'pictorialBar',
  //       name: '发现数量',
  //       symbol: 'roundRect',
  //       symbolSize: [18, 7],
  //       symbolRepeat: true,
  //       symbolMargin: 2,
  //       barGap: '10%',
  //     },
  //     {
  //       type: 'pictorialBar',
  //       name: '发现频次',
  //       symbol: 'roundRect',
  //       symbolSize: [18, 7],
  //       symbolRepeat: true,
  //       symbolMargin: 2,
  //       barGap: '10%',
  //     },
  //   ],
  // }
  return _.merge({}, barCommonOptions, options)
}

// 日期轴`label`格式转换
export function transDate(timeType: number | null, date: string) {
  if (timeType && Number(timeType) < 3) {
    return date.slice(5, 10); // 按日统计
  }
  return date.slice(0, 7); // 按月统计
}

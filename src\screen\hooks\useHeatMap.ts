import { ref, reactive } from 'vue';
import { getDevSpeciesFrequency } from '/@/api/sd/dataScreen/bird';
import { getSpeciesFrequency } from '/@/api/screen';
import { transCoordinate } from '../utils/map';
import { createQuery } from '../utils/query';

// 定义设备类型常量
type FilterType = 1 | 3 | 5 | 6 | 7 | 8;

/**
 * 
 * @param params {
 *  filterType: FilterType, 设备类型 1.红外素材3.监控巡航5.巡检素材6.虚拟7.防火8.执法记录仪
 *  timeType: TimeType, 
 *  animalNameList?: string[],
 *  animalTag?: number,
 *  eventType?: number, 模型类型、monitorEvent的事件类型 0:野生动物 1:鸟类 2:植物 5:烟火 20:高温 21:水生物种
 * }
 * @returns 
 */
export function useHeatMap() {
  const state = reactive({
    heatMapVisible: false,
    heatMapData: { type: 'FeatureCollection', features: [] },
    heatMapMax: 0,
  });

  const heatmapLayerStyle = ref({
    radius: 40,
    unit: 'px',
    height: 0,
    gradient: {
      '0.2': 'blue',
      '0.4': 'rgb(117,211,248)',
      '0.6': 'rgb(0, 255, 0)',
      '0.8': '#ffea00',
      '1.0': 'red',
    },
    value(index: number, feature: any) {
      return feature.properties.count;
    },
    heightBezier: [0.5, 0, 1, 0.5],
  });

  const getHeatMapData = async (params: {
    filterType?: FilterType,
    timeType?: TimeType,
    eventType?: number | string,
  }) => {
    try {
      const query = createQuery({
        filterType: params.filterType,
        timeType: params.timeType as TimeType,
        eventType: params.eventType,
      });
      const api = params.filterType === 1 ? getSpeciesFrequency : getDevSpeciesFrequency
      const { payload } = await api(query as any);
      const data: any = [];

      let max = 0;

      if (params.filterType === 1) {
        // 红外
        payload.forEach((item: any) => {
          const { longitude, latitude, coordinateType, speciesFrequency } = item.devicePoint;
          if (longitude && latitude) {
            data.push({
              type: 'Feature',
              properties: {
                count: speciesFrequency,
              },
              geometry: {
                type: 'Point',
                coordinates: transCoordinate([longitude, latitude], coordinateType, 2),
              },
            });
            if (speciesFrequency > max) max = speciesFrequency;
          }
        });
      } else {
        // 监控、巡检、虚拟、防火、执法记录仪
        payload.forEach((item: any) => {
          const { longitude, latitude, coordinateType, speciesFrequency } = item;
          if (longitude && latitude && speciesFrequency > 0) {
            data.push({
              type: 'Feature',
              properties: {
                count: speciesFrequency,
              },
              geometry: {
                type: 'Point',
                coordinates: transCoordinate([longitude, latitude], coordinateType, 2),
              },
            });
            if (speciesFrequency > max) max = speciesFrequency;
          }
        });
      }

      state.heatMapData.features = data;
      state.heatMapMax = max;
      state.heatMapVisible = true;
    } catch (error) {
      console.error('获取热力图数据失败:', error);
    }
  };

  return {
    state,
    heatmapLayerStyle,
    getHeatMapData,
  };
} 
<template>
  <div class="layout-pd">
    <div class="mb15" style="text-align: right;">
      <el-tooltip effect="dark"	content="刷新" placement="top">
        <el-button circle @click="onRefresh">
          <template #icon>
            <el-icon><ele-Refresh /></el-icon>
          </template>
        </el-button>
      </el-tooltip>
    </div>
    <Table v-bind="tableOptions" ref="tableRef">
      <template #prems="{row}">
        <template v-if="row.permissions && row.permissions.length > 0">
          <div v-for="p in row.permissions" :key="p.id">"{{ p.title }}"</div>
        </template>
        <template v-else>-</template>
      </template>
      <template #operate="{row}">
        <el-button size="small" text type="primary" @click="onAssign(row)">
          <el-icon><ele-EditPen /></el-icon>
          分配权限
        </el-button>
      </template>
    </Table>
    <AssignDialog ref="assignDialogRef" @refresh="onRefresh"></AssignDialog>
  </div>
</template>

<script setup lang="ts" name="Auth">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { getRoles } from '/@/api/auth';

// 引入异步组件
const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));
const AssignDialog = defineAsyncComponent(() => import('./assignDialog.vue'));

const tableRef = ref<InstanceType<typeof Table>>();
const tableOptions: GlobalTableOptions<AuthRow> = reactive({
  data: [],
  header: [
    {
      title: '角色名称',
      key: 'name',
      isCheck: true,
    },
    {
      title: '已分配权限',
      key: 'prems',
      isCheck: true,
    },
  ],
  config: {
    loading: true,
    isSelection: false,
    isSerialNo: true,
    isOperate: true, // 是否显示操作列
    operateWidth: 150, // 操作列宽
    total: 0, // 总条数
  },
  isPagination: false,
  pageParams: {
    page: 1,
    size: 10,
  },
});

onMounted(() => {
  initTableData();
});

const initTableData = async () => {
  tableOptions.config.loading = true;
  try {
    const { payload } = await getRoles();
    tableOptions.config.loading = false;
    tableOptions.data = payload;
    tableOptions.config.total = payload.length;
  } catch (e) {
    tableOptions.config.loading = false;
    tableOptions.config.total = 0;
    tableOptions.data = [];
  }
};
const onRefresh = () => {
  initTableData();
};
const assignDialogRef = ref<InstanceType<typeof AssignDialog>>();
const onAssign = (row: AuthRow) => {
  assignDialogRef.value?.openDialog(row);
};
</script>

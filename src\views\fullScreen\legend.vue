<template>
	<div class="map-legend-small" v-show="show" @click.stop="handleOpenLegend">
		<img :src="tuli1" alt="图例" title="点击查看图例" draggable="false" />
		<span>图例</span>
	</div>

	<div class="legend-filter" v-show="!show" @click.stop>
		<div class="close" @click.stop="closeLegend">关闭</div>
		<div style="display: flex; height: 25px">
			<span class="jc-sb">
				<span style="padding-left: 22px">路径</span>
				<img
					src="/src/assets/fullScreen/mapIcon/lujing.png"
					width="30"
					height="4"
					alt=""
					style="margin-right: 5px"
				/>
			</span>
			<span class="jc-sb">
				<span style="padding-left: 22px">关键点位</span>
				<img
					src="/src/assets/fullScreen/mapIcon/guanjian.png"
					width="15"
					height="17"
					alt=""
					style="margin-right: 5px"
				/>
			</span>
		</div>
		<el-checkbox v-model="checkAll" @change="handleCheckAllChange" style="margin-right: 34px"
			>全选</el-checkbox
		>
		<el-checkbox-group v-model="checkList" @change="handleCheckedCitiesChange" tag="span">
			<template v-for="event in eventTypeOptions" :key="event.value">
				<el-checkbox :label="event.value" :value="event.value">
					<template #default>
						{{ event.label }}
						<img :src="event.icon" width="17" height="25" alt="" style="margin-right: 5px" />
					</template>
				</el-checkbox>
			</template>
		</el-checkbox-group>

		<div class="footer">
			<el-button type="primary" @click.stop="closeLegend">取消</el-button>
			<el-button type="primary" @click.stop="handleConfirm">确定</el-button>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import tuli1 from '/@/assets/fullScreen/tuli1.png';
import { storeToRefs } from 'pinia';
import { liveInfo } from '/@/stores/fullScreen';
const emits = defineEmits(['currentIndex', 'updateStat', 'location', 'filterMarker']);
const liveStore = liveInfo();

const { eventTypeOptions, legendFilterList } = storeToRefs(liveStore);

const show = ref(true);
const checkAll = ref(false);
const checkList = ref<number[]>([]);

const closeLegend = () => {
	show.value = true;
};

const checks = computed(() => {
	return eventTypeOptions.value.map((item) => item.value);
});

const handleOpenLegend = () => {
	show.value = false;
	checkList.value = legendFilterList.value;
	const checkedCount = checkList.value.length;
	checkAll.value = checkedCount === checks.value.length;
};
const handleCheckAllChange = (val: boolean) => {
	checkList.value = val ? checks.value : [];
};

const handleCheckedCitiesChange = (value: number[]) => {
	const checkedCount = value.length;
	checkAll.value = checkedCount === checks.value.length;
};
const handleConfirm = () => {
	show.value = true;
	legendFilterList.value = checkList.value;
};
defineExpose({
	closeLegend,
});
</script>

<style lang="scss" scoped>
$top: 6.667vh;
$right: 395px;
$z-index: 9;
.map-legend {
	position: absolute;
	top: $top;
	right: $right;
	width: 307px;
	height: 358px;
	z-index: $z-index;
	object-fit: contain;
	cursor: pointer;
}

.legend-filter {
	position: absolute;
	top: $top;
	right: $right;
	width: 315px;
	height: 418px;
	z-index: $z-index;
	padding: 55px 10px 0;

	background-image: url('/src/assets/fullScreen/map-legend.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;

	.jc-sb {
		// display: inline-block;
		width: calc(50% - 18px);
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #fff;
		font-size: 12px;
		margin-right: 34px;
		&:nth-child(2n) {
			margin-right: 0;
		}
	}
	&:deep(.el-checkbox) {
		width: calc(50% - 17px);
		margin-right: 0px;
		margin-bottom: 3.5px;
		.el-checkbox__label {
			width: calc(100% - 14px);
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		&:nth-child(2n) {
			margin-right: 34px;
		}
	}

	&:deep(.el-button) {
		background-color: transparent;
	}

	.close {
		position: absolute;
		top: 20px;
		right: 20px;
		cursor: pointer;
		color: #fff;
	}
	.footer {
		margin-top: 10px;
		display: flex;
		justify-content: end;
		&:deep(.el-button) {
			position: relative;
			background-color: transparent;
			&:last-child {
				background-image: linear-gradient(
					to bottom,
					rgba(24, 96, 71, 0.65),
					rgba(65, 169, 127, 0.65)
				);
			}
		}
	}
}

.map-legend-small {
	position: absolute;
	top: $top;
	right: $right;
	width: 40px;
	z-index: $z-index;
	margin-right: 16px;
	cursor: pointer;
	display: flex;
	flex-direction: column;
	justify-content: center;
	img {
		object-fit: contain;
	}
	span {
		color: rgba(255, 255, 255, 1);
		font-size: 14px;
		line-height: 28px;
		text-align: center;
	}
}

:deep(.el-checkbox) {
	color: #fff;
}
:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
	color: rgb(34, 214, 159);
}
:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
	background-color: transparent;
	border-color: rgb(34, 214, 159);
}

:deep(.el-checkbox__inner) {
	background-color: transparent;
	border-color: rgb(34, 214, 159);
}
:deep(.el-checkbox__label) {
	font-size: 12px;
}
</style>

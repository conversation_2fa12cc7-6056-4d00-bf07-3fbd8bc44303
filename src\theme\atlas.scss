:deep(.el-row.atlas) {
  clear: both;
  .el-card.atlas-item {
    margin-bottom: 10px;
  }
  .el-card.atlas-item .el-card__body {
    padding: 0;
    position: relative;
    .el-checkbox {
      position: absolute;
      top: 0;
      left: 8px;
    }
    .thumb-rec {
      position: relative;
    }
    .thumb-rec .recResult-content {
      width: 100%;
      position: absolute;
      left: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.6);
      color: var(--el-color-white);
      padding: 5px;
      .s-link {
        cursor: pointer;
        transition: all 0.2s ease;
        &:hover {
          color: var(--next-bg-menuActiveColor);
        }
      }
    }
    .thumbnail,
    .thumb-rec .thumbnail {
      width: 100%;
      padding-top: 56%;
      /* 清除img底部空白 */
      display: block;
      // vertical-align: middle;
      & > .el-image__error,
      & > img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        transition: all 0.3s ease;
        &:hover {
          transform: scale(1.05);
        }
      }
      & > img {
        cursor: pointer;
      }
    }
    /* 原始图片集 图片 */
    .thumbnail-container {
      display: flex;
      height: 200px;
      .main-thumb {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .thumb-recs {
        width: 36%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 5px;
        .thumb-rec {
          height: calc(100% / 2);
          .recResult-content {
            font-size: 12px;
            padding: 2px;
          }
        }
        .thumb-rec ~ .thumb-rec {
          margin-top: 5px;
        }
      }
      .thumbnail {
        padding-top: 0;
        height: 100%;
      }
    }
    .other-content {
      padding: 10px;
      position: relative;
      & > div:first-child {
        width: 80%;
      }
      & > div {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .operate-btns {
        position: absolute;
        right: 5px;
        top: 8px;
        .el-button.is-text {
          height: 22px;
          padding: 2px;
          margin-left: 4px !important;
        }
        .el-button.is-text .el-icon {
          font-size: 18px !important;
        }
      }
      .rb-icon {
        position: absolute;
        right: 10px;
        bottom: 10px;
        width: 16px;
        height: 16px;
        color: var(--el-color-warning);
      }
    }

  }
}
<template>
	<div class="data-screen">
		<div class="data-screen-header">
			<Header :activeMenu="activeMenu" @activeMenuChange="onActiveMenuChange"></Header>
		</div>
		<div class="data-screen-main">
			<!-- 带缓存功能的路由视图 -->
			<router-view v-slot="{ Component, route }">
				<transition mode="out-in">
					<keep-alive :include="cachedComponents">
						<component :is="Component" :key="route.path" />
					</keep-alive>
				</transition>
			</router-view>
			<Notice />
		</div>
	</div>
</template>

<script setup lang="ts" name="SDDataScreen">
import { ref, onMounted } from 'vue';
import { NextLoading } from '/@/utils/loading';
import 'animate.css';
// 引入组件
import Header from './component/header/index.vue';
import Notice from './component/notice/index.vue';

// 切换，并传递给子组件
const activeMenu = ref('device');
const onActiveMenuChange = (key: string) => {
	activeMenu.value = key;
};
// provide('activeMenu', readonly(activeMenu));

// 固定缓存指定页面
const cachedComponents = ref<string[]>(['DataScreenDevice']);

onMounted(() => {
	NextLoading.done();
});
</script>

<style lang="scss">
@import '/@/viewsSD/styles/dataScreen.scss';
</style>

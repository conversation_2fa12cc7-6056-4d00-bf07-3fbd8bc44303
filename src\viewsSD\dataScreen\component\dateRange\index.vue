<template>
  <el-radio-group class="data-screen-radio-group" v-model="radioValue" @change="onRadioChange">
    <el-radio-button
      v-for="item in dateRanges"
      :key="item.value"
      :label="item.value"
    >
      {{ item.label }}
    </el-radio-button>
  </el-radio-group>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const props = defineProps<{
  dateRange: number | null,
}>();
const emits = defineEmits<{
  (e: 'dateRangeChange', value: number | null): void,
}>();

const dateRanges = [
  { label: '近一月', value: 1 },
  { label: '近三月', value: 2 },
  { label: '近一年', value: 3 },
  { label: '累计', value: 0 },
];
const radioValue = ref(props.dateRange);
const onRadioChange = (value: number) => {
  emits('dateRangeChange', value || null);
};
</script>
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import JSONBIG from 'json-bigint';
import qs from 'qs';
// import { Session } from '/@/utils/storage';
import { useUserInfo } from '/@/stores/userInfo';
// 取消请求
import AxiosCanceler from "./requestCancel";
const axiosCanceler = new AxiosCanceler();

import commonFunction from './commonFunction';
const commonFun = commonFunction();
const hideErrorMsg = ['机器人不在线'] // 不显示的错误信息

// 扩展 AxiosRequestConfig 接口以包含自定义配置项
export interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  isTransformResponse?: boolean; // 是否自定义解析响应数据
}

// 配置新建一个 axios 实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 60000,
  headers: { 'Content-Type': 'application/json' },
  paramsSerializer: {
    serialize(params) {
      return qs.stringify(params, { allowDots: true });
    },
  },
});

/**
 * 某些接口的响应数据会包含大整数（多数情况为id），默认解析会造成精度丢失，使用`json-bigint`对接收到的数据做特殊处理
 * 定义接口时，通过配置项 `isTransformResponse`决定是否自定义解析
 * @param data 
 * @returns 经json-bigint处理后的数据
 */
const transformResponseFun = (data: string) => {
  try {
    const jsonBigData = JSONBIG().parse(data);
    let useData = jsonBigData.payload; // 前端实际使用的数据
    if (!useData) {
      return jsonBigData;
    }
    // 以下代码主要是将解析后类型为BigNumber的id，统一转为字符串
    let list;
    if (Array.isArray(useData)) {
      list = useData;
    } else if (Array.isArray(useData.content)) {
      list = useData.content;
    } else if (Object.prototype.toString.call(useData) === '[object Object]') {
      list = [useData];
    }
    list && list.forEach((item: any) => {
      if (item.id) item.id = item.id.toString();
    })
    return jsonBigData;
  } catch (err) {
    return data;
  }
};


// 添加请求拦截器
service.interceptors.request.use(
  (config) => {
    if (config.data) config.data = commonFun.filterObject(config.data)
    if (config.params) config.params = commonFun.filterObject(config.params)
    // 自定义解析响应数据
    if (config.isTransformResponse) {
      config.transformResponse = [transformResponseFun];
    }
    // 将当前请求添加到pending
    axiosCanceler.addPending(config);
    // 在发送请求之前做些什么 token
    // if (Session.get('token')) {
    // 	config.headers!['Authorization'] = `${Session.get('token')}`;
    // }
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 添加响应拦截器
service.interceptors.response.use(
  (response) => {
    // 请求结束后，移除pending
    axiosCanceler.removePending(response.config);
    // 对响应数据做点什么
    const res = response.data;
    if (res.status === 200 && res.errorCode) {
      ElMessage.error(res.msg || '服务器异常');
      return Promise.reject(response);
    } else {
      return res;
    }
  },
  (error) => {
    // 对响应错误做点什么
    const { response } = error;
    if (response) {
      errorHandle(response.status, response.data.msg)
      if (response.status !== 401) {
        return Promise.reject(response)
      }
      return Promise.reject(error);
    }
    // 取消pending请求时，不提示错误信息
    if (error.message.indexOf('canceled') !== -1) {
      return Promise.reject(error);
    }
    ElMessage.error('服务器异常');
    return Promise.reject(error)
  }
);

let isElMessageBoxShow = false;
const errorHandle = (status: number, errorMsg: string) => {
  switch (status) {
    case 400:
      ElMessage.error('输入数据无效');
      break
    case 401: // `token`过期
      if (isElMessageBoxShow) return;
      isElMessageBoxShow = true;
      ElMessageBox.alert('用户验证失败，请重新登录', '提示', {
        showClose: false,
        type: 'warning',
      }).then(() => {
        useUserInfo().userLogout();
        isElMessageBoxShow = false;
      })
        .catch(() => { });
      break
    case 403: // 无权限
      if (isElMessageBoxShow) return;
      isElMessageBoxShow = true;
      ElMessageBox.alert(`当前用户无权访问，请使用其他用户登录`, '提示', {
        showClose: false,
        type: 'warning',
      }).then(() => {
        useUserInfo().userLogout();
        isElMessageBoxShow = false;
      })
        .catch(() => { });
      break
    case 404:
      ElMessage.error('请求的地址不存在');
      break
    case 408:
      ElMessage.error('请求超时');
      break
    case 500:
      if (!hideErrorMsg.includes(errorMsg)) {
        ElMessage.error(errorMsg || '服务器异常');
      }
      break
    case 502:
      ElMessage.error(errorMsg || '网关出错');
      break
    case 503:
      ElMessage.error('服务不可用');
      break
    case 505:
      ElMessage.error('http版本不支持该请求');
      break
    default:
      ElMessage.error(errorMsg);
      break
  }
};

// 导出 axios 实例
export default service;

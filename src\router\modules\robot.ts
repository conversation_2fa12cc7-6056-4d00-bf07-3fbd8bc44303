import { RouteRecordRaw } from 'vue-router';

// window.systemConfig = robot
export const homeRoute = {
  path: '/home',
  name: 'Home',
  redirect: '/full-screen',
  meta: {
    title: '大屏一览',
    isHide: false,
    isKeepAlive: true,
    isAffix: true,
    icon: 'iconfont icon-shouye',
  },
};

export const staticRoutes: Array<RouteRecordRaw> = [
  {
    path: '/full-screen',
    name: 'FullScreen',
    component: () => import('/@/views/fullScreen/index.vue'),
    meta: {
      title: '大屏',
    },
  },
  {
    path: '/live/:liveId?',
    name: 'Live',
    component: () => import('/@/views/live/index.vue'),
    meta: {
      title: '直播',
    },
  },
  {
    path: '/robot-monitor/:deviceId?',
    name: 'RobotMonitor',
    component: () => import('/@/views/robotMonitor/index.vue'),
    meta: {
      title: '实况',
    },
  },
  {
    path: '/event-details',
    name: 'EventDetails',
    component: () => import('/@/views/eventDetails/index.vue'),
    meta: {
      title: '感知事件详情',
    },
  },
  {
    path: '/task/:deviceId?',
    name: 'Task',
    component: () => import('/@/views/task/index.vue'),
    meta: {
      title: '任务管理',
    },
  },
  {
    path: '/robot-detail/:deviceId?',
    name: 'RobotDetail',
    component: () => import('/@/views/robotDetail/index.vue'),
    meta: {
      title: '机器人管理',
    },
  },
  {
    path: '/robot-stat',
    name: 'RobotStat',
    component: () => import('/@/views/robotStat/index.vue'),
    meta: {
      title: '机器人统计',
    },
  },
];
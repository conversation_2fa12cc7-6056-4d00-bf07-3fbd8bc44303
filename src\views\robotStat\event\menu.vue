<template>
	<!-- @select="onMenuClick" @open="onMenuOpen" @close="onMenuClose" -->
	<div class="menu-container scrollbar-lg">
		<el-menu
			ref="elmenuRef"
			background-color="transparent"
			default-active="-1-item"
			:unique-opened="true"
			style="width: 216px"
			@open="handleMenuOpen"
			@close="handleMenuClose"
		>
      <template v-if="menuLists && menuLists.length > 0" >
        <template v-for="val in menuLists">
          <el-sub-menu
            :index="val.groupEventType + '-item'"
            v-if="val.children && val.children.length > 0"
            :key="val.groupEventType"
            :id="val.groupEventType + '-item'"
          >
            <template #title>
              <img
                :src="val.icon"
                width="25"
                height="25"
                style="display: block; width: 25; height: 25; margin-right: 10px; object-fit: contain"
                alt=""
              />
              <span style="font-size: 18px">{{ val.name }}</span>
            </template>
            <el-menu-item
              v-for="menu in val.children"
              :index="menu.eventType + '-sub'"
              :key="menu.eventType"
              :id="menu.eventType + '-sub'"
              @click="handleClick"
            >
              <template #title>
                <img
                  :src="menu.icon"
                  width="16"
                  height="16"
                  style="display: block; margin-right: 10px; object-fit: contain"
                  alt=""
                />
                <span style="font-size: 16px">{{ menu.name }}</span>
              </template>
            </el-menu-item>
          </el-sub-menu>
          <template v-else>
            <el-menu-item
              ref="allMenuRef"
              :index="val.groupEventType + '-item'"
              :key="val.groupEventType"
              :id="val.groupEventType + '-item'"
              @click="handleClick"
            >
              <template #title>
                <img
                  :src="val.icon"
                  width="25"
                  height="25"
                  style="display: block; margin-right: 10px; object-fit: contain"
                  alt=""
                />
                <span style="font-size: 18px">{{ val.name }}</span>
              </template>
            </el-menu-item>
          </template>
        </template>
      </template>
		</el-menu>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue';
import { getGroupSubEvents } from '/@/api/robotStat';
import other from '/@/utils/other';

interface MenuItemRegistered {
	index: string;
}
let itemMenuEl: HTMLElement | null;
let subMenuEl: HTMLElement | null;
const handleClick = (item: MenuItemRegistered, reHttp: boolean) => {
	if (active.value === item.index && !reHttp) return;

	if (subMenuEl?.classList.contains('active-bg')) {
		subMenuEl.classList.remove('active-bg');
	}

	if (item.index === '-1-item') {
		emits('menuItem', -1, menuLists.value);
		// 全部
		elmenuRef.value.close(active.value);
	} else {
		emits('menuItem', parseInt(item.index.split('-')[0]));
	}

	active.value = item.index;
	itemMenuEl = document.getElementById(item.index);
	itemMenuEl!.classList.add('is-active');
};

const handleMenuOpen = (id: string, indexPath?: string[], reHttp: boolean = false) => {
	if (active.value === id && !reHttp) return;

	if (itemMenuEl?.classList.contains('is-active')) {
		itemMenuEl.classList.remove('is-active');
	}
	if (subMenuEl?.classList.contains('active-bg')) {
		subMenuEl.classList.remove('active-bg');
	}
	const partId = parseInt(id.split('-')[0]);
	const events = menuLists.value.find((item) => item.groupEventType === partId)?.children;
	emits('menuSub', partId, events);
	subMenuEl = document.getElementById(id)?.firstElementChild as HTMLElement;
	subMenuEl.classList.add('active-bg');
	active.value = id;
};
const handleMenuClose = (id: string) => {
	if (active.value === id) return;
	if (itemMenuEl?.classList.contains('is-active')) {
		itemMenuEl.classList.remove('is-active');
	}
	if (subMenuEl?.classList.contains('active-bg')) {
		subMenuEl.classList.remove('active-bg');
	}
	handleMenuOpen(id);
};

const emits = defineEmits(['menuSub', 'menuItem']);
type MenuType = {
	name: string;
	icon: string;
	eventType?: number;
	groupEventType?: number;
	children?: MenuType[];
};
const elmenuRef = ref();
const active = ref('');
const menuLists = ref<MenuType[]>([
	{
		icon: other.getStaticImag('fullScreen/robotStat/event/quanbu.png'),
		name: '全部',
		groupEventType: -1,
	},
]);

getGroupSubEvents().then(({ payload }) => {
	const menu = payload.map((item: any) => {
		const children = item.subEventTypes.map((subItem: any) => {
			return {
				name: subItem.name,
				icon: other.getStaticImag(`fullScreen/robotStat/event/${subItem.eventType}.png`),
				eventType: subItem.eventType,
			};
		});
		return {
			name: item.name,
			icon: other.getStaticImag(`fullScreen/robotStat/event/g-${item.eventType}.png`),
			groupEventType: item.eventType,
			children,
		};
	});
	menuLists.value = [...menuLists.value, ...menu];
	emits('menuItem', -1, menuLists.value);
	nextTick(() => {
		itemMenuEl = document.getElementById('-1-item');
	});
});

const allMenuRef = ref();

onMounted(() => {});
defineExpose({
  activeMenu: active,
  handleClick,
  handleMenuOpen,
});
</script>

<style lang="scss" scoped>
.menu-container {
	width: 216px;
	height: 100%;
	overflow-x: hidden;
	overflow-y: auto;
	background-color: transparent;

	background-image: url('/src/assets/fullScreen/robotStat/event/menu-bg.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;

	&:deep(.el-sub-menu__title *) {
		vertical-align: top !important;
	}
	&:deep(.el-sub-menu__title) {
		div {
			overflow: visible !important;
			img {
				left: -5px !important;
			}
		}
	}
	&:deep(.el-menu-item *) {
		vertical-align: top !important;
	}
	&:deep(.el-menu-item) {
		div {
			width: 15px !important;
			height: 15px !important;
			overflow: visible !important;
			img {
				width: 15px !important;
				height: 15px !important;
				left: -5px !important;
			}
		}
	}

	.background-lg {
		background: linear-gradient(to bottom, rgba(104, 207, 173, 0.45), transparent) !important;
	}

	&:deep(.el-menu) {
		.el-menu-item {
			width: 216px;
			height: 24px;
		}
		.el-menu-item:hover {
			@extend .background-lg;
			.el-icon,
			.iconfont {
				transform: scale(1.08);
				transition: all 0.3s linear;
			}
		}
		.el-sub-menu__title:hover {
			@extend .background-lg;
		}
		.el-menu-item.is-active {
			@extend .background-lg;
		}

		.el-sub-menu {
			.active-bg {
				@extend .background-lg;
			}
		}

		.is-active {
			color: #fff !important;
			// .el-sub-menu__title {
			// 	background: transparent !important;
			// }
			.el-sub-menu__title:hover {
				background: linear-gradient(to bottom, rgba(104, 207, 173, 0.45), transparent) !important;
			}
		}

		.el-sub-menu {
			.active-bg {
				@extend .background-lg;
			}
		}
	}
}
</style>

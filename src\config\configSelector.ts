import readline from 'readline';
import { projectConfigs, configDescriptions } from './project.config';
import { updateConfig } from './configUpdater';

const askQuestion = async (query: string): Promise<string> => {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise(resolve => rl.question(query, ans => {
    rl.close();
    resolve(ans);
  }));
};

export async function handleConfigSelection(command?: string) {
  // 只在构建时触发
  if (command !== 'build') return;

  const configOptions = Object.entries(configDescriptions);
  const maxOption = configOptions.length;

  console.log('\x1b[36m%s\x1b[0m', `
═══════════════════════════════════════
请选择项目配置:

配置文件路径: \x1b[34m\x1b[4mfile://${process.cwd()}/src/config/project.config.ts\x1b[0m\x1b[36m

${configOptions.map(([key, desc], index) => `${index + 1}) ${desc}`).join('\n')}

输入 'n' 终止打包
═══════════════════════════════════════
  `);

  const answer = await askQuestion(`请输入选项 (1-${maxOption}，回车使用默认): `);

  // 检查是否要终止打包
  if (answer.trim().toLowerCase() === 'n') {
    console.log('\x1b[31m%s\x1b[0m', '已终止打包！');
    process.exit(0);
  }

  let selectedConfig;

  switch (answer.trim()) {
    case '2':
      selectedConfig = projectConfigs.robot;
      console.log('\x1b[32m%s\x1b[0m', `已选择: ${configDescriptions.robot}`);
      break;
    case '3':
      selectedConfig = projectConfigs.heilongjiang;
      console.log('\x1b[32m%s\x1b[0m', `已选择: ${configDescriptions.heilongjiang}`);
      break;
    case '4':
      selectedConfig = projectConfigs.ximeng;
      console.log('\x1b[32m%s\x1b[0m', `已选择: ${configDescriptions.ximeng}`);
      break;
    case '5':
      selectedConfig = projectConfigs.wetlandGreen;
      console.log('\x1b[32m%s\x1b[0m', `已选择: ${configDescriptions.wetlandGreen}`);
      break;
    case '1':
    case '':
      selectedConfig = projectConfigs.wetland;
      console.log('\x1b[32m%s\x1b[0m', `已选择: ${configDescriptions.wetland}`);
      break;
    default:
      selectedConfig = projectConfigs.wetland;
      console.log('\x1b[33m%s\x1b[0m', `无效输入，使用默认配置: ${configDescriptions.wetland}`);
  }

  updateConfig(selectedConfig);
  console.log('\x1b[32m%s\x1b[0m', '配置已更新！');
} 
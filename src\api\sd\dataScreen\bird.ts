import request from '/@/utils/request';

// # 鸟类
// 精美图集（瞬间）
export function getMoments(params: Object) {
  return request({
    url: `/moments/audit/dashboard`,
    method: 'get',
    params,
  });
}

// 物种频次排行
export function getSpeciesFrequencyRank(params: Object) {
  return request({
    url: `/dashboard/species/frequency/includeTotal/rank`,
    method: 'get',
    params,
  });
}

// 多样性趋势，即发现物种种类趋势
export function getSpeciesTypeTrend(params: Object) {
  return request({
    url: `/dashboard/trend/type`,
    method: 'get',
    params,
  });
}

// 种群栖息趋势
export function getSpeciesPopulationTrend(params: Object) {
  return request({
    url: `/dashboard/trend/detection/population`,
    method: 'get',
    params,
  });
}

// 居留类型
export function getResidencyStat(params: Object) {
  return request({
    url: `/dashboard/residency/statistics`,
    method: 'get',
    params,
  });
}

// 物种统计
export function getSpeciesStat(params: Object) {
  return request({
    url: `/dashboard/species/statistics`,
    method: 'get',
    params,
  });
}

// 物种占比
export function getSpeciesRatio(params: Object) {
  return request({
    url: `/dashboard/species/ratios/statistics`,
    method: 'get',
    params,
  });
}

// 最近发现
export function getRecentDiscovered(params: Object) {
  return request({
    url: `/dashboard/recently/discovered`,
    method: 'get',
    params,
  });
}

// 物种占比 - 发现次数
export function getSpeciesFrequencyRatio(params: Object) {
  return request({
    url: `/dashboard/species/frequency/ratios`,
    method: 'get',
    params,
  });
}

// 物种占比 - 发现个数
export function getSpeciesNumRatio(params: Object) {
  return request({
    url: `/dashboard/species/num/ratios`,
    method: 'get',
    params,
  });
}

// 监测趋势
export function getSpeciesTrend(params: Object) {
  return request({
    url: `/dashboard/trend/detection`,
    method: 'get',
    params,
  });
}

// 所有设备物种频次（热力数据）
export function getDevSpeciesFrequency(params: Object) {
  return request({
    url: `/dashboard/devices/species/frequency`,
    method: 'get',
    params,
  });
}

// 设备物种发现频次排行
export function getDevSpeciesFrequencyRank(params: Object) {
  return request({
    url: `/dashboard/species/frequency/rank`,
    method: 'get',
    params,
  });
}


// 鸟类轨迹
export function getDevSpecieslocus(params: Object) {
  return request({
    url: `/dashboard/species/locus`,
    method: 'get',
    params,
  });
}


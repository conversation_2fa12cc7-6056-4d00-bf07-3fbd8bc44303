<template>
	<el-amap
		:center="center"
		:zoom="zoom"
		mapStyle="amap://styles/blue"
		view-mode="3D"
		:pitch="42"
		@init="initMap"
		@zoomchange="onMapZoomChange"
	>
		<!-- <el-amap-control-tool-bar /> -->
		<!-- <el-amap-control-map-type /> -->
		<!-- <el-amap-layer-satellite :visible="state.satelliteVisible" /> -->

		<!-- 湿地区域边界 -->
		<el-amap-polyline
			v-if="state.areaPolylinePath.length > 0"
			:visible="true"
			:draggable="false"
			:path="state.areaPolylinePath"
			:strokeOpacity="0.9"
			:strokeWeight="4"
			strokeColor="#37FF9D"
			@init="initPolyline"
		/>

		<el-amap-marker
			v-for="(item, $index) in deviceList"
			:key="item.num"
			:position="item.location"
			:topWhenClick="true"
			:zIndex="20"
			@init="initMarker"
		>
			<!-- `红外设备sourceType = 2` 和 `监控设备sourceType = 3` -->
			<div
				class="map-custom-marker"
				@mouseover.native="state.activeDeviceIndex = $index"
				@mouseleave.native="state.activeDeviceIndex = -1"
			>
				<div class="point-icon" @click.stop="onDevMarkerClick($event, item)">
					<img
						v-show="state.activeDevice !== item.num && state.activeDeviceIndex !== $index"
						:src="item.sourceType === 2 ? infraredIcon : monitoringIcon"
						alt=""
					/>
					<img
						v-show="state.activeDevice === item.num || state.activeDeviceIndex === $index"
						class="is-active"
						:src="item.sourceType === 2 ? infraredIconActive : monitoringIconActive"
						alt=""
					/>
				</div>
				<div class="text" v-show="labelShow">{{ item.name }}</div>
			</div>
		</el-amap-marker>

		<el-amap-loca @init="initLoca">
			<!-- heatmap -->
			<el-amap-loca-heatmap
				:visible="state.heatMapVisible"
				:source-data="state.heatMapData"
				:layer-style="heatmapLayerStyle"
			/>
			<!-- pulseLink -->
			<template v-if="pulseLinkSourceData.length">
				<el-amap-loca-pulse-link
					v-for="item in pulseLinkSourceData"
					:key="item"
					:source-data="item.source"
					:layer-style="item.style"
					:visible-duration="500"
					:zIndex="30"
				/>
			</template>
		</el-amap-loca>

		<RealTimeVideoWindow ref="realTimeVideoWindowRef"></RealTimeVideoWindow>
		<FrequencyRankWindow ref="frequencyRankWindowRef"></FrequencyRankWindow>
	</el-amap>
</template>

<script setup lang="ts">
import { ref, shallowRef, reactive, inject, watch, onUnmounted, nextTick } from 'vue';
import { ElAmap } from '@vuemap/vue-amap';
import { generatePlayUrl } from '/@/api/devices';
import { getDevSpeciesFrequency, getDevSpecieslocus } from '/@/api/sd/dataScreen/bird';
import { ElAmapLoca, ElAmapLocaPulseLink } from '@vuemap/vue-amap-loca';
import { IsCompanyNmg } from '/@/hooks/useConfig';
import { calculateWindowOffset, transCoordinate, randomInteger } from './utils';
// 设备点位图标
import infraredIcon from '/@/assets/sd/data-screen/point-infrared.png';
import infraredIconActive from '/@/assets/sd/data-screen/point-infrared-active.png';
import monitoringIcon from '/@/assets/sd/data-screen/point-monitoring.png';
import monitoringIconActive from '/@/assets/sd/data-screen/point-monitoring-active.png';
// 组件
import RealTimeVideoWindow from './realTimeVideoWindow.vue';
import FrequencyRankWindow from './frequencyRankWindow.vue';
// 湿地区域边界json
import xmPolygonJson from './polygonJson/xm.json'; // 锡盟（gps84）

const props = defineProps<{
	animalTag?: number;
	animalNameList?: string[];
	timeType: number | null;
}>();
const deviceList = inject<DeviceRow[]>('deviceList');

// map
let map = shallowRef<any>(null); // 地图实例
const zoom = ref(12);
const center = ref<[number, number]>();
const state = reactive({
	activeDeviceIndex: -1, // 激活设备下标
	activeDevice: '', // 激活设备num
	// heatmap
	heatMapVisible: false,
	heatMapData: { type: 'FeatureCollection', features: [] },
	heatMapMax: 0,
	// Polyline
	areaPolylinePath: [] as [number, number][],
});

const initMap = (instance: any) => {
	map.value = instance;
	getPolylineData();
};
// 初始化湿地边界（优先）图层或设备图层后，调整地图视野；
const initPolyline = (polyline: any) => {
	map.value.setFitView([polyline], true, [10, 10, 50, 50]);
};
const initMarker = () => {
	if (state.areaPolylinePath.length === 0) {
		map.value.setFitView(undefined, true, [10, 10, 50, 50]); // 上、下、左、右
	}
};

const labelShow = ref(zoom.value >= 11.1);
const onMapZoomChange = () => {
	labelShow.value = map.value.getZoom() >= 11.1;
};

// # 湿地区域边界
const getPolylineData = () => {
	// 锡盟
	if (IsCompanyNmg) {
		const coordinates = xmPolygonJson.features[0].geometry.coordinates[0];
		state.areaPolylinePath = coordinates.map((coord: [number, number]) => {
			return transCoordinate(coord, 1, 2);
		});
	}
};

// # 热力图（设备物种频次）
const heatmapLayerStyle = ref({
	radius: 40,
	unit: 'px',
	height: 0,
	gradient: {
		'0.2': 'blue',
		'0.4': 'rgb(117,211,248)',
		'0.6': 'rgb(0, 255, 0)',
		'0.8': '#ffea00',
		'1.0': 'red',
	},
	value(index: number, feature: any) {
		return feature.properties.count;
	},
	// min: 0,
	// max: state.heatMapMax,
	heightBezier: [0.5, 0, 1, 0.5],
});
const getHeatMapData = async () => {
	const query = {
		animalNameList: props.animalNameList,
		animalTag: props.animalTag,
		timeType: props.timeType,
	};
	const { payload } = await getDevSpeciesFrequency(query);
	const data: any = [];

	let max = 0;
	payload.forEach((item: any) => {
		const { longitude, latitude, coordinateType, speciesFrequency } = item;
		if (longitude && latitude && speciesFrequency > 0) {
			data.push({
				type: 'Feature',
				properties: {
					count: speciesFrequency,
				},
				geometry: {
					type: 'Point',
					coordinates: transCoordinate([longitude, latitude], coordinateType, 2),
				},
			});
			if (speciesFrequency > max) max = speciesFrequency;
		}
	});

	state.heatMapData.features = data;
	state.heatMapMax = max;
	state.heatMapVisible = true;
};

// # 点击设备marker，显示window窗口
const realTimeVideoWindowRef = ref<InstanceType<typeof RealTimeVideoWindow>>();
const frequencyRankWindowRef = ref<InstanceType<typeof FrequencyRankWindow>>();
const onDevMarkerClick = async (event: MouseEvent, device: DeviceRow) => {
	// 弹窗位置跟随鼠标
	// 1、内蒙古（锡盟）项目，查看物种频次排行
	if (IsCompanyNmg) {
		const { eventX, eventY, panX, panY } = calculateWindowOffset(event, 'rank');
		map.value.panBy(panX, panY, 150);
		state.activeDevice = device.num;
		frequencyRankWindowRef.value?.openDialog(
			{
				eventX,
				eventY,
				device: device.num,
				animalNameList: props.animalNameList,
				animalTag: props.animalTag,
				timeType: <number>props.timeType,
			},
			() => {
				state.activeDevice = '';
			}
		);
	} else {
		// 2、其他，查看实时视频，仅监控设备 `sourceType = 3`
		if (device.sourceType !== 3) return;
		const { eventX, eventY, panX, panY } = calculateWindowOffset(event, 'video');
		map.value.panBy(panX, panY, 150);
		state.activeDevice = device.num;
		realTimeVideoWindowRef.value?.openDialog(
			{
				eventX,
				eventY,
				playUrl: generatePlayUrl(device.id),
			},
			() => {
				state.activeDevice = '';
			}
		);
	}
};

// # 物种飞行轨迹曲线，仅在鸟类标签页，且选中某物种时展示
const initLoca = (loca: any) => {
	loca.animate.start();
};
const pulseLinkSourceData: any = ref([]);
const getLayerStyle = (lineColor: string[]) => {
	return {
		unit: 'meter',
		dash: [4000, 0, 4000, 0],
		lineWidth(index: number, feat: any) {
			const lineWidth = feat.link.properties.lineWidthRatio;
			return [lineWidth[0], lineWidth[1]];
		},
		height(index: number, feat: any) {
			return feat.distance / 2;
		},
		smoothSteps: 50,
		speed(index: number, feat: any) {
			return feat.distance / 2;
		},
		flowLength: 10,
		lineColors: lineColor,
		maxHeightScale: 0.5, // 弧顶位置比例
		headColor: 'rgba(255, 255, 255, 1)',
		trailColor: 'rgba(255, 255, 255, 0)',
	};
};
const getLocusLine = () => {
	pulseLinkSourceData.value = [];
	if (props.animalTag === undefined) return;
	if (props.animalNameList?.length === 0) return;
	const query = {
		animalNameList: props.animalNameList,
		animalTag: props.animalTag,
		timeType: props.timeType,
	};
	getDevSpecieslocus(query).then(({ payload }) => {
		const transformedData = transformLocusLineData(payload);

		Object.values(transformedData).forEach((trans: any) => {
			const obj = {
				source: {
					type: 'FeatureCollection',
					features: [] as any,
				},
				style: {},
			};
			trans.forEach((tr: any) => {
				obj.style = getLayerStyle(tr.lineColor);
				obj.source.features.push({
					type: 'Feature',
					properties: {
						lineWidthRatio: tr.speciesFrequency,
					},
					geometry: {
						type: 'LineString',
						coordinates: tr.LatLonList,
					},
				});
			});
			pulseLinkSourceData.value.push(obj);
		});

		nextTick(() => {
			map.value.setFitView(undefined, true, [10, 10, 10, 10]);
		});
	});
};
/**
 * 物种飞行轨迹按天统计，每天都可能存在0、1个或多个点位，此处只记录存在有效点位的日期
 * n天监测到物种，n天中的每个点位都算作起点，n+1中的每个点位都为n的终点，以此类推；即两个数据集进行两两组合，形成多条轨迹线
 * 同物种的多条轨迹，用一种颜色表示
 * 提前为每个点位都定义一个偏移坐标，避免起点与终点相同的情况
 */
function transformLocusLineData(data: Track[]) {
	const result: any = {};
	data.forEach((entry: Track) => {
		if (entry.LatLonList) {
			const { LatLonList, speciesName, speciesFrequency } = entry;
			if (!result[speciesName]) {
				result[speciesName] = [];
			}
			result[speciesName].push({
				LatLonList: JSON.parse(LatLonList).map((item: number[]) => {
					const originPos = transCoordinate([item[0], item[1]], item[2], 2);
					// 为每个点位定义偏移坐标（沿x轴向右偏移50像素）
					const [originX, originY] = map.value.lngLatToCoords(originPos);
					const offsetPos = map.value.coordsToLngLat([originX + 50, originY]);
					return {
						originPos: originPos,
						offsetPos: [offsetPos.getLng(), offsetPos.getLat()],
					};
				}),
				speciesFrequency,
			});
		}
	});

	const finalResult: any = {};
	for (const species in result) {
		const entries = result[species];
		if (entries.length < 2) continue;

		// 为每个物种随机生成一种链接线颜色数组
		const lineColor = [
			`rgb(${randomInteger(0, 255)}, ${randomInteger(0, 255)}, ${randomInteger(0, 255)})`,
		];

		const mergedEntries = [];
		for (let i = 0; i < entries.length - 1; i++) {
			const current = entries[i];
			const next = entries[i + 1];
			// 两两组合
			for (let ci = 0; ci < current.LatLonList.length; ci++) {
				for (let ni = 0; ni < next.LatLonList.length; ni++) {
					const startPos = current.LatLonList[ci].originPos;
					let endPos = next.LatLonList[ni].originPos;
					// 若起点与终点相同，此时把偏移坐当作终点坐标
					if (JSON.stringify(startPos) === JSON.stringify(endPos)) {
						endPos = next.LatLonList[ni].offsetPos;
					}
					mergedEntries.push({
						LatLonList: [startPos, endPos],
						speciesFrequency: [current.speciesFrequency, next.speciesFrequency],
						lineColor: lineColor,
					});
					// 下一次的起点为上一次的终点
					// next.LatLonList[ni].originPos = endPos;
				}
			}
		}
		finalResult[species] = mergedEntries;
	}
	return finalResult;
}

onUnmounted(() => {
	map.value.destroy();
});

watch(
	[() => props.animalNameList, () => props.timeType],
	() => {
		getHeatMapData();
		getLocusLine();
	},
	{ immediate: true }
);
</script>

<style lang="scss" scoped>
.map-custom-marker {
	text-align: center;
	transform: translate(-50%, calc((vw(12) + vw(6)) - vw(114)));
	user-select: none;
	.point-icon img {
		width: vw(40);
		cursor: pointer;
		pointer-events: auto;
		vertical-align: middle;
		&.is-active {
			transform: scale(1.1);
			transform-origin: center bottom;
		}
	}
	.text {
		margin-top: vw(6);
		font-size: vw(12);
		font-family: 'ALiMaMaShuHeiTi';
		white-space: nowrap;
	}
}

:deep(.amap-toolbar) {
	right: 22vw !important;
	z-index: 999;
}

:deep(.amap-maptype) {
	bottom: 200px !important;
	right: 22vw !important;
	z-index: 999;
}
</style>

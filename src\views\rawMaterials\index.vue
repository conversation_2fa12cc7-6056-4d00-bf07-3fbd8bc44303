<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
        <!-- `测试数据`子菜单时不展示设备筛选 -->
				<el-form-item v-if="typeFilter !== 6" label="设备">
					<el-select v-model="state.tableData.filter.imei" placeholder="请选择" clearable>
						<el-option
							v-for="item in deviceOptions"
							:key="item.id"
							:label="`设备编号：${item.num}，名称：${item.channelName || item.name}`"
							:value="item.num"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="类型">
					<el-select v-model="state.tableData.filter.type" placeholder="请选择" clearable>
						<el-option label="图片" :value="0"></el-option>
						<el-option label="视频" :value="1"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="是否识别">
					<el-select v-model="state.tableData.filter.hasRecord" placeholder="请选择" clearable>
						<el-option label="全部" value=""></el-option>
						<el-option label="未识别" :value="false"></el-option>
						<el-option label="已识别" :value="true"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="">
					<el-date-picker
						v-model="state.tableData.filter.timePeriod"
						type="datetimerange"
						range-separator="-"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						value-format="YYYY-MM-DD HH:mm:ss"
						clearable
					/>
				</el-form-item>
			</template>
		</ViewSearch>

		<el-row class="atlas" v-loading="state.tableData.loading" :gutter="20">
			<template v-if="state.tableData.data.length > 0">
				<el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6" v-for="item in state.tableData.data" :key="item.id">
					<el-card class="atlas-item">
						<div class="thumbnail-container">
							<div class="main-thumb">
								<!-- type：0图片 1视频 -->
								<el-image
									v-if="!item.coverUrl && !item.url"
									class="thumbnail"
									:src="DefaultCoverUrl" fit="cover"
									lazy
								></el-image>
								<el-image
									v-else
									class="thumbnail"
									:src="item.type === 1 ? item.coverUrl : item.thumbnailUrl"
									fit="cover"
									:preview-src-list="[item.type === 1 ? item.coverUrl : item.url]"
									lazy
								></el-image>
							</div>
							<!-- 图片素材识别结果：monitorEvents -->
							<div class="thumb-recs" v-if="item.type === 0 && item.monitorEvents && item.monitorEvents.length > 0">
								<div class="thumb-rec" v-for="me in item.monitorEvents" :key="me.id">
									<el-image
										class="thumbnail"
										:src="me.pictures[0].pictureUrl || me.pictures[0].oriPictureThumbnailUrl"
										fit="cover"
										:preview-src-list="[me.pictures[0].pictureUrl || me.pictures[0].oriPictureUrl]"
										lazy
									></el-image>
									<div class="recResult-content">
										<template v-if="!me.pictures[0].rawResult">
											未发现物种
										</template>
										<span v-else v-for="res in me.pictures[0].monitorEventDetails" :key="res.recResult">
											{{ res.recResult }}（{{ res.recResultCnt }}）
										</span>
									</div>
								</div>
							</div>
						</div>
						<div class="other-content">
              <!-- `测试数据`子菜单时不展示设备信息 -->
              <div>
                <span v-if="typeFilter !== 6" class="mr5">{{ item.imei }}</span>
                <el-tag v-show="!item.monitorEvents || item.monitorEvents.length === 0" type="info" size="small">
                  未识别
                </el-tag>
              </div>
              <div v-if="typeFilter !== 6" class="mt5">
                GPS：{{ item.longitude ? `${item.longitude},${item.latitude}` : '-' }}
              </div>
							<div class="mt5">{{ item.createTime }}</div>
							<div class="operate-btns" v-auths="['*:*:*', 'images:*:*']">
								<template v-if="item.type === 1" >
									<el-button type="primary" text title="视频播放" @click="onPlay(item.url)">
										<i class="iconfont icon-bofang font16"></i>
									</el-button>
									<el-button v-show="item.monitorEvents && item.monitorEvents.length > 0" type="primary" text title="识别历史" @click="onVideoRec(item)">
										<i class="iconfont icon-AI font16"></i>
									</el-button>
								</template>
								<el-button type="primary" text title="下载" @click="onDownload(item)">
									<i class="iconfont icon-xiazai font17"></i>
								</el-button>
							</div>
						</div>
					</el-card>
				</el-col>
			</template>
			<el-empty class="flex-margin" v-else></el-empty>
		</el-row>
		<el-pagination
			class="mt10"
			v-model:current-page="state.tableData.pageParams.page"
			:page-sizes="[12, 24, 38, 48]"
			background
			v-model:page-size="state.tableData.pageParams.size"
			layout="total, sizes, prev, pager, next, jumper"
			:total="state.tableData.total"
			@size-change="onHandleSizeChange"
			@current-change="onHandleCurrentChange"
		>
		</el-pagination>
		<VideoPlayerDialog ref="VideoPlayerDialogRef"></VideoPlayerDialog>
	</div>
</template>

<script setup lang="ts" name="RawMaterials">
import { ref, reactive, onMounted, defineAsyncComponent, computed } from 'vue';
import { useRouter } from 'vue-router';
import { getRawMaterials, downloadFile } from '/@/api/rawMaterials';
import { useDevices } from '/@/hooks/useDevices';
import { Session } from '/@/utils/storage';
import DefaultCoverUrl from '/@/assets/od-cover.png';

const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const VideoPlayerDialog = defineAsyncComponent(() => import('./components/videoPlayerDialog.vue'));

// 定义变量内容
const props = defineProps<{
  typeFilter: number; // 1红外感知抓拍、3监控巡航抓拍、5巡护感知抓拍、6测试数据
}>(); 
const router = useRouter();
const state = reactive<ViewBaseState<RMRow>>({
	deviceOptions: [],
	tableData: {
		filter: {
			imei: '',
			type: '',
			hasRecord: '',
			timePeriod: [],
			sort: 'createTime,desc',
		},
		data: [],
		loading: false,
		pageParams: {
			page: 1,
			size: 12,
		},
		total: 0,
	},
});
const devices = useDevices();
const deviceOptions = computed(() => {
  // 红外相机 `sourceType = 2`
  if (props.typeFilter === 1) {
    return devices.data.value.filter((item: DeviceRow) => item.sourceType === 2);
  }
  // 监控设备 `sourceType = 1` 和 `sourceType = 3`
  if (props.typeFilter === 3) {
    return devices.data.value.filter((item: DeviceRow) => (item.sourceType === 1 || item.sourceType === 3));
  }
  // 巡护设备 `sourceType = 4`
  if (props.typeFilter === 5) {
    return devices.data.value.filter((item: DeviceRow) => (item.sourceType === 4));
  }
	return devices.data.value;
});

// 页面加载时
onMounted(() => {
	getTableData();
});

// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	const { timePeriod } = state.tableData.filter;
	const query = {
    typeFilter: props.typeFilter,
		...state.tableData.filter,
		page: state.tableData.pageParams.page - 1,
		size: state.tableData.pageParams.size,
		startTime: timePeriod && timePeriod.length > 0 ? timePeriod[0] : null,
		endTime: timePeriod && timePeriod.length > 0 ? timePeriod[1] : null,
		timePeriod: null,
	};
	const { payload } = await getRawMaterials(query)
	state.tableData.data = payload.content;
	state.tableData.total = payload.totalElements;
	state.tableData.loading = false;
};
const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetPage) state.tableData.pageParams.page = 1;
	if (resetFilter) {
		state.tableData.filter.imei = '';
		state.tableData.filter.type = '';
		state.tableData.filter.hasRecord = '';
		state.tableData.filter.timePeriod = [];
	}
	getTableData();
};
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getTableData();
	resetScroll();
};
const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getTableData();
	resetScroll();
};
// 重置滚动条
const resetScroll = () => {
	// .layout-backtop-header-fixed .el-scrollbar__wrap
	const scrollEle = document.querySelector('.layout-backtop .el-scrollbar__wrap');
	(<HTMLDivElement>scrollEle).scrollTo({
		top: 0,
		behavior: 'smooth',
	});
};
// 播放视频
const VideoPlayerDialogRef = ref<InstanceType<typeof VideoPlayerDialog>>();
const onPlay = (url: string) => {
  const data = {
    type: 'video',
    playUrl: url,
  };
	VideoPlayerDialogRef.value?.openDialog(data);
};
// 视频识别记录
const onVideoRec = (data: RMRow) => {
	Session.set('rawMaterials-videoRec-data', data);
	router.push({
		path: '/rawMaterials/videoRec',
	});
};
const onDownload = ({ imei, url, fileId }: RMRow) => {
	const suffix = fileId.slice(fileId.lastIndexOf('.'));
	downloadFile(url).then((res) => {
		const url = URL.createObjectURL(new Blob([res as unknown as Blob]));
		const link = document.createElement('a');
		link.href = url;
		link.download = imei + suffix;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		URL.revokeObjectURL(url);
	});
};
</script>

<style scoped lang="scss">
@import '/@/theme/atlas.scss';
</style>
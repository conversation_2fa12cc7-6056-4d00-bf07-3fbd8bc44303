<template>
	<div class="side-container" :class="[direction, { 'is-collapsed': modelValue }]">
		<div class="content">
			<slot></slot>
		</div>
		<SlideControl
			v-if="isControl"
			:direction="direction"
			:modelValue="modelValue"
			@update:modelValue="$emit('update:modelValue', $event)"
		/>
	</div>
</template>

<script setup lang="ts">
import SlideControl from './slideControl.vue';

const props = withDefaults(
	defineProps<{
		direction: 'left' | 'right';
		modelValue?: boolean;
		isControl?: boolean;
	}>(),
	{
		direction: 'left',
		modelValue: false,
		isControl: true,
	}
);

defineEmits<{
	(e: 'update:modelValue', value: boolean): void;
}>();
</script>

<style lang="scss" scoped>
.side-container {
	position: absolute;
	top: 0;
	z-index: 10;
	width: vw(402);
	height: 100%;
	padding: vw(15);
	box-sizing: border-box;
	transition: all 0.3s ease;
	&.left {
		left: 0;
		&::before {
			display: block;
			content: '';
			width: calc(100% + vw(96));
			height: 100%;
			position: absolute;
			left: 0;
			top: 0;
			background: linear-gradient(
				to right,
				$sd-screen-bg-color calc((100% - vw(96)) / 2),
				transparent
			);
			z-index: -1;
			pointer-events: none;
		}
		&.is-collapsed {
			margin-left: vw(-402);
		}
	}

	&.right {
		right: 0;
		// 阴影
		&::before {
			display: block;
			content: '';
			width: calc(100% + vw(96));
			height: 100%;
			position: absolute;
			right: 0;
			top: 0;
			background: linear-gradient(
				to left,
				$sd-screen-bg-color calc((100% - vw(96)) / 2),
				transparent
			);
			z-index: -1;
			pointer-events: none;
		}
		&.is-collapsed {
			margin-right: vw(-402);
		}
	}

	.content {
		width: 100%;
		height: 100%;
		overflow: hidden;
		display: flex;
		flex-direction: column;
	}
}
</style>

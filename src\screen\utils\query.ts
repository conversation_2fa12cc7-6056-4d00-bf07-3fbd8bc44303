import { omit, omitBy, isNil, isEmpty, isArray } from 'lodash';
interface BaseQuery {
  timeType?: TimeType;
  deviceList?: string[];
  eventType?: string | number;
  eventTypeList?: number[];
  filterType?: any;
  [key: string]: any;
}

/**
 * 处理查询参数
 * @description 统一处理 timeType、deviceList 等通用查询参数，使用 lodash 优化
 * @param baseQuery 基础查询对象
 * @returns 处理后的查询对象
 */
export function createQuery(baseQuery: BaseQuery = {}): BaseQuery {
  let query = { ...baseQuery };

  // 处理 timeType === 'all' 时删除该字段
  if (query.timeType === 'all') {
    query = omit(query, 'timeType');
  }

  // 处理 eventType === 'all' 时删除并添加 eventTypeList
  if (query.eventType === 'all') {
    query = { ...omit(query, 'eventType'), eventTypeList: [0, 1, 21] };
  }

  // 处理空数组 deviceList
  if (isArray(query.deviceList) && isEmpty(query.deviceList)) {
    query = omit(query, 'deviceList');
  }

  // 移除所有 undefined、null 和空值，使用 lodash 的 omitBy
  query = omitBy(query, (value) => isNil(value) || (isArray(value) && isEmpty(value)));

  return query
}
<template>
  <div class="layout-pd">
    <div class="first-level-title font18 mb15">设备：{{ state.rmData.imei }}</div>
    <div v-for="me in state.rmData.monitorEvents" :key="me.id">
      <div class="ai-model-name">
        <el-tag>{{ me.aiModelName }}</el-tag>
      </div>
      <el-row class="atlas" :label="me.aiModelId" :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" :lg="4" :xl="4" v-for="(mePic, $index) in me.pictures" :key="mePic.id">
          <el-card class="atlas-item">
            <div class="thumb-rec">
              <el-image
                class="thumbnail"
                :src="mePic.pictureUrl || mePic.oriPictureThumbnailUrl"
                fit="cover"
                lazy
                @click="onImageViewerOpen(me.pictures, $index)"
              ></el-image>
              <div class="recResult-content">
                <template v-if="!mePic.rawResult">
                  未发现物种
                </template>
                <span v-for="res in mePic.monitorEventDetails" :key="res.recResult">
                  {{ res.recResult }}（{{ res.recResultCnt }}）
                </span>
              </div>
            </div>
            <div class="other-content">
              <div>{{ mePic.createTime }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <el-image-viewer
      v-if="state.showImageViewer"
      :url-list="state.imageViewerList"
      :initial-index="state.viewerIndex"
      @close="onImageViewerClose"
    ></el-image-viewer>
  </div>
</template>

<script setup lang="ts" name="RawMaterialsVideoRec">
import { reactive, onMounted } from 'vue';
import { Session } from '/@/utils/storage';

const state = reactive({
  rmData: <RMRow>{},
  showImageViewer: false,
  imageViewerList: <string[]>[],
  viewerIndex: -1,
});
onMounted(() => {
  state.rmData = Session.get('rawMaterials-videoRec-data');
});
const onImageViewerOpen = (pictures: MonitorEventRow[], $index: number) => {
  const imageList = pictures.map((item) => (item.pictureUrl || item.oriPictureUrl));
  state.imageViewerList = imageList;
  state.viewerIndex = $index;
  state.showImageViewer = true;
};
const onImageViewerClose = () => {
  state.showImageViewer = false;
  state.imageViewerList = [];
};
</script>

<style lang="scss" scoped>
@import '../../theme/atlas.scss';
.ai-model-name {
  margin: 10px 0;
  font-size: 16px;
}
:deep(.el-tabs--border-card>.el-tabs__content) {
  padding-top: 0;
}
</style>
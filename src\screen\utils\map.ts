import { convertCoordinates } from '/@/utils/coordinates';

import { bd09_To_gps84, bd09_To_gcj02, gps84_To_bd09, gps84_To_gcj02, gcj02_To_bd09, gcj02_To_gps84 } from '@vuemap/vue-amap';

// 添加 Device 和 MessageItem 类型定义
interface Device {
  id: string;
  name: string;
  longitude: number;
  latitude: number;
  coordinateType: number | null;
  onlineStatus: number | null;
}

// 添加 MessageItem 类型定义
interface MessageItem {
  monitorEventFileModel?: {
    coordinateType?: number | null;
    longitude?: number;
    latitude?: number;
    monitorEventDetails?: Array<{
      remark?: string;
    }>;
  };
}

export interface MarkerInfo {
  position: [number, number];
  data: {
    id: string;
    name: string;
    status: number;
  };
}

export interface WindowState {
  visible: boolean;
  position: [number, number];
}


/**
 * 转换经纬度
 * @param coord 经纬度数组
 * @param current 当前（coord）经纬度类型 null 为GCJ02
 * @param target 目标经纬度类型
 * @returns 
 */
// 坐标系类型 0：bd09  1：wgs84  2：GCJ02
const transCoordinateMap: { [key: string]: Function } = {
  '0-1': bd09_To_gps84,
  '0-2': bd09_To_gcj02,
  '1-0': gps84_To_bd09,
  '1-2': gps84_To_gcj02,
  '2-0': gcj02_To_bd09,
  '2-1': gcj02_To_gps84,
}
export function transCoordinate(coord: [number, number], current: number | null, target: number): [number, number] {
  current = current || 1;
  if (!coord[0] && !coord[1]) return [0, 0];
  if (current === target) return coord;
  const { lng, lat } = transCoordinateMap[(`${current}-${target}`) as string](coord[0], coord[1]);
  return [lng, lat];
}



/**
 * 生成包含{min}至{max}之间的随机整数
 * @param min 最小值
 * @param max 最大值
 * @returns 随机数
 */
export const randomInteger = (min = 0, max = 50) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};




export const convertDevicesToMarkers = (deviceList: Device[]) => {
  return deviceList
    .filter((device) => device.longitude !== 0 && device.latitude !== 0)
    .map((device) => ({
      position: convertCoordinates(device.longitude, device.latitude, device.coordinateType),
      data: {
        id: device.id,
        name: device.name,
        status: device.onlineStatus,
      },
    }));
};

export const getInitialWindowState = (): WindowState => ({
  visible: false,
  position: [0, 0],
});

// 从消息中获取坐标信息
export const getCoordinatesFromMessage = (msg: MessageItem): [number, number] => {
  // 首先尝试从 remark 获取坐标
  if (msg.monitorEventFileModel?.monitorEventDetails?.length) {
    const detail = msg.monitorEventFileModel.monitorEventDetails[0];
    if (detail?.remark) {
      try {
        const remarkValue = JSON.parse(detail.remark);
        if (Array.isArray(remarkValue) && remarkValue.length === 2) {
          const [lng, lat] = remarkValue;
          if (!(lng === 0 && lat === 0)) {
            return transCoordinate(
              [lng, lat],
              msg.monitorEventFileModel?.coordinateType || null,
              2
            );
          }
        }
      } catch {
        // remark 解析失败，继续使用默认经纬度
      }
    }
  }

  // 使用默认经纬度
  const { longitude, latitude } = msg.monitorEventFileModel || {};
  return transCoordinate(
    [longitude!, latitude!],
    msg.monitorEventFileModel?.coordinateType || null,
    2
  );
};

/**
 * 判断文本是否为短文本，用于设备名称显示样式
 * @param text 要判断的文本
 * @param maxLength 最大长度
 * @returns 是否为短文本
 */
export const isShortText = (text: string, maxLength = 10) => {
  if (!text) return true;

  // 计算文本的"有效长度"
  // 中文字符计为2，其他字符计为1
  let effectiveLength = 0;
  for (let i = 0; i < text.length; i++) {
    // 判断是否为中文字符（Unicode范围）
    if (/[\u4e00-\u9fa5]/.test(text[i])) {
      effectiveLength += 2;
    } else {
      effectiveLength += 1;
    }
  }

  // 根据有效长度判断是否为短文本
  return effectiveLength <= maxLength; // 可以根据实际情况调整阈值
};
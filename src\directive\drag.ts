// export default {
//   install(app: any) {
//     defaneDrag(app);
//   },
// };
import { nextTick } from 'vue';
import type { App } from 'vue';
export function defaneDrag(app: App) {
  app.directive("drag", {
    mounted(el) {
      console.log("drag-mounted", el.scrollWidth, el.scrollHeight);
      el.style.width = el.scrollWidth + 'px'
      el.style.height = el.scrollHeight + 'px'

      let body = document.body;
      body.style.position = "relative";
      el.style.position = "fixed";

      // 设置鼠标hover效果:移动上前去显示可移动的提示效果，并且禁用页面可选择，离开恢复正常
      el.onmouseover = () => {
        el.style.cursor = "move";
      };
      el.onmouseout = () => {
        el.style.cursor = "none";
      };
      // 防止选中移动块上的文字等
      body.onmouseover = () => {
        document.onselectstart = () => {
          return false;
        };
      };
      body.onmouseout = () => {
        document.onselectstart = () => {
          return true;
        };
      };

      el.onmousedown = (event: any) => {
        //event的兼容,同时得到clientX,的值
        var event = event || window.event;
        let x = event.clientX - el.offsetLeft;
        let y = event.clientY - el.offsetTop; //得到小段的偏移
        // 将移动事件绑定到 document 上，防止拖动过快脱离开
        document.onmousemove = (event: any) => {
          let xx = event.clientX - x; //当移动的时候，用它的鼠标值减去偏移量
          let yy = event.clientY - y;
          if (xx <= 0) {
            //判定边界值 0，就在最边上了,
            xx = 0;
          }
          if (xx >= body.offsetWidth - el.offsetWidth) {
            //大于整个盒子的宽度-小盒子的宽度
            xx = body.offsetWidth - el.offsetWidth;
          }
          if (yy <= 0) {
            yy = 0;
          }
          if (yy >= body.offsetHeight - el.offsetHeight) {
            yy = body.offsetHeight - el.offsetHeight;
          }

          el.style.left = xx + "px";
          el.style.top = yy + "px";
        };
        el.onmouseup = () => {
          // 取消事件
          document.onmousemove = null;
          el.onmouseup = null;
        };
      };
    },
    updated(el, binding) {
      // 当相关数据变化时，这个钩子会被调用

      console.log(el.scrollWidth, el.scrollHeight);
      // el.style.height = 0
      // // el.style.width = 0
      // el.style.bottom = 0
      el.style.width = el.scrollWidth + 'px'
      el.style.height = el.scrollHeight + 'px'

      // el.style.bottom = 0
      // console.log('Directive is updated', el.clientWidth, el.clientHeight);
    },

  });
};

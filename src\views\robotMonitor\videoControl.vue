<template>
	<div class="jessibuca-control">
		<div class="left">
			<slot name="left"></slot>
		</div>
		<template v-for="control of controls">
			<div v-if="control.show" class="jessibuca-control-item" :key="control.name">
				<img
					:src="isMobile ? control.mobileUrl : control.pcUrl"
					alt=""
					:width="control.width"
					:height="control.height"
					@click.stop="control.click"
				/>
			</div>
		</template>
	</div>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue';
import other from '/@/utils/other';

type controlType = {
	name: string;
	click: () => void;
	mobileUrl: string;
	pcUrl: string;
	show: boolean;
	width: number;
	height: number;
};

const props = defineProps({
	startRecordShow: {
		type: Boolean,
		default: true,
	},
	fullScreenShow: {
		type: Boolean,
		default: true,
	},
});

const emits = defineEmits(['startRecord', 'stopRecordAndSave', 'fullscreen', 'refresh']);

// 移动端用 3x 图片
const isMobile = computed<boolean>(() => {
	return other.isMobile();
});

const controls = reactive<controlType[]>([
	{
		name: '刷新',
		click: () => {
			emits('refresh');
		},
		width: 22,
		height: 22,
		mobileUrl: other.getStaticImag('live/refresh.png'),
		pcUrl: other.getStaticImag('live/refresh.png'),
		show: true,
	},
	{
		name: '录制',
		click: () => {
			emits('startRecord');
		},
		mobileUrl: other.getStaticImag('live/mobile/luping.png'),
		pcUrl: other.getStaticImag('live/luping.png'),
		show: props.startRecordShow,
		width: 22,
		height: 16,
	},
	{
		name: '全屏',
		click: () => {
			emits('fullscreen');
		},
		mobileUrl: other.getStaticImag('live/mobile/full-screen.png'),
		pcUrl: other.getStaticImag('live/full-screen.png'),
		show: props.fullScreenShow,
		width: 20,
		height: 20,
	},
]);

const width = computed(() => {
	const showCount = controls.filter((item) => item.show).length;
	let width = 0;
	isMobile.value ? (width = showCount * 55) : (width = showCount * 50);
	return `${width}px`;
});
</script>

<style lang="scss" scoped>
/* @import url(); 引入css类 */

$width: 450px;
.jessibuca-control {
	height: 7%;
	min-height: 50px;
	width: calc(100%);
	position: absolute;
	bottom: 0px;
	right: 0px;
	background-image: linear-gradient(to bottom, transparent, black 100%);

	background-repeat: no-repeat;
	// background-position: left bottom;
	background-size: 100% 100%;
	// padding: 0 5px;
	display: flex;
	align-items: center;

	.left {
		flex: 1;
	}
	.jessibuca-control-item {
		width: 45px;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;

		img {
			object-fit: cover;
			cursor: pointer;
		}
	}
}

// @media (max-width: 768px) {
// 	/* 假设768px是切换到移动端布局的断点 */
// 	.jessibuca-control {
// 		width: 100%px;
// 		height: 25px;
// 	}
// }
</style>

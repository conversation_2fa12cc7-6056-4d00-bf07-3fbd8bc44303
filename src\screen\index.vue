<template>
	<div class="main-container">
		<LeftCon v-model="isLeftCollapsed"></LeftCon>
		<Map></Map>
		<RightCon v-model="isRightCollapsed"></RightCon>
		<div class="filter-container">
			<Select
				v-model="eventType"
				:options="animalTypesStore.screenAnimalTypeOptions"
				:clearable="false"
			/>
			<DateRange v-model="timeType" :radios="TIME_RADIO_OPTIONS" />
		</div>
	</div>
</template>

<script setup lang="ts" name="DataScreenDevice">
import { ref, provide, reactive, defineAsyncComponent, onMounted } from 'vue';
import { TIME_RADIO_OPTIONS } from '/@/utils/constants';
import { useAnimalTypesStore } from '/@/stores/animalTypes';
const LeftCon = defineAsyncComponent(() => import('./leftCon.vue'));
const RightCon = defineAsyncComponent(() => import('./rightCon.vue'));
const Map = defineAsyncComponent(() => import('./map/index.vue'));
const DateRange = defineAsyncComponent(() => import('./component/dateRange/index.vue'));
const Select = defineAsyncComponent(() => import('./component/select/index.vue'));
import { getSpecies } from '/@/api/screen';

// 使用动物类型 store
const animalTypesStore = useAnimalTypesStore();

const isLeftCollapsed = ref(false);
provide('isLeftCollapsed', isLeftCollapsed);
const isRightCollapsed = ref(false);
provide('isRightCollapsed', isRightCollapsed);
const timeType = ref<TimeType>(1);
provide('timeType', timeType);
const eventType = ref('all');
provide('eventType', eventType);

// 组件挂载时确保动物类型数据存在（智能缓存）
onMounted(async () => {
	await animalTypesStore.ensureAnimalTypes();
});
</script>

<style lang="scss">
@import '/@/screen/styles/dataScreen.scss';
</style>

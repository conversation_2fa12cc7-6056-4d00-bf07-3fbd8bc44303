<template>
	<div class="jessibucaContainer" ref="jessibucaContainer" id="jessibuca-container">
		<div v-show="jessibucaState.errorStatus" class="jessibuca-error">
			<div class="jessibuca-error-icon">
				<img src="/src/assets/video-play-error.png" alt="" />
			</div>
			<div>{{ errorStatusText }}</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onUnmounted, computed } from 'vue';

const props = withDefaults(
	defineProps<{
		playUrl: string; // 播放地址
		playBtn?: boolean; // 是否显示播放按钮
		fullscreenBtn?: boolean; // 是否显示全屏按钮
	}>(),
	{
		playBtn: true,
		fullscreenBtn: true,
	}
);

const jessibucaContainer = ref();
let jessibuca: any;
const jessibucaState = reactive({
	playing: false,
	errorStatus: 0, // 错误状态码：1 超时 2 加载出错
});
const errorStatusText = computed(() => {
	if (jessibucaState.errorStatus === 1) return '加载超时，请检查设备情况';
	if (jessibucaState.errorStatus === 2) return '加载出错，请检查设备状态';
	return '';
});

const initJessibuca = () => {
	jessibuca = new (window as any).Jessibuca({
		container: jessibucaContainer.value,
		videoBuffer: 1,
		isResize: true,
		isFullResize: false,
		isFlv: true,
		loadingText: '视频加载中，请稍候',
		heartTimeout: 3,
		loadingTimeout: 30,
		loadingTimeoutReplay: false,
		decoder: '/jessibuca/decoder.js',
		hasAudio: true,
		debug: false,
		operateBtns: {
			fullscreen: props.fullscreenBtn,
			screenshot: false,
			play: props.playBtn,
			audio: false,
			record: false,
		},
		isNotMute: true,
		useWebFullScreen: false,
		controlAutoHide: true,
	});

	jessibuca.on('play', function () {
		jessibucaState.playing = true;
	});
	jessibuca.on('pause', function () {
		jessibucaState.playing = true;
	});
	jessibuca.on('fullscreen', function (flag) {
		console.log('is webFullscreen', flag);
	});

	// 当设定的超时时间内无数据返回
	jessibuca.on('timeout', function (error: any) {
		console.log('jessibuca ==== timeout', error);
		if (error === 'loadingTimeout') {
			jessibucaState.errorStatus = 1;
		}
	});

	jessibuca.on('error', function (error: any) {
		console.log('jessibuca ==== error', error);
		jessibucaState.errorStatus = 2;
	});
};

const play = (url: string) => {
	if (jessibuca) {
		jessibuca.play(url);
	}
};

const destroy = () => {
	if (jessibuca) {
		jessibuca.destroy();
		jessibuca = null;
	}
	jessibucaState.playing = false;
	jessibucaState.errorStatus = 0;
};

// 播放地址改变
watch(
	() => props.playUrl,
	(value) => {
		destroy();
		if (value) {
			initJessibuca();
			// jessibuca初始化完成
			if (jessibuca.loaded) {
				play(value);
			} else {
				jessibuca.on('load', function () {
					play(value);
				});
			}
		}
	}
);

onUnmounted(() => {
	destroy();
});
</script>

<style lang="scss" scoped>
.jessibucaContainer {
	width: 100%;
	height: 100%;
	background-color: #161616;

	:deep(.jessibuca-controls) {
		.jessibuca-icon {
			width: 20px !important;
			height: 20px !important;
		}
	}

	.jessibuca-error {
		position: absolute;
		z-index: 20;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		width: 100%;
		height: 100%;
		pointer-events: none;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: 14px;
		text-align: center;
		&-icon {
			margin-bottom: 16px;
			img {
				width: vw(36);
			}
		}
	}
}
</style>

<template>
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		:show-close="false"
		align-center
		class="marker-window marker-window-frequency"
		modal-class="marker-window-modal marker-window-modal-frequency"
		@close="onCancelDialog"
		:style="markerWindowStyle"
		destroy-on-close
	>
		<div class="module map-custom-info-window">
			<div class="module-title">物种频次排行</div>
			<div class="icon-close" @click="onCancelDialog">
				<img src="/src/assets/sd/data-screen/close.png" alt="" />
			</div>
			<div class="info-window-cont ranks" v-loading="state.getRankListLoading">
				<div class="rank-item" v-for="(item, $index) in state.rankList" :key="$index">
					<div class="serial-num" :class="$index < 3 ? 'threeBefore' : ''">NO.{{ $index + 1 }}</div>
					<div class="info">
						<span>{{ item.speciesAlias }}</span>
						<span class="count">{{ item.speciesFrequency }}</span>
					</div>
				</div>
				<el-empty
					v-if="state.rankList.length === 0 && state.getRankListLoading === false"
					description="暂无物种频次排行数据"
				>
					<template #image>&nbsp;</template>
				</el-empty>
			</div>
		</div>
	</el-dialog>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue';
import { getDevSpeciesFrequencyRank } from '/@/api/sd/dataScreen/bird';

const state = reactive({
	dialog: {
		title: '',
		isShowDialog: false,
	},
	position: {
		x: 0,
		y: 0,
	},
	getRankListLoading: false,
	rankList: [] as SpeciesEvent[], // 物种频次排行
	markerClickCallback: null as Function | null,
});

const markerWindowStyle = computed(() => {
	return {
		left: state.position.x + 'px',
		top: state.position.y + 'px',
	};
});

interface OpenDialogParams {
	eventX: number;
	eventY: number;
	device: string;
	animalNameList?: string[];
	animalTag?: number;
	timeType: number;
}
const openDialog = async (params: OpenDialogParams, callback: Function) => {
	state.dialog.isShowDialog = true;
	state.position.x = params.eventX;
	state.position.y = params.eventY;
	state.markerClickCallback = callback;
	// 获取物种频次排行
	const query = {
		device: params.device,
		animalNameList: params.animalNameList,
		animalTag: params.animalTag,
		timeType: params.timeType,
	};
	try {
		state.getRankListLoading = true;
		const { payload } = await getDevSpeciesFrequencyRank(query);
		state.rankList = payload;
		state.getRankListLoading = false;
	} catch (e) {
		state.getRankListLoading = false;
		onCancelDialog();
	}
};

const onCancelDialog = () => {
	state.dialog.isShowDialog = false;
	state.rankList = [];
	if (typeof state.markerClickCallback === 'function') {
		state.markerClickCallback(state);
		state.markerClickCallback = null;
	}
};

defineExpose({
	openDialog,
});
</script>
<style lang="scss">
.marker-window-modal-frequency {
	.marker-window-frequency {
		width: vw(320) !important;
	}
}
</style>

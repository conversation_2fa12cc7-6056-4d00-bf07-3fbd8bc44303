// 取消http请求
import axios, { AxiosRequestConfig, Canceler } from "axios";
import { verifyPrototype } from './toolsValidate';

// 声明一个Map 用来存储http请求标识 和 取消函数
const pendingMap = new Map<string, Canceler>();

export default class AxiosCanceler {
  /**
   * @description: 添加请求
   * @param {Object} config
   * @param {boolean} [shouldCancel=true] 控制是否应该取消之前的异步请求
   */
  addPending(config: AxiosRequestConfig) {
    // 如果shouldCancel为false，则不执行任何取消操作
    if (!config?.shouldCancel) {
      return;
    }
    // 请求开始前，取消上一次请求
    this.removePending(config);
    const url = <string>config.url;
    config.cancelToken =
      new axios.CancelToken((cancel) => {
        if (!pendingMap.has(url)) {
          pendingMap.set(url, cancel);
        }
      });
  }
  /**
   * @description: 移除请求
   * @param {Object} config
   */
  removePending(config: AxiosRequestConfig) {
    const url = <string>config.url;
    if (pendingMap.has(url)) {
      const cancel = pendingMap.get(url);
      cancel && verifyPrototype(cancel, 'Function') && cancel();
      pendingMap.delete(url);
    }
  }
  /**
   * @description: 清空所有pending
   */
  removeAllPending() {
    pendingMap.forEach((cancel) => {
      cancel && verifyPrototype(cancel, 'Function') && cancel();
    });
    pendingMap.clear();
  }
}

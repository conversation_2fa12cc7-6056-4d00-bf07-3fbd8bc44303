import request from '/@/utils/request';

/**
 * @method getLives 获取直播列表
 * @method createLive 新增
 * @method updateLive 修改
 * @method deleteLive 删除
 * @method batchDeleteLive 批量删除
 * @method getRobots 机器人列表
 */

export function getLives(params?: Object) {
	return request({
		url: `/lives`,
		method: 'get',
		params,
	});
}

export function createLive(params: Object) {
	return request({
		url: `/lives`,
		method: 'post',
		data: params,
	});
}

export function updateLive(params: LiveForm) {
	return request({
		url: `/lives/${params.id}`,
		method: 'put',
		data: params,
	});
}

export function deleteLive(id: string) {
	return request({
		url: `/lives/${id}`,
		method: 'delete',
	});
}


export function batchDeleteLive(ids: string[]) {
	return request({
		url: `/lives/del`,
		method: 'delete',
		data: ids
	});
}

export function getRobots(params?: Object) {
	return request({
		url: `/robots`,
		method: 'get',
		params,
	});
}
import request from '/@/utils/request';

/**
 * @method getMonitors 获取监测编排列表
 * @method createMonitor 新增
 * @method updateMonitor 修改
 * @method deleteMonitor 删除
 * @method batchDeleteMonitor 删除
 * @method startMonitor 启动
 * @method stopMonitor 停止
 * @method checkStatus 检查监测任务状态
 */

export function getMonitors(params: Object) {
  return request({
    url: `/monitors`,
    method: 'get',
    params,
  });
}

export function createMonitor(data: Object) {
  return request({
    url: `/monitors`,
    method: 'post',
    data,
  });
}

export function updateMonitor(data: EmptyObjectType) {
  return request({
    url: `/monitors/${data.id}`,
    method: 'put',
    data,
  });
}

export function deleteMonitor(id: string) {
  return request({
    url: `/monitors/${id}`,
    method: 'delete',
  });
}

export function batchDeleteMonitor(ids: string[]) {
  return request({
    url: `/monitors/del`,
    method: 'delete',
    data: ids
  });
}

export function startMonitor(params: { id: string }) {
  return request({
    url: `/monitors/start`,
    method: 'post',
    params,
  });
}

export function stopMonitor(params: { id: string }) {
  return request({
    url: `/monitors/stop`,
    method: 'post',
    params,
  });
}

export function checkStatus() {
  return request({
    url: `/monitors/check_status`,
    method: 'post',
  });
}
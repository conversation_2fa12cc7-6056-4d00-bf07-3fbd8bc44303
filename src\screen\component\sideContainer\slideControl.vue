<template>
	<div
		class="slide-control"
		:class="[direction, { 'is-collapsed': modelValue }]"
		@click="handleClick"
	>
		<div class="slide-ear">
			<img :src="currentIcon" alt="" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import leftHideIcon from '/@/assets/sd/data-screen/left-hide.png';
import rightHideIcon from '/@/assets/sd/data-screen/right-hide.png';
import leftShowIcon from '/@/assets/sd/data-screen/left-show.png';
import rightShowIcon from '/@/assets/sd/data-screen/right-show.png';

interface Props {
	direction?: 'left' | 'right';
	modelValue: boolean;
}

const props = withDefaults(defineProps<Props>(), {
	direction: 'left',
	modelValue: false,
});

const emit = defineEmits(['update:modelValue']);

const currentIcon = computed(() => {
	if (props.direction === 'left') {
		return props.modelValue ? leftShowIcon : leftHideIcon;
	} else {
		return props.modelValue ? rightShowIcon : rightHideIcon;
	}
});

const handleClick = () => {
	emit('update:modelValue', !props.modelValue);
};
</script>

<style lang="scss" scoped>
.slide-control {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	z-index: 10;
	cursor: pointer;

	&.left {
		right: -20px;
	}

	&.right {
		left: -20px;
	}

	.slide-ear {
		width: vw(20);
		height: vw(70);
		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
}
</style>

<template>
  <div class="flex pd20">
    <!-- 详情信息 -->
    <div class="detail-container flex-auto">
      <div v-if="state.detail" class="detail-container-content">
        <el-row class="content-top" :gutter="20">
          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <el-carousel class="content-top-images" height="400px">
              <el-carousel-item v-for="(item, $index) in state.detail[`${tags['imageList']}`]" :key="item">
                <el-image
                  :src="speciesType==0 ? item : item.imageUrl"
                  fit="contain"
                  :preview-src-list="speciesPicList"
                  :initial-index="$index"
                  :preview-teleported="true"
                ></el-image>
              </el-carousel-item>
            </el-carousel>
          </el-col>

          <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
            <div class="content-top-right">
              <div class="name">{{ state.detail[`${tags['name']}`] }}</div>
              <div v-show="state.detail[`${tags['alias']}`]">别名：{{ state.detail[`${tags['alias']}`] }}</div>
              <div v-show="state.detail[`${tags['latinName']}`]">拉丁名：{{ state.detail[`${tags['latinName']}`] }}</div>
              <div v-show="state.detail.dataSource" class="origin">来源：《{{ state.detail.dataSource }}》</div>
              <div class="piece species">
                <div class="piece-title">分类系统</div>
                <div>
                  <span>纲</span>
                  {{ state.detail[`${tags['className']}`] }}
                </div>
                <div>
                  <span>目</span>
                  {{ state.detail[`${tags['order']}`] }}
                </div>
                <div>
                  <span>科</span>
                  {{ state.detail[`${tags['family']}`] }}
                </div>
                <div>
                  <span>属</span>
                  {{ state.detail[`${tags['genus']}`] }}
                </div>
                <div>
                  <span>种</span>
                  {{ state.detail[`${tags['specie']}`] }}
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <!-- modules -->
        <template v-for="item in tags['modules']">
          <div class="piece" :key="item.title"
            v-if="(!item.subField && state.detail[item.field]) || (item.subField && state.detail[item.field] && state.detail[item.field][item.subField])">
            <div class="piece-title">{{ item.title }}</div>
            <div>
              {{ item.subField ? state.detail[item.field][item.subField] : state.detail[item.field] }}
            </div>
          </div>
        </template>
        <!-- （鱼类）自定义属性 -->
        <template v-if="state.detail.detail">
          <div class="piece" v-for="(value, key) in JSON.parse(state.detail.detail)" :key="key">
            <div class="piece-title">{{ key }}</div>
            <div>{{ value || '-' }}</div>
          </div>
        </template>
      </div>
    </div>
    <!-- 已识别的物种图片（识别结果） -->
    <div class="right-content">
      <div class="first-level-title font16 mb10">近期发现</div>
      <div class="ME-list" :style="{ height: state.MEHeight + 'px' }" v-infinite-scroll="getMEList"
        :infinite-scroll-immediate="false">
        <template v-if="state.MEList.length > 0">
          <el-card class="ME-cont" v-for="(item, $index) in state.MEList" :key="item.id">
            <div class="ME-cont-image">
              <el-image
                :src="item.monitorEventDetails.length > 0 ? item.pictureUrl : item.oriPictureUrl"
                fit="cover"
                lazy
                :preview-src-list="MEPicList"
								:initial-index="$index"
              ></el-image>
            </div>
            <div class="ME-cont-other">
              <div :title="item.device.name">{{ item.device.name }}</div>
              <div class="mt5">
								GPS：{{ item.longitude && item.latitude ? `${item.longitude},${item.latitude}` : '-' }}
							</div>
              <div class="mt5">{{ item.createTime }}</div>
            </div>
          </el-card>
        </template>
        <el-empty v-else description="暂无已识别物种图片"></el-empty>
      </div>
    </div>
    
  </div>
</template>

<script setup lang='ts' name="SpeciesDetail">
import { reactive, computed, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { getDirectoryDetailById, getDirectoryDetailByName } from '/@/api/species';
import { getMonitorEvents } from '/@/api/monitorEvents';
import { useSpeciesType } from '/@/hooks/useSpeciesType';
import { Query } from './type';
import { Session } from '/@/utils/storage';

const { speciesType } = useSpeciesType();
// 数据库中定义的各物种详情字段，顺序为植物、鸟、昆虫、动物（哺乳动物/鱼类/两栖类/爬行类）
const speciesTypeTags = [
  // 植物详情需特殊处理
  {
    name: 'nameStd', // 名称
    alias: 'alias', // 别名
    latinName: 'nameLt', // 拉丁名
    className: 'className', // 纲
    order: 'order', // 目
    family: 'familyCn', // 科
    genus: 'genusCn', // 属
    specie: 'specie', // 种
    imageList: 'images', // 轮播列表
    modules: [
      {
        title: '形态特征',
        field: 'description',
        subField: ''
      },
      {
        title: '表型特征',
        field: 'info',
        subField: 'bxtz',
      },
      {
        title: '分布地区',
        field: 'info',
        subField: 'fbdq',
      },
      // {
      //   title: '花开时节',
      //   field: 'info',
      //   subField: 'hksj',
      // },
      // {
      //   title: '花语寓意',
      //   field: 'info',
      //   subField: 'hyyy',
      // },
      // {
      //   title: '价值功用',
      //   field: 'info',
      //   subField: 'jzgy',
      // },
      // {
      //   title: '名称来历',
      //   field: 'info',
      //   subField: 'mcll',
      // },
      // {
      //   title: '相关诗词',
      //   field: 'info',
      //   subField: 'xgsc',
      // },
      // {
      //   title: '养护技术',
      //   field: 'info',
      //   subField: 'yhjs',
      // },
    ],
  },
  {
    name: 'name',
    alias: 'alias',
    latinName: 'latinName',
    className: 'className',
    order: 'order',
    family: 'family',
    genus: 'genus',
    specie: 'specie',
    imageList: 'imageList',
    modules: [
      {
        title: '俗名',
        field: 'commonName',
        subField: ''
      },
      {
        title: '虹膜',
        field: 'iris',
        subField: '',
      },
      {
        title: '叫声',
        field: 'sound',
        subField: '',
      },
      {
        title: '形态特征',
        field: 'describe',
        subField: ''
      },
      {
        title: '分布地区',
        field: 'distributionRange',
        subField: '',
      },
      {
        title: '分布情况',
        field: 'distribution',
        subField: '',
      },
      {
        title: '习性',
        field: 'habit',
        subField: '',
      },
    ],
  },
  {
    name: 'name',
    alias: 'alias',
    latinName: 'latinName',
    className: 'className',
    order: 'order',
    family: 'family',
    genus: 'genus',
    specie: 'specie',
    imageList: 'imageList',
    modules: [
      {
        title: '形态特征',
        field: 'describe',
        subField: '',
      },
    ],
  },
  {
    name: 'name',
    alias: 'alias',
    latinName: 'latinName',
    className: 'className',
    order: 'order',
    family: 'family',
    genus: 'genus',
    specie: 'specie',
    imageList: 'imageList',
    modules: [
      {
        title: '形态特征',
        field: 'morphologicalDescription',
        subField: '',
      },
      {
        title: '鉴别特征',
        field: 'differentiateCharacteristic',
        subField: ''
      },
      {
        title: '分布地区',
        field: 'distribution',
        subField: '',
      },
    ],
  },
];
const tags = computed(() => {
  return speciesType.value > 2 ? speciesTypeTags[3] : speciesTypeTags[speciesType.value];
});

const state = reactive({
  detail: <EmptyObjectType | null>null,
  page: 0,
  pageSize: 10,
  MEHeight: 0,
  MELoading: false,
  MEList: [] as MonitorEventRow[],
  MEFinished: false,
});

onMounted(() => {
  initSpeciesDetail();
});
const initSpeciesDetail = async () => {
  const query: Query = Session.get('speciesDetailQuery') ? JSON.parse(Session.get('speciesDetailQuery') as string) : {};
  if (query.id !== undefined) {
    const { payload } = await getDirectoryDetailById(speciesType.value, query.id);
    if (!payload) {
      ElMessage.error(`未查询到相应百科信息`);
    }
    state.detail = payload;
    nextTick(() => {
      getMEList();
    });
  } else {
    const { payload } = await getDirectoryDetailByName(speciesType.value, {
      name: query.name as string,
    });
    if (!payload || !payload.result) {
      ElMessage.error(`未查询到${name}百科信息`);
    }
    state.detail = payload && payload.result;
  }
};
const speciesPicList = computed(() => {
	return state.detail && state.detail[`${tags.value['imageList']}`].map((item: any) => (speciesType.value==0 ? item : item.imageUrl));
});
const getMEList = async () => {
  const detailConEle = document.querySelector('.detail-container-content');
  state.MEHeight = detailConEle?.clientHeight as number;
  if (!state.detail || state.MEFinished === true) return;
  const query = {
    page: state.page,
    size: state.pageSize,
    noResult: 0,
    // name: '', // 物种名称模糊匹配
    speciesName: state.detail[`${tags.value['name']}`], // 物种名称全匹配
    sort: 'createTime,desc',
  };
  state.MELoading = true;
  const { payload } = await getMonitorEvents(query);
  state.MEList = state.MEList.concat(payload.content);
  state.MELoading = false;
  if (payload.last) {
    state.MEFinished = true;
    return;
  }
  state.page += 1;
};
const MEPicList = computed(() => {
	return state.MEList.map((item) => (item.pictureUrl || item.oriPictureUrl));
})
</script>

<style lang='scss' scoped>
.detail-container {
  .content-top {
    display: flex;

    .content-top-images {
      width: 100%;

      // border: 1px solid #08677a;
      .el-carousel__item {
        .el-image {
          width: 100%;
          height: 100%;
          // object-fit: contain;
          // aspect-ratio: 1/1;
        }
      }
    }

    .content-top-right {
      font-size: 15px;
      line-height: 30px;

      .name {
        font-size: 20px;
        font-weight: bolder;
      }

      .origin {
        font-size: 12px;
        color: #bbb;
      }

      .species {
        font-size: 14px;
      }

      span {
        background-color: var(--el-color-primary);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        margin-right: 4px;
      }
    }
  }

  .piece {
    margin: 20px 0;

    .piece-title {
      font-size: 16px;
      line-height: 30px;
      margin-bottom: 4px;

      &::before {
        display: inline-block;
        content: "";
        width: 4px;
        height: 16px;
        margin-right: 6px;
        background: var(--el-color-primary);
        position: relative;
        left: 0;
        top: 2px;
      }
    }

    div {
      line-height: 30px;
      letter-spacing: 1px;
    }
  }
}

.right-content {
  width: 360px;
  border-left: 1px solid #eee;
  padding-left: 20px;
  margin-left: 20px;
}

.ME-list {
  min-height: calc(100vh - 146px);
  overflow-y: auto;

  // &::-webkit-scrollbar {
  // 	width: 0;
  // }
  :deep(.ME-cont > .el-card__body) {
    padding: 0;
    border-radius: 2px;
    .ME-cont-image {
      padding-top: 75%;
      position: relative;
      .el-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
    .ME-cont-other {
      padding: 10px;
    }
  }

  .ME-cont+.ME-cont {
    margin-top: 15px;
  }
}
</style>
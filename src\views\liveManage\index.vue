<template>
  <div class="layout-pd">
    <ViewSearch @onRefreshData="onRefresh">
      <template #searchFilters>
        <el-form-item label="名称">
          <el-input v-model="searchOptions.filter.name" placeholder="请输入名称" clearable>
						<template #suffix>
							<el-icon><ele-Search /></el-icon>
						</template>
					</el-input>
        </el-form-item>
        <!-- <el-form-item label="类型">
					<el-select v-model="searchOptions.filter.type" placeholder="请选择" clearable>
            <el-option :value="1" label="机器人"></el-option>
            <el-option :value="2" label="摄像头"></el-option>
          </el-select>
				</el-form-item> -->
      </template>

      <template #searchBtns>
        <div v-auths="['*:*:*', 'lives:*:*']">
          <el-button type="primary" @click="onCreate">
            <template #icon>
              <el-icon><ele-Plus /></el-icon>
            </template>
            新增
          </el-button>
          <el-button type="success" :disabled="tableRef?.selectRows.length !== 1"
            @click="onUpdateRow(null)">
            <template #icon>
              <el-icon><ele-Edit /></el-icon>
            </template>
            修改
          </el-button>
          <el-button type="danger" :disabled="tableRef?.selectRows.length === 0"
            @click="onBatchDelete">
            <template #icon>
              <el-icon><ele-Delete /></el-icon>
            </template>
            删除
          </el-button>
        </div>
      </template>
    </ViewSearch>

    <Table v-bind="tableOptions" ref="tableRef" @pageChange="onPageChange">
      <template #type="{ row }">
        {{ row.type === 1 ? '巡护设备' : '摄像头' }}
      </template>
      <template #ID="{ row }">
        {{ row.type === 1 ? row.robotId : '-' }}
      </template>
      <template #operate="{ row }">
        <el-button size="small" text type="primary" @click="onUpdateRow(row)">
          <el-icon><ele-EditPen /></el-icon>
          修改
        </el-button>
        <el-button size="small" text type="primary" @click="onDelRow(row.id)">
          <el-icon><ele-Delete /></el-icon>
          删除
        </el-button>
      </template>
    </Table>
    <CreateDialog ref="createDialogRef" @refresh="onRefresh"></CreateDialog>
  </div>
</template>

<script setup lang="ts" name="LiveManage">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getLives, deleteLive, batchDeleteLive } from '/@/api/lives';
import { AUTHS } from '/@/directive/authDirective';

// 引入异步组件
const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));
const CreateDialog = defineAsyncComponent(() => import('./dialog.vue'));

const tableRef = ref<InstanceType<typeof Table>>();
const searchOptions = reactive({
  filter: {
    name: '',
    type: '',
    sort: 'startTime,desc',
  },
});
const tableOptions: GlobalTableOptions<LiveRow> = reactive({
  data: [],
  header: [
    {
      title: '名称',
      key: 'name',
      isCheck: true,
    },
    {
      title: '设备类型',
      key: 'type',
      isCheck: true,
    },
    {
      title: '设备ID',
      key: 'ID',
      isCheck: true,
    },
    {
      title: '观看次数',
      key: 'viewCount',
      isCheck: true,
    },
    {
      title: '开始时间',
      key: 'startTime',
      isCheck: true,
    },
    {
      title: '结束时间',
      key: 'endTime',
      isCheck: true,
    },
  ],
  config: {
    loading: true,
    isSelection: true,
    isSerialNo: true,
    isOperate: AUTHS(['*:*:*', 'lives:*:*']),
    operateWidth: 150, // 操作列宽
    total: 0, // 总条数
  },
  pageParams: {
    page: 1,
    size: 10,
  },
});

onMounted(() => {
  initTableData();
});


const initTableData = async () => {
  tableOptions.config.loading = true;
  try {
    const query: any = {
      page: tableOptions.pageParams?.page && tableOptions.pageParams.page - 1,
      size: tableOptions.pageParams?.size,
      ...searchOptions.filter,
    };
    const { payload } = await getLives(query)
    tableOptions.config.loading = false;
    tableOptions.data = payload.content;
    tableOptions.config.total = payload.totalElements;
  } catch (e) {
    tableOptions.config.loading = false;
    tableOptions.config.total = 0;
    tableOptions.data = [];
  }
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
  if (resetFilter) {
    searchOptions.filter.name = '';
    searchOptions.filter.type = '';
  }
  if (resetPage) {
    tableRef.value?.pageReset();
  } else {
    initTableData();
  }
  tableRef.value?.clearSelection();
};

const onPageChange = async (page: { pageNum: number; pageSize: number; }) => {
  if (tableOptions.pageParams) {
    tableOptions.pageParams.page = page.pageNum;
    tableOptions.pageParams.size = page.pageSize;
  }
  initTableData();
};

const onBatchDelete = () => {
  ElMessageBox({
    title: '提示',
    message: '此操作将永久删除，是否继续?',
    type: 'warning',
    showCancelButton: true,
  }).then(async () => {
    const ids = tableRef?.value?.selectRows.map((item) => item.id) as string[];
    await batchDeleteLive(ids);
    ElMessage.success('删除成功');
    onRefresh();
  })
    .catch(() => { })
}

const onDelRow = (rowId: string) => {
  ElMessageBox({
    title: '提示',
    message: '此操作将永久删除，是否继续?',
    type: 'warning',
    showCancelButton: true,
  }).then(async () => {
    await deleteLive(rowId);
    ElMessage.success('删除成功');
    onRefresh();
  })
    .catch(() => { })
};

const createDialogRef = ref<InstanceType<typeof CreateDialog>>()
const onCreate = () => {
  createDialogRef.value?.openDialog();
};
const onUpdateRow = (row: LiveRow | null) => {
  if (!row) {
    row = tableOptions.data.find((item) => item.id === tableRef?.value?.selectRows[0].id) as LiveRow;
  }
  createDialogRef.value?.openDialog(row);
};
</script>

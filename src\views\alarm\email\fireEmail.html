<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>防火告警电子邮件</title>
</head>
<body>
  <div
    style="
      max-width: 700px;
      font-size: 14px;
      border: 1px solid #e0e0e0;
      color: #111;
      font-family: Helvetica, 'Microsoft YaHei', sans-serif;
      box-sizing: border-box;
      margin: 30px auto;
    "
  >
    <table style="width: 100%; padding: 0; background-color: #ff7700;">
      <tbody>
        <tr>
          <td style="padding: 0 8%; color: #fff; font-size: 24px; line-height: 60px; letter-spacing: 2px;" th:text="${alarmType}">
            高温告警（告警类型/动态）
          </td>
        </tr>
      </tbody>
    </table>
    <div style="padding: 4% 8%;">
      <div style="font-weight: bold; line-height: 28px;">尊敬的客户，您好：</div>
      <div style="line-height: 28px;" th:text="${message}">在2024年9月30日星期一的下午1点30分40秒，生物多样性监测系统中的防火设备检测到了高温告警，请及时确认告警信息，采取必要的防范措施，减少潜在的风险和损失。</div>
      <!-- device -->
      <div style="padding: 24px 50px; border: 1px solid #c9d5e8; border-radius: 8px; color: #687282; margin: 12px 0;">
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
          <div style="flex-shrink: 0; margin-right: 10px;">告警时间</div>
          <div style="flex: 1; text-align:right; word-break: break-all; text-align: right; font-weight: bold;" th:text="${time}">2024-09-30 13:30:40</div>
        </div>
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
          <div style="flex-shrink: 0; margin-right: 10px;">设备编号</div>
          <div style="flex: 1; text-align:right; word-break: break-all; text-align: right; font-weight: bold;" th:text="${num}">AK1785644</div>
        </div>
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
          <div style="flex-shrink: 0; margin-right: 10px;">设备IP</div>
          <div style="flex: 1; text-align:right; word-break: break-all; text-align: right; font-weight: bold;" th:text="${ip}">***********</div>
        </div>
        <div style="display: flex; align-items: center; margin-bottom: 15px;">
          <div style="flex-shrink: 0; margin-right: 10px;">设备水平角</div>
          <div style="flex: 1; text-align:right; word-break: break-all; text-align: right; font-weight: bold;" th:text="${anglex}">233.30003</div>
        </div>
        <div style="display: flex; align-items: center;">
          <div style="flex-shrink: 0; margin-right: 10px;">设备俯仰角</div>
          <div style="flex: 1; text-align:right; word-break: break-all; text-align: right; font-weight: bold;" th:text="${angley}">4.68</div>
        </div>
      </div>
      <!-- coordinates -->
      <table>
        <tbody>
          <tr>
            <td>
              <img style="width: 24px;" src="https://img1.baidu.com/it/u=3644604479,3696224535&fm=253&fmt=auto&app=120&f=JPEG?w=626&h=626" alt="">
            </td>
            <td style="font-weight: bold; line-height: 28px;">告警点位列表</td>
          </tr>
        </tbody>
      </table>
      <div style="padding: 24px; border: 1px solid #c9d5e8; border-radius: 8px; color: #687282; margin: 12px 0; line-height: 30px; text-align: center;">
        <div style="display: flex; font-weight: bold;">
          <div style="flex: 1;">点位</div>
          <div style="flex: 1;">经度</div>
          <div style="flex: 1;">纬度</div>
          <div style="flex: 1;">高度</div>
        </div>
        <div style="display: flex; text-align: center;" th:each="location:${locations}">
          <div style="flex: 1;" th:text="'点位' + ${locationStat.count}">点位1</div>
          <div style="flex: 1;" th:text="${location.getLit()}">117.2000</div>
          <div style="flex: 1;" th:text="${location.getLat()}">34.00002</div>
          <div style="flex: 1;" th:text="${location.getHeight()}">118</div>
        </div>
      </div>
      <!-- imgs -->
      <table>
        <tbody>
          <tr>
            <td>
              <img style="width: 24px;" src="https://img1.baidu.com/it/u=3644604479,3696224535&fm=253&fmt=auto&app=120&f=JPEG?w=626&h=626" alt="">
            </td>
            <td style="font-weight: bold; line-height: 28px;">告警图片</td>
          </tr>
        </tbody>
      </table>
      <div class="img" style="margin: 12px 0; text-align: center;">
        <img style="width: 80%;" th:src="${pic}" alt="">
      </div>
    </div>
  </div>
</body>
</html>
interface SocketCallback {
  (data: any): void;
}

export default function createSocket(url: string, cb: SocketCallback) {
  let ws: WebSocket | undefined;
  let remainPayload: any = {
    type: 'PushSwitch',
    data: {
      FaceCapturePushSwitch: 0,  // 人脸抓拍推送开关
      RobotSituationPushSwitch: 0, // 机器人概览推送开关
      RobotListPushSwitch: 0, // 机器人列表推送开关
      LineInfoPushSwitch: 0, // 线路信息推送开关
      ChannelListPushSwitch: 0, // 机器人通道列表推送开关
      RobotStatusPushSwitch: 1, // 机器人状态信息推送开关
      AlarmInfoPushSwitch: 1,  // 告警推送开关
      CoordinatePushSwitch: 1, // 机器人坐标推送开关
    }
  };

  const isConnected = () => ws && ws.readyState === WebSocket.OPEN;

  const connect = async () => {
    return new Promise<void>(resolve => {
      ws = new WebSocket(url);

      ws.onopen = async e => {
        console.info('ws open 连接成功', e);
        ws!.send(JSON.stringify({ "data": 575, "type": "DeviceChange" }))
        ws!.send(JSON.stringify(remainPayload));
        resolve();
      };

      ws.onmessage = e => {
        if (cb) {
          cb(JSON.parse(e.data));
        }
      };

      ws.onerror = e => {
        console.error('ws error', e);
      };
    });
  };

  const send = async (payload: any) => {
    if (!payload) return;
    if (isConnected()) {
      ws!.send(JSON.stringify(payload));
    } else {
      await connect(); // 尝试重新连接
      if (isConnected()) {
        ws!.send(JSON.stringify(payload));
      }
    }
  };

  const close = () => {
    if (ws) {
      console.log('ws::销毁');
      ws.close();
      ws = undefined;
    }
  };

  // 初始连接
  connect();

  return { send, close };
}

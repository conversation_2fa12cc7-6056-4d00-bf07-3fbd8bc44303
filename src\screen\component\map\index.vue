<template>
	<el-amap
		:center="center"
		:zoom="zoom"
		mapStyle="amap://styles/blue"
		view-mode="3D"
		:pitch="42"
		@init="initMap"
	>
	</el-amap>
</template>

<script setup lang="ts">
import { ref, shallowRef, reactive, inject, watch, onUnmounted, nextTick } from 'vue';
import { ElAmap } from '@vuemap/vue-amap';
let map = shallowRef<AMap.Map>(); // 地图实例
const zoom = ref(12);
const center = ref([121.59996, 31.197646]);

const initMap = (instance: AMap.Map) => {
	map.value = instance;
};
</script>

<style lang="scss" scoped></style>

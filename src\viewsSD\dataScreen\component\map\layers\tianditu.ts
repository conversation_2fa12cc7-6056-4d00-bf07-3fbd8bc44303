import TileLayer from 'ol/layer/Tile.js';
// import XYZ from 'ol/source/XYZ.js';
import WMTS from 'ol/source/WMTS.js';
import WMTSTileGrid from 'ol/tilegrid/WMTS.js';
import { getWidth, getTopLeft } from 'ol/extent';
import { get } from 'ol/proj';
import { customTileLoadFun } from '../utils';

type MapColor = 'gray' | 'blue' | 'darkgreen' | 'black';

/**
 * 天地图图层
 * 鲁朗天地图账号 `<EMAIL>` 密码 `Robusoft2023`
 * 天地图密钥：fce239d0ee1d69e2c64ad2882dc10d6c
 */
export const tiandituKey = 'fce239d0ee1d69e2c64ad2882dc10d6c';

const projection = 'EPSG:4326';
const projectionExtent: any = get(projection)?.getExtent();
const size = getWidth(projectionExtent) / 256;
const resolutions: number[] = new Array(19);
const matrixIds: string[] = new Array(19);
for (let z = 0; z < 19; ++z) {
  resolutions[z] = size / Math.pow(2, z);
  matrixIds[z] = z.toString();
}

/**
 * 生成地图资源
 * @param layer vec：矢量  cva：矢量标注  img：影像  cia：影像标注  ter：地形  cta：地形标注
 * @param matrixSet c: 经纬度投影  w: 墨卡托投影
 * @param mapColor 图层颜色
 */
const layerSource = (layer: string, matrixSet: string, mapColor?: MapColor) => {
  return new WMTS({
    projection: projection, // 数据的投影坐标系
    url: `http://t{0-7}.tianditu.gov.cn/${layer}_${matrixSet}/wmts?tk=${tiandituKey}`,
    layer,
    matrixSet, // 投影坐标系设置矩阵
    style: 'default',
    crossOrigin: 'anonymous', 
    format: 'tiles', // 图片格式
    wrapX: true,
    tileGrid: new WMTSTileGrid({ // 瓦片网格对象
      origin: getTopLeft(projectionExtent), // 原点（左上角）
      resolutions: resolutions, // 分辨率数组
      matrixIds: matrixIds, // 矩阵标识列表，与地图级数保持一致
    }),
    tileLoadFunction: (imageTile: any, src: string) => {
      if (mapColor) {
        customTileLoadFun(imageTile, src, mapColor);
      } else {
        imageTile.getImage().src = src;
      }
    },
  });
  // return new XYZ({
  //   url: `http://t0.tianditu.com/DataServer?T=${layer}_${matrixSet}&x={x}&y={y}&l={z}&tk=${tiandituKey}`,
  //   projection,
  //   wrapX: false,
  // });
};

export default [
  // 天地图矢量图层
  new TileLayer({
    visible: true,
    source: layerSource('vec', 'c', 'darkgreen'),
  }),
  // 天地图矢量注记图层
  new TileLayer({
    visible: true,
    source: layerSource('cva', 'c', 'darkgreen'),
  })
];

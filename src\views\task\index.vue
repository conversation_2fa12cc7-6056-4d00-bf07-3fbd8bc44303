<template>
	<div class="task-container fullScreen">
		<img class="fanhui" src="/src/assets/live/fanhui.png" alt="返回" @click="goBack" />
		<div class="title">
			<img src="/src/assets/fullScreen/title-name.png" alt="" draggable="false" />
		</div>
		<div class="mian">
			<div class="task-box">
				<img class="title-img" src="/src/assets/task/task.png" alt="" />
				<div class="task-bottom">
					<!-- 任务时间 -->
					<div class="task-time">
						<div class="select-title robot-title">
							<el-select
								v-model="deviceId"
								placeholder="筛选"
								size="small"
								style="width: 100%"
								:fit-input-width="true"
								:teleported="false"
								placement="bottom-start"
								@change="handelRobotChange"
							>
								<el-option label="全部" :value="-1" />
								<el-option
									v-for="(key, index) in robotsInfo.keys()"
									:label="robotsInfo.get(key)?.robotName"
									:value="key"
								/>
							</el-select>
						</div>
						<div class="select-title date-title">
							<el-date-picker
								v-model="state.timeData.filter.beginDate"
								type="date"
								placeholder="点击选择日期"
								:teleported="false"
								format="YYYY-MM-DD"
								value-format="YYYY-MM-DD HH:mm:ss"
								:editable="false"
								:clearable="false"
								placement="bottom-start"
								@change="handelTimeChange(true, true)"
								@visibleChange=""
								:suffix-icon="customPrefix"
							>
							</el-date-picker>
						</div>
						<div
							class="select-option scrollbar-lg"
							ref="swiperRef"
							v-infinite-scroll="handelTimeChange"
							:infinite-scroll-immediate="false"
							:infinite-scroll-disabled="disabled"
							:infinite-scroll-distance="17"
						>
							<div
								class="time-item"
								v-for="time in state.timeData.data"
								:class="state.timeData.activeId === time.id ? 'active-time-item' : ''"
								@click="handelTimeClick(time)"
							>
								<span class="time-item-name"
									>{{ time.patrolPlanName || '-' }} ({{ time.robotName }})</span
								>
								<span class="task-title-time"
									><i></i>开始时间：{{
										time.beginTime ? extractTimePart(time.beginTime, 'HH:mm:ss') : '-'
									}}</span
								>
								<span class="task-title-time"
									><i></i>结束时间：{{
										time.endTime ? extractTimePart(time.endTime, 'HH:mm:ss') : '-'
									}}</span
								>
							</div>

							<p
								v-if="state.timeData.loading"
								class="text-loding"
								:class="{ mt80: state.timeData.pageParams.page === 0 }"
								style="color: #22d69f"
							>
								加载中...
							</p>
							<p
								v-else-if="noMore"
								class="text-loding"
								:class="{ mt80: state.timeData.total === 0 }"
							>
								{{ state.timeData.total ? '没有更多了' : '暂无数据' }}
							</p>
						</div>
					</div>
					<div class="task-view">
						<div class="task-title">
							<span class="task-name">{{ state.timeData.patrolPlanName || '-' }}</span>
							<span class="task-title-time"
								><i></i>开始时间：{{ state.timeData.beginTime || '-' }}</span
							>
							<span class="task-title-time"
								><i></i>结束时间：{{ state.timeData.endTime || '-' }}</span
							>
						</div>
						<div class="task-search">
							<div class="search-input">
								<span class="search-item">
									事件类型：
									<el-select
										v-model="state.tableData.filter.eventTypes"
										placeholder="点击选择事件类型"
										multiple
										collapse-tags
										clearable
										:teleported="false"
									>
										<el-option
											v-for="item in state.METypeOptions"
											:key="item.id"
											:label="item.name"
											:value="item.eventType"
										>
										</el-option> </el-select
								></span>
								<span>
									<span class="button-round" @click="onRefresh(true, false)">搜索</span>
									<span
										class="button-round"
										style="margin-left: 30px"
										@click="onRefresh(true, true)"
										>重置</span
									>
								</span>
							</div>
						</div>
						<div class="table-box scrollbar-lg" v-loading="state.tableData.loading">
							<template v-if="state.tableData.data.length > 0">
								<div
									class="atlas-item"
									v-for="(item, $index) in state.tableData.data"
									:key="item.id"
								>
									<!-- <el-checkbox class="checkbox" v-model="item.checked" /> -->
									<div class="atlas-type">{{ item.parentEventTypeName }}</div>
									<div class="thumb-rec">
										<el-image
											class="thumbnail"
											:src="item.pictureUrl || item.oriPictureThumbnailUrl"
											:preview-src-list="perviewPicList"
											:initial-index="$index"
											fit="contain"
											lazy
											@show="handleImageShow(item)"
											@close="handleImageClose"
											@switch="handleImageSwitch"
										>
											<template #placeholder>
												<div class="image-placeholder">
													<el-icon class="is-loading">
														<ele-Loading />
													</el-icon>
												</div>
											</template>
											<template #error>
												<div class="load-error">
													<img
														class="small-img-error"
														src="/src/assets/fullScreen/small-load-error.png"
														title="加载失败"
														alt=""
													/>
												</div>
											</template>
										</el-image>
									</div>
									<div class="other-content">
										<span
											v-if="item?.monitorEventDetails && item.monitorEventDetails.length"
											v-for="res in item.monitorEventDetails"
											:key="res.recResult"
										>
											{{ item.pointName }}
											{{ res.recResult }}
											<template v-if="item.eventType < 2">
												{{ res.recResultCnt && `（${res.recResultCnt}）` }}
											</template>
										</span>
										<span>{{ extractTimePart(item.recTime, 'HH:mm:ss') }}</span>
									</div>
								</div>
								<el-pagination
									class="el-pagination_largeScreen mt10"
									v-model:current-page="state.tableData.pageParams.page"
									:page-sizes="[6, 12, 24]"
									background
									v-model:page-size="state.tableData.pageParams.size"
									layout="total, sizes, prev, pager, next, jumper"
									:total="state.tableData.total"
									:teleported="false"
									@size-change="onHandleSizeChange"
									@current-change="onHandleCurrentChange"
								>
								</el-pagination>
							</template>

							<div v-else class="text-loding" style="width: 100%; height: 20px; margin-top: 200px">
								暂无数据
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="statistics-box">
				<div class="title-img"></div>
				<!-- <img class="title-img" src="/src/assets/task/statistics.png" alt="" /> -->
				<div class="statistics-main">
					<div class="event-statistics">
						<!-- <span class="event-statistics-title"> 任务汇总 </span> -->
						<div class="task">
							<div class="event-types">
								<template v-for="(item, index) in eventTypeList" :key="item.eventType">
									<div class="event-type" :style="{ backgroundImage: `url(${item[1].bgimg})` }">
										<div class="event-type-name">{{ item[1].eventTypeName }}</div>
										<div class="event-type-count">{{ item[1].alarmCount }}</div>
									</div>
								</template>
							</div>
							<template v-for="(item, index) in taskInfo.eventTypeStatus" :key="item.name">
								<div
									class="task-bottom"
									:class="item.eventTypeName === '公园环境' ? 'trash-full' : ''"
								>
									<div class="task-bottom-title">
										{{ item.eventTypeName }}
									</div>
									<div class="task-bottom-status">
										<span><i></i>{{ item.normal.name }}: {{ item.normal.count }}</span>
										<span><i></i>{{ item.alarm.name }}: {{ item.alarm.count }}</span>
										<span><i></i>{{ item.omit.name }}: {{ item.omit.count }}</span>
									</div>
								</div>
							</template>
						</div>
					</div>

					<div class="echarts-stat">
						<!-- <span class="event-statistics-title"> 事件类型 </span> -->
						<Echarts class="echarts-item" ref="pieStatisticsRef" id="pie-statistics" />
					</div>

					<div class="points-box">
						<!-- <span class="event-statistics-title"> 巡检点位列表 </span> -->
						<!-- <img src="/src/assets/task/xunjian-title.png" alt="" /> -->
						<div class="pionts-title pionts-item">
							<span>巡检地点</span>
							<span>巡检时间</span>
							<span>巡检结果</span>
						</div>
						<div class="point-list scrollbar-lg" v-loading="state.tasksPoints.loading">
							<div
								class="pionts-item"
								v-if="state.tasksPoints.data.length"
								v-for="(item, index) in state.tasksPoints.data"
								:key="index"
							>
								<span>{{ item.pointName }}</span>
								<span>
									{{
										item.status !== 0
											? extractTimePart(item.monitorEventFiles[0].recTime, 'HH:mm:ss')
											: '-'
									}}
								</span>
								<span
									><span :style="{ borderColor: getPointColor(item.status) }">{{
										getPointLabel(item)
									}}</span></span
								>
							</div>
							<div v-else class="text-loding" style="width: 100%; height: 20px; margin-top: 30px">
								暂无数据
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="footer">
			<img src="/src/assets/fullScreen/footer-bg.png" alt="底部" />
		</div>
		<div v-if="dragData.show" v-drag class="viewer-remark">
			{{ dragData.remark }}
		</div>
	</div>
</template>

<script lang="ts" setup>
import { h, ref, reactive, onMounted, onUnmounted, nextTick, computed, shallowRef } from 'vue';
import { getPatrolTasks, getTasksPoints, getLastTask } from '/@/api/home';
import { getMonitorFilesEvents, getMonitorEventTypes } from '/@/api/monitorEvents';
import { useRouter, useRoute } from 'vue-router';
import { NextLoading } from '/@/utils/loading';
import other from '/@/utils/other';
import Echarts from '/@/components/echarts/echarts.vue';
import { liveInfo } from '/@/stores/fullScreen';
import { storeToRefs } from 'pinia';
import { extractTimePart } from '/@/utils/formatTime';
const liveStore = liveInfo();
const { robotsInfo } = storeToRefs(liveStore);
const router = useRouter();
const route = useRoute();
const deviceId = ref<number>(Number(route.query.deviceId));
const robotSn = ref(route.query.robotSn as unknown as string);
const customPrefix = shallowRef({
	render() {
		return h('p', 'pre');
	},
});
const replaceTimeWithZero = (dateTimeString: string) => {
	const spaceIndex = dateTimeString.indexOf(' ');
	const newDateTimeString = dateTimeString.substring(0, spaceIndex) + ' 00:00:00';
	return newDateTimeString;
};
const intraday = replaceTimeWithZero(route.query.beginTime as unknown as string);

const state = reactive<ViewBaseState<MonitorEventRow>>({
	METypeOptions: [],
	tableData: {
		filter: {
			eventTypes: [], // eventType：0鸟类 1动物
			noResult: 0,
			beginDate: '',
		},
		data: [],
		loading: false,
		pageParams: {
			page: 1,
			size: 6,
		},
		total: 0,
	},

	timeData: {
		filter: {
			beginDate: intraday,
		},
		patrolPlanName: '',
		beginTime: '',
		endTime: '',
		data: [],
		loading: false,
		pageParams: {
			page: -1,
			size: 6,
		},
		total: 0,
		activeId: '',
	},
	tasksPoints: {
		data: [],
		loading: false,
	},
	selectAll: false,
});

// 左侧时间
// 没有数据了
const noMore = computed(() => {
	return state.timeData.pageParams.page * state.timeData.pageParams.size >= state.timeData.total;
});
// 禁用下拉加载
const disabled = computed(() => {
	return state.timeData.loading || noMore.value;
});

const handelRobotChange = () => {
	handelTimeChange(true, true);
};

const handelTimeChange = async (timeChange = false, once = false) => {
	return new Promise(async (resolve) => {
		state.timeData.loading = true;

		if (once) {
			state.tableData.loading = true;
		}
		if (timeChange) {
			state.timeData.data = [];
			state.timeData.pageParams.page = -1;
			state.tableData.pageParams.page = 1;
			state.tableData.pageParams.size = 6;
			state.timeData.activeId = '';
			// state.tableData.filter.eventTypes = [];
			state.tasksPoints.data = [];
		}
		state.timeData.pageParams.page += 1;

		const query = {
			page: state.timeData.pageParams.page,
			size: state.timeData.pageParams.size,
			deviceId: deviceId.value === -1 ? null : deviceId.value,
			...state.timeData.filter,
		};

		console.log(query.deviceId);
		const { payload } = await getPatrolTasks(query);

		if (payload.content.length > 0) {
			const { id, patrolPlanName, beginTime, endTime, deviceId } = payload.content[0];

			if (once) {
				state.timeData.activeId = id;
				state.tableData.filter.startTime = beginTime;
				state.tableData.filter.endTime = endTime;
				state.timeData.patrolPlanName = patrolPlanName;
				state.timeData.beginTime = beginTime;
				state.timeData.endTime = endTime;
				getTableData();
				getTaskInfo(deviceId);
				getPoints(state.timeData.activeId);
			}
		} else {
			if (once) {
				state.timeData.activeId = '';
				state.tableData.data = [];
				state.tableData.filter.startTime = '';
				state.tableData.total = 0;
				state.tableData.filter.endTime = '';
				state.timeData.patrolPlanName = '';
				state.timeData.beginTime = '';
				state.timeData.endTime = '';
				handleOverviewStat();
			}
		}

		state.timeData.data.push(...payload.content);
		state.timeData.total = payload.totalElements;

		console.log('total', state.timeData.total);
		state.tableData.loading = false;
		state.timeData.loading = false;
		resolve(payload);
	});
};

const getPointColor = (status: number) => {
	switch (status) {
		case 0:
			return 'rgba(255, 209, 57, 1)';
		case 1:
			return 'rgba(71, 194, 132, 1)';
		case 2:
			return 'rgba(255, 0, 0, 1)';
		default:
			return '#68cfad';
	}
};

const getPointLabel = (item: any) => {
	let label = '';
	if (item.status === 0) {
		label = '漏检';
	} else if (item.status === 1) {
		label = '正常';
	} else if (item.eventType === 17) {
		label = '垃圾桶满溢';
	} else if (item.eventType === 3) {
		label = '有病虫害';
	} else {
		label = '路灯故障';
	}
	return label;
};
const getPoints = async (taskId: string) => {
	try {
		state.tasksPoints.data = [];
		state.tasksPoints.loading = true;
		const query = {
			taskId,
		};
		const { payload } = await getTasksPoints(query);

		state.tasksPoints.data = payload;
		state.tasksPoints.loading = false;
	} catch (error) {
		state.tasksPoints.loading = false;
	}
};

const handelTimeClick = (_item: any) => {
	console.log('handelTimeClick', _item);
	robotSn.value = _item.robotSn;
	state.timeData.activeId = _item.id;
	state.timeData.patrolPlanName = _item.patrolPlanName;
	state.timeData.beginTime = _item.beginTime;
	state.timeData.endTime = _item.endTime;
	state.tableData.pageParams.page = 1;
	state.tableData.pageParams.size = 6;
	// state.tableData.filter.eventTypes = [];
	state.tableData.filter.startTime = _item.beginTime;
	state.tableData.filter.endTime = _item.endTime;
	getTableData();
	getTaskInfo(_item.deviceId);
	getPoints(_item.id);
};

const eventTypeKeys = [2, 1, 0, 3];
const eventTypeObjects = new Map();
eventTypeKeys.forEach((item) => {
	eventTypeObjects.set(item, {
		eventTypeName: '',
		eventType: item,
		alarmCount: 0,
		bgimg: other.getStaticImag(`fullScreen/task/${item}.png`),
	});
});

const eventTypeList = computed(() => Array.from(eventTypeObjects.entries()));
const taskInfo = reactive({
	patrolPlanName: '',
	beginTime: '',
	endTime: '-',
	eventTypeList: eventTypeObjects,
	eventTypeStatus: {
		4: {
			eventTypeName: '公园设施',
			eventType: 4,
			normal: {
				name: '正常',
				count: 0,
			},
			alarm: {
				name: '路灯故障',
				count: 0,
			},
			omit: {
				name: '漏检',
				count: 0,
			},
			bgimg: other.getStaticImag('fullScreen/anfang.png'),
		},
		5: {
			eventTypeName: '生物环境',
			eventType: 5,
			normal: {
				name: '正常',
				count: 0,
			},
			alarm: {
				name: '垃圾桶满溢',
				count: 0,
			},
			omit: {
				name: '漏检',
				count: 0,
			},
			bgimg: other.getStaticImag('fullScreen/duoyangxing.png'),
		},
	} as EmptyObjectType,
});

const getTaskInfo = async (deviceId: number) => {
	const { payload } = await getLastTask(deviceId, state.timeData.activeId);
	let otherData: any = [];
	const allDatalegend: any = [];
	if (payload) {
		taskInfo.patrolPlanName = payload.patrolPlanName;
		taskInfo.beginTime = payload.beginTime;
		taskInfo.endTime = payload.endTime;
		let order: number[] = [];
		if (payload?.statistics?.length) {
			const totalAlarmCount = payload.statistics.reduce(
				(total: any, obj: { alarmCount: any }) => total + obj.alarmCount,
				0
			);
			console.log(totalAlarmCount);
			payload.statistics.forEach((item: any, index: number) => {
				order.push(item.eventType);
				if (item.alarmCount) {
					const obj = {
						name: item.eventTypeName,
						value: item.alarmCount,
						percentage:
							((item.alarmCount / totalAlarmCount) * 100).toFixed(2).replace(/\.00$/, '') + '%',
						eventType: item.eventType,
						itemStyle: {
							color: seriesColors[item.eventType],
						},
					};
					otherData.push(obj);
					allDatalegend.push(item.eventTypeName);
				}

				const eventData = taskInfo.eventTypeList.get(item.eventType);
				const statusData = taskInfo.eventTypeStatus[item.eventType];
				if (eventData) {
					eventData.eventTypeName = item.eventTypeName;
					eventData.alarmCount = item.alarmCount;
				} else if (statusData) {
					statusData.eventTypeName = item.eventTypeName;
					statusData.normal.count = item.normalCount;
					statusData.alarm.count = item.alarmCount;
					// 任务巡检结束，漏检=总数-正常-告警
					if (item.totalCount) {
						const omitCount = item.totalCount - item.normalCount - item.alarmCount;
						omitCount < 0 ? (statusData.omit.count = 0) : (statusData.omit.count = omitCount);
					}
				}
			});
		}

		// // 根据接口排序

		console.log('taskInfo.eventTypeList', eventTypeList);

		handleOverviewStat(otherData, allDatalegend);
	}
};
const perviewPicList = computed(() => {
	return state.tableData.data.map((item) => item.pictureUrl || item.oriPictureUrl);
});

const getTableData = async () => {
	try {
		state.tableData.loading = true;
		const query = {
			page: state.tableData.pageParams.page - 1,
			size: state.tableData.pageParams.size,
			num: robotSn.value,
			...state.tableData.filter,
		};
		const { payload } = await getMonitorFilesEvents(query);

		state.tableData.data = payload.content;
		state.tableData.total = payload.totalElements;
		state.selectAll = false;
		state.tableData.loading = false;
	} catch (error) {
		state.tableData.loading = false;
	}
};
const getMETypeOptions = async () => {
	const { payload } = await getMonitorEventTypes();
	state.METypeOptions = payload;
};

const seriesColors: EmptyObjectType = reactive({
	2: '#114CFF',
	4: '#FF6000',
	1: '#F8CE6B',
	0: '#22D69F',
	3: '#9123C8',
	5: '#0BBDFF',
	qita: '#4753ff',
});
const pieStatisticsRef = ref();
// 概览统计
const handleOverviewStat = async (otherData = [], allDatalegend = []) => {
	console.log(otherData, allDatalegend);
	const icon = {
		align: 'left',
		width: 11,
		height: 11,
	};
	const pieStatOption = {
		backgroundColor: 'transparent',
		tooltip: {
			trigger: 'item',
			axisPointer: {
				type: 'shadow',
			},
			textStyle: {
				color: '#fff',
			},
			backgroundColor: 'rgba(50,50,50,0.7)',
			borderColor: 'rgba(104, 207, 173, 1)',
			// formatter: (params: any) => {
			// 	let html = ``;
			// 	params.data.subEventStatistics.forEach((item: any) => {
			// 		html += `<div>${item.name}：${item.count}</div>`;
			// 	});
			// 	return html;
			// },
		},
		legend: {
			show: true,
			type: 'scroll',
			orient: 'vertical',
			right: 4,
			padding: [30, 0, 0, 0],
			top: 1,
			itemGap: 1,
			icon: 'none',
			pageIconColor: '#67cfac',
			pageIconSize: [12, 12],
			pageTextStyle: {
				color: '#fff',
			},
			textStyle: {
				color: '#fff',
				rich: {
					2: {
						...icon,
						backgroundColor: seriesColors[2], // 设置线的颜色
					},
					4: {
						...icon,
						backgroundColor: seriesColors[4], // 设置线的颜色
					},
					1: {
						...icon,
						backgroundColor: seriesColors[1], // 设置线的颜色
					},
					0: {
						...icon,
						backgroundColor: seriesColors[0], // 设置线的颜色
					},
					3: {
						...icon,
						backgroundColor: seriesColors[3], // 设置线的颜色
					},
					5: {
						...icon,
						backgroundColor: seriesColors[5], // 设置线的颜色
					},
					uname: {
						align: 'left',
						width: 65,
						verticalAlign: 'bottom',
					},
					unum: {
						width: 45,
						align: 'center',
						verticalAlign: 'middle',
					},
					middle: {
						width: 110,
						height: 1,
						backgroundColor: 'rgba(255, 255, 255, .2)',
						verticalAlign: 'middle',
					},
					square: {
						width: 8,
						height: 8,
						backgroundColor: 'rgba(255, 255, 255, .2)',
						borderWidth: 0,
						verticalAlign: 'middle',
					},
				},
			},
			data: allDatalegend,
			formatter(name: string) {
				const nameItem = otherData.find((item: any) => item.name === name);

				console.log(nameItem);
				if (nameItem) {
					return `{${nameItem.eventType}|} {uname|${name}}{unum|${nameItem.percentage}}\n{middle|}{square|}`;
				}
			},
		},
		series: [
			{
				name: '统计信息',
				type: 'pie',
				left: -90,
				radius: ['34%', '45%'],
				label: {
					show: true,
					color: '#fff',
					formatter: (params: any) => {
						return `{green|${params.percent}%}\n${params.name}`;
					},
					rich: {
						green: {
							padding: [3, 0],
							color: '#67cfac',
						},
					},
				},
				labelLine: {
					length: 11,
					length2: 6,
					lineStyle: {
						color: '#67cfac',
					},
				},
				data: otherData,
			},
			{
				name: '内部虚线',
				type: 'pie',
				silent: true, // 不响应和触发鼠标事件
				left: -90,
				radius: ['27%', '28%'],
				color: ['#606664'],
				labelline: {
					show: false,
				},
				label: {
					show: false,
				},
				itemStyle: {
					normal: {
						color: (a: { data: number }) => {
							if (a.data == 1) {
								return '#606664';
							}
							if (a.data == 1) {
								return 'transparent';
							}
						},
					},
				},
				data: [
					2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1,
					2, 1,
				],
			},
			{
				name: '内部实线',
				type: 'pie',
				silent: true,
				left: -90,
				legend: { show: false },
				radius: ['6%', '8%'],
				color: ['#67cfac'],
				labelline: {
					show: false,
				},
				label: {
					show: false,
				},
				data: [{ value: 222, name: '内部实线' }],
			},
		],
	};
	nextTick(() => {
		pieStatisticsRef.value?.myChart.clear();
		pieStatisticsRef.value?.resetOption(pieStatOption);
	});

	console.log(pieStatOption);
};

// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getTableData();
};
const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getTableData();
};

// 全选
const onSelectAll = () => {
	state.selectAll = !state.selectAll;
	state.tableData.data = state.tableData.data.map((item) => ({
		...item,
		checked: state.selectAll,
	}));
};
const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (state.timeData.data.length === 0) return;
	if (resetPage) state.tableData.pageParams.page = 1;
	if (resetFilter) {
		state.tableData.filter.name = '';
		state.tableData.filter.eventTypes = [];
		state.tableData.filter.noResult = 0;

		if (!state.timeData.activeId) {
			state.tableData.filter.startTime = '';
			state.tableData.filter.endTime = '';
		}
	}
	getTableData();
};

const dragData = ref({
	remark: '',
	show: false,
});
const handleImageShow = (item: any) => {
	console.log('handleImageShow', item);
	if (item.groupEventType === 1) {
		dragData.value.remark =
			(item.monitorEventDetails?.length && item.monitorEventDetails[0]?.remark) || '';
		dragData.value.show = true;
	}
};
const handleImageClose = () => {
	dragData.value.show = false;
};
const handleImageSwitch = (index: number) => {
	const item: any = state.tableData.data[index];
	console.log('handleImageSwitch', item);
	if (item.groupEventType === 1) {
		dragData.value.show = true;
		dragData.value.remark =
			(item.monitorEventDetails?.length && item.monitorEventDetails[0]?.remark) || '';
	} else {
		dragData.value.show = false;
	}
};

// 返回大屏页面
const goBack = () => {
	router.push('/full-screen');
};
onMounted(async () => {
	await handelTimeChange(false, true);

	getMETypeOptions();
	NextLoading.done();
});

onUnmounted(() => {});

defineExpose({});
</script>

<style lang="scss" scoped>
.bg-lg {
	background-image: linear-gradient(to bottom, #186047, #41a97f);
}
.task-container {
	position: relative;
	height: 100%;
	background-color: #000000;
	color: rgba(255, 255, 255, 1);
	.mian {
		height: calc(100%);
		padding: 100px 39px 59px;
		display: flex;

		.title-img {
			display: block;
			width: 66px;
			height: 29px;
			margin-bottom: 20px;
		}

		.task-box {
			flex: 1;

			.task-title-time {
				display: flex;
				align-items: center;
				height: 20px;
				line-height: 20px;
				font-size: 14px;
				i {
					width: 7px;
					height: 7px;
					border-radius: 50%;
					margin-right: 5px;
					background-color: rgba(71, 194, 132, 1);
				}
				&:last-child {
					margin-left: 20px;
					i {
						background-color: rgba(155, 155, 155, 1);
					}
				}
			}

			.task-bottom {
				height: calc(100% - 29px);
				display: flex;

				.task-time {
					border: 1px solid rgba(104, 207, 173, 0.5);
					width: 230px;
					height: 100%;
					color: #fff;
					font-size: 14px;
					.select-title {
						height: 50px;
						display: flex;
						align-items: center;
						background-repeat: no-repeat;
						background-size: 100% 100%;
						background-image: url('/src/assets/task/select-time-bg.png');
						&:deep(.el-input__wrapper) {
							.el-input__suffix {
								width: 22px !important;
							}
						}
						&:deep(.el-picker-panel) {
							background-color: #000;
						}

						&:deep(.el-input__inner::placeholder) {
							color: rgba(255, 255, 255, 1);
							font-size: 14px;
						}

						&:deep(.el-popper.is-light .el-popper__arrow::before) {
							border: 1px solid rgba(104, 207, 173, 1) !important;
							border-bottom-color: transparent !important;
							border-right-color: transparent !important;
							background: #010b07 !important;
						}

						&:deep(.el-input__wrapper) {
							background-color: transparent;

							box-shadow: none;
							box-shadow: 0 0 0 0px transparent inset !important;

							.el-input__prefix-inner {
								.el-icon {
									position: relative;
									svg {
										display: none;
									}
									&::before {
										content: '';
										width: 18px;
										height: 18px;
										position: absolute;
										top: 50%;
										left: 10px;
										transform: translateY(-50%);
										background-repeat: no-repeat;
										background-size: 100% 100%;
										background-image: url('/src/assets/task/time.png');
									}
								}
							}
							.el-input__inner {
								text-align: center;
								color: rgba(104, 207, 173, 1);
								font-size: 14px;
							}
						}
					}

					.robot-title {
						position: relative;
						&::before {
							content: '';
							width: 19px;
							height: 17px;
							position: absolute;
							top: 50%;
							left: 20px;
							transform: translateY(-50%);
							background-repeat: no-repeat;
							background-size: 100% 100%;
							background-image: url('/src/assets/robotManage/jiqiren.png');
						}
						&:deep(.el-input__wrapper) {
							.el-input__inner {
								padding-left: 26px !important;
							}
						}
					}
					.date-title {
						padding-right: 8px;
						&:deep(.el-date-editor.el-input) {
							width: 100%;
							.el-input__wrapper {
								padding-right: 0;
							}
						}
					}
					.select-option {
						height: calc(100% - 100px);
						overflow-y: auto;
						.mt80 {
							margin-top: 80px;
						}
						.active-time-item {
							background-repeat: no-repeat;
							background-size: 100% 100%;
							background-image: url('/src/assets/task/active-time-item-bg.png');
						}
						.time-item {
							height: 113px;
							display: flex;
							flex-direction: column;
							justify-content: center;
							padding: 10px 21px;
							.time-item-name {
								height: 22px;
								line-height: 22px;
								font-size: 16px;
								margin-bottom: 10px;
							}
							.task-title-time {
								margin: 0;
								&:first-child {
									margin-bottom: 10px;
								}
							}
						}
					}
				}

				.task-view {
					flex: 1;
					padding: 0 23px;

					.task-title {
						display: flex;
						align-items: center;
						height: 25px;
						color: rgba(255, 255, 255, 1);
						line-height: 25px;
						font-size: 18px;
						margin-bottom: 25px;
						.task-name {
							margin-right: 20px;
						}
					}

					.task-search {
						color: #fff;
						&:deep(.el-tag) {
							@extend .bg-lg;
							color: #fff;
						}
						&:deep(.el-select-dropdown__item.selected) {
							background-color: transparent !important;
							color: rgb(104, 207, 173);
						}

						&:deep(.el-input__prefix) {
							display: none;
						}
						.search-input {
							margin-bottom: 18px;
							.search-item {
								margin-right: 80px;
								.el-select {
									margin-left: 22px;
								}
							}
						}
						.button-round {
							display: inline-block;
							width: 108px;
							height: 34px;
							border-radius: 54px;
							color: #fff;
							line-height: 34px;
							text-align: center;
							cursor: pointer;
							@extend .bg-lg;
							-webkit-user-select: none; /* Safari */
							-moz-user-select: none; /* Firefox */
							-ms-user-select: none; /* IE/Edge */
							user-select: none; /* 标准语法 */
						}

						.search-btns {
							display: flex;
							justify-content: space-between;
							margin-bottom: 18px;
						}

						&:deep(.el-select-dropdown__item.selected) {
							background-color: transparent !important;
							color: rgb(104, 207, 173);
						}

						&:deep(.el-input__prefix) {
							display: none;
						}
						&:deep(.el-popper.is-light .el-popper__arrow::before) {
							border: 1px solid rgba(104, 207, 173, 1) !important;
							border-bottom-color: transparent !important;
							border-right-color: transparent !important;
							background: #010b07 !important;
						}
						.el-popper.is-light {
							background: var(--el-bg-color-overlay);
							border: 1px solid var(--el-border-color-light);
						}

						&:deep(el-popper) &:deep(.el-form-item__label) {
							color: #fff;
							font-size: 14px;
						}

						&:deep(.el-select .el-input__inner::placeholder) {
							color: #767876 !important;
							font-size: 14px;
						}

						&:deep(.el-input__wrapper) {
							border-radius: 50px !important;
							border: 1px solid rgba(104, 207, 173, 1) !important;
							background-color: transparent !important;
							box-shadow: none !important;
							box-shadow: 0 0 0 0px transparent inset !important;
							padding: 0 11px !important;
						}
						&:deep(.el-input__inner) {
							--el-input-inner-height: 30px !important;
							color: rgba(255, 255, 255, 1);
						}
						&:deep(.el-select .el-input.is-focus .el-input__wrapper) {
							box-shadow: 0 0 0 0px transparent inset !important;
						}
						&:deep(.el-select .el-input__inner::placeholder) {
							color: rgba(255, 255, 255, 1);
							font-size: 14px;
						}

						.el-icon :deep(svg) {
							color: rgba(255, 255, 255, 1);
						}

						&:deep(.el-select) {
							--el-select-input-focus-border-color: none !important;
						}
					}
					.table-box {
						height: calc(100% - 100px);
						display: flex;
						flex-wrap: wrap;
						overflow-y: auto;

						.atlas-item {
							position: relative;
							width: calc((100% - 20px) / 3);
							height: 260px;
							margin-right: 10px;
							margin-bottom: 0.926vh;
							background-image: url('/src/assets/task/item-bg.png');
							background-repeat: no-repeat;
							background-size: 100% 100%;
							padding: 10px;

							&:nth-last-child(-n + 3) {
								margin-bottom: 0;
							}

							.thumbnail {
								width: 100%;
								height: 204px;

								.image-placeholder {
									width: 100%;
									height: 100%;
									display: flex;
									justify-content: center;
									align-items: center;
									.is-loading {
										color: #22d69f;
										font-size: 20px;
									}
								}

								:deep(.el-image__inner) {
									&:hover {
										transform: scale(1.05);
									}
								}
							}

							.checkbox {
								position: absolute;
								top: 10px;
								left: 20px;
							}

							&:deep(.el-checkbox__inner) {
								background-color: rgba(1, 11, 7, 0.3);
								border: 1px solid rgba(104, 207, 173, 1);
							}

							/* 当鼠标悬停时改变边框颜色 */
							&:deep(.el-checkbox__inner:hover) {
								border-color: #409eff;
							}

							&:deep(.el-checkbox__inner.is-checked) {
								background-color: #409eff;
								border-color: #409eff;
							}

							.atlas-type {
								position: absolute;
								top: 20px;
								right: 20px;
								width: 77px;
								height: 21px;
								line-height: 21px;
								text-align: center;
								color: #fff;
								border-radius: 12px;
								font-size: 12px;
								background-color: rgba(0, 0, 0, 0.3);
								z-index: 2;
							}

							&:nth-child(3n) {
								margin-right: 0;
							}
						}

						.other-content {
							margin-top: 10px;
							color: #fff;
							display: flex;
							justify-content: space-between;
							span {
								line-height: 25px;
								&:first-child {
									font-size: 17px;
								}
								&:last-child {
									font-size: 14px;
								}
							}
						}
					}
				}
			}
		}
		.statistics-box {
			width: 363px;
			height: 100%;
			padding-top: 29px;

			.statistics-main {
				height: calc(100% - 29px);
				// .event-statistics-title {
				// 	display: block;
				// 	height: 25px;
				// 	font-size: 16px;
				// 	line-height: 25px;
				// 	margin-bottom: 12px;
				// 	font-weight: 500;

				// }
				.event-statistics {
					height: 251px;
					padding: 60px 13px 0;
					background-image: url('/src/assets/task/renwu.png');
					background-repeat: no-repeat;
					background-size: 100% 100%;
					.task {
						position: relative;
						height: calc(100% - 25px - 16px);
						color: rgba(255, 255, 255, 1);
						font-size: 14px;
						.task-name {
							height: 2.04vh;
							margin-top: 17px;
							margin-bottom: 0.83vh;
							line-height: 2.04vh;
							font-size: 16px;
							font-weight: 600;
						}
						.time {
							height: 1.85vh;
							display: flex;
							align-items: center;
							span {
								display: block;
								width: 7px;
								height: 7px;
								border-radius: 50%;
								margin-right: 6px;
							}
						}
						.start-time {
							span {
								background-color: rgba(104, 207, 173, 1);
							}
							margin-bottom: 0.463vh;
						}
						.end-time {
							span {
								background-color: rgba(155, 155, 155, 1);
							}
							margin-bottom: 0.926vh;
						}

						.event-types {
							height: 52px;
							margin-bottom: 10px;
							display: flex;
							.event-type {
								flex: 1;
								margin-right: 10px;
								background-repeat: no-repeat;
								background-size: 100% 100%;
								padding: 0.463vh;
								&:last-child {
									margin-right: 0;
								}

								.event-type-name {
									height: 15px;
									line-height: 15px;
									font-size: 12px;
									text-align: center;
									margin-bottom: 10px;
								}
							}
						}

						.task-bottom {
							height: 52px;
							background-repeat: no-repeat;
							background-size: 100% 100%;
							background-image: url('/src/assets/fullScreen/park-equipment-bg.png');
							padding-left: 10px;
							margin-bottom: 10px;
							display: flex;
							flex-direction: column;
							justify-content: center;

							&:last-child {
								margin-bottom: 0;
							}
							.task-bottom-title {
								margin-bottom: 0.463vh;
							}
							.task-bottom-status {
								width: 100%;
								display: flex;
								span {
									display: flex;
									align-items: center;
									margin-right: 20px;

									i {
										width: 4px;
										height: 4px;
										border-radius: 50%;
										margin-right: 5px;
										background-color: rgba(71, 194, 132, 1);
									}
									&:nth-child(2n) {
										i {
											background-color: rgba(255, 0, 0, 1);
										}
									}
									&:last-child {
										margin-right: 0;
										i {
											background-color: rgba(255, 209, 57, 1);
										}
									}
								}
							}
						}

						// .park-equipment {
						// 	margin-bottom: 0.926vh;
						// 	background-image: url('/src/assets/fullScreen/park-equipment-bg.png');
						// }

						.trash-full {
							background-image: url('/src/assets/fullScreen/trash-full-bg.png');
						}
					}
				}

				.echarts-stat {
					width: 100%;
					padding-top: 60px;
					margin: 20px 0;
					height: 238px;
					display: flex;
					flex-direction: column;
					background-image: url('/src/assets/task/shijian.png');
					background-repeat: no-repeat;
					background-size: 100% 100%;
					.event-statistics-title {
						margin-top: 12px;
						margin-bottom: 0;
					}
					.echarts-item {
						flex: 1;
					}
				}
				.points-box {
					position: relative;
					height: calc(100% - 238px - 251px - 40px);
					padding: 60px 13px 0;
					background-image: url('/src/assets/task/xunjian-bg.png');
					background-repeat: no-repeat;
					background-size: 100% 100%;
					&::before {
						content: '';
						position: absolute;
						top: 8px;
						left: 0;
						width: 100%;
						height: 37px;
						background-image: url('/src/assets/task/xunjian-title.png');
						background-repeat: no-repeat;
						background-size: 100% 100%;
					}
					img {
						display: block;
						width: 100%;
						height: 50px;
						margin-bottom: 10px;
					}
					.pionts-title {
						background-image: linear-gradient(
							to bottom,
							rgba(24, 96, 71, 0.2),
							rgba(65, 169, 127, 0.2)
						);
					}

					.point-list {
						height: calc(100% - 34px);
						overflow-y: auto;
						overflow: overlay;
					}
					.pionts-item {
						height: 34px;
						font-size: 14px;
						display: flex;
						align-items: center;

						span {
							// flex: 1;
							text-align: center;
							&:first-child {
								width: 145px;
								text-align: left;
							}
							&:nth-child(2) {
								flex: 1;
							}
							&:last-child {
								width: 88px;
								display: flex;
								justify-content: center;
								align-content: center;
								span {
									height: 21px;
									line-height: 21px;
									text-align: center;
									border-radius: 10px;
									border: 1px solid #999;
								}
							}
						}
					}
				}
			}
		}
	}

	&:deep(.el-loading-mask) {
		background-color: rgba(0, 0, 0, 0.7);
		.path {
			stroke: #68cfad;
		}
	}
	.text-loding {
		text-align: center;
		font-size: 14px;
		color: #bbb;
	}
	.fanhui {
		width: 84px;
		height: 35px;
		object-fit: cover;
		position: absolute;
		top: 45px;
		left: 40px;
		z-index: 10;
		cursor: pointer;
	}
	.title {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 80px;
		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.footer {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 187px;
		pointer-events: none;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;

		img {
			display: block;
			width: 100%;
			height: 33px;
			object-fit: cover;
		}
	}
}

:deep(.el-popper) {
	.el-icon {
		color: #fff;
	}
	.el-date-picker {
		--el-datepicker-active-color: #68cfad;
		--el-datepicker-hover-text-color: #68cfad;
	}

	.el-date-picker__header-label {
		color: #fff;
	}

	.el-date-table th {
		color: #fff;
		border-bottom: solid 1px #606266;
	}
	.el-date-table td.next-month,
	.el-date-table td.prev-month {
		color: var(--el-text-color-regular);
	}

	.el-picker-panel {
		color: #fff;
	}

	.el-date-table td.today .el-date-table-cell__text {
		color: #68cfad;
		font-weight: 700;
	}

	.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
		color: #fff !important;
	}
}
</style>

<template>
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		width="480px"
		align-center
	>
		<el-form
			ref="dialogFormRef"
			:model="state.ruleForm"
			label-width="80px"
			size="large"
			label-position="right"
		>
			<el-form-item
				label="账号"
				prop="username"
				:rules="[{ required: true, message: '账号不能为空', trigger: 'blur' }]"
			>
				<el-input v-model="state.ruleForm.username" placeholder="请输入" clearable></el-input>
			</el-form-item>

			<el-form-item
				label="用户名"
				prop="name"
				:rules="[{ required: true, message: '用户名不能为空', trigger: 'blur' }]"
			>
				<el-input v-model="state.ruleForm.name" placeholder="请输入" clearable></el-input>
			</el-form-item>

			<template v-if="state.dialog.type === 'add'">
				<el-form-item
					label="密码"
					prop="password"
					:rules="[
						{ required: true, message: '密码不能为空', trigger: 'blur' },
						{ validator: validatePassword, trigger: 'blur' },
					]"
				>
					<el-input
						v-model="state.ruleForm.password"
						type="password"
						show-password
						placeholder="请输入"
						clearable
					></el-input>
				</el-form-item>

				<el-form-item
					label="确认密码"
					prop="checkPassword"
					:rules="[
						{ required: true, message: '确认密码不能为空', trigger: 'blur' },
						{ validator: validateCheckPassword, trigger: 'blur' },
					]"
				>
					<el-input
						v-model="state.ruleForm.checkPassword"
						type="password"
						show-password
						placeholder="请输入"
						clearable
					></el-input>
				</el-form-item>
			</template>

			<el-form-item
				label="角色"
				prop="userType"
				required
				:rules="[{ required: true, message: '角色不能为空', trigger: 'blur' }]"
			>
				<el-radio-group v-model="state.ruleForm.userType">
					<el-radio v-for="item in DEFAULT_ROLE" :key="item.id" :label="item.id">
						{{ item.name }}
					</el-radio>
				</el-radio-group>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="onCancel">取 消</el-button>
				<el-button type="primary" @click="onSubmit">{{ state.dialog.submitTxt }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, computed } from 'vue';
import type { FormInstance } from 'element-plus';
import { createUser, updateUser } from '/@/api/users';
import { ElMessage, ElMessageBox } from 'element-plus';
import { DEFAULT_ROLE } from '/@/utils/constants';
import { validatePassword as verifyPassword } from '/@/utils/toolsValidate';
import { useUserInfo } from '/@/stores/userInfo';
import { Session } from '/@/utils/storage';

const emits = defineEmits(['refresh']);

const dialogFormRef = ref<FormInstance>();
const state: UserDialogState = reactive({
	ruleForm: {
		username: '',
		name: '',
		password: '',
		checkPassword: '',
		userType: 2,
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
});
// 当前登录用户userId
const userId = computed(() => {
	return useUserInfo().userInfos.id;
});
// 当前登录用户的角色id
const roleId = computed(() => {
	return useUserInfo().userInfos.role?.id;
});
const openDialog = (row?: UserRow) => {
	state.dialog.isShowDialog = true;
	nextTick(() => {
		dialogFormRef.value?.resetFields();
		if (row && row.id) {
			state.dialog.type = 'edit';
			state.dialog.title = '修改用户';
			state.dialog.submitTxt = '修 改';
			state.ruleForm = {
				id: row.id,
				username: row.username,
				name: row.name,
				userType: row.role.id,
			};
		} else {
			state.dialog.type = 'add';
			state.dialog.title = '新增用户';
			state.dialog.submitTxt = '新 增';
			state.ruleForm = {
				username: '',
				name: '',
				password: '',
				checkPassword: '',
				userType: 2,
			};
		}
	});
};
const validatePassword = (rule: any, value: any, callback: any) => {
	if (!value) {
		callback(new Error('密码不能为空'));
	} else if (!verifyPassword(value)) {
		callback(new Error('密码长度在6~32之间，且必须包含字母和数字'));
	} else {
		callback();
	}
};
const validateCheckPassword = (rule: any, value: any, callback: any) => {
	if (!value) {
		callback(new Error('确认密码不能为空'));
	} else if (!verifyPassword(value)) {
		callback(new Error('密码长度在6~32之间，且必须包含字母和数字'));
	} else if (value !== state.ruleForm.password) {
		callback(new Error('两次密码不一致，请重新输入'));
	} else {
		callback();
	}
};
const onCancel = () => {
	state.dialog.isShowDialog = false;
};
const onSubmit = () => {
	dialogFormRef.value?.validate((valid: boolean) => {
		if (!valid) return;
		if (state.dialog.type === 'add') {
			const data = { ...state.ruleForm };
			delete data.checkPassword;
			createUser(data).then(() => {
				ElMessage.success('新增成功');
				onCancel();
				emits('refresh', true, true);
			});
			return;
		}
		updateUser(state.ruleForm).then(() => {
			ElMessage.success('修改成功');
			onCancel();
			emits('refresh');
			// 如果当前用户的角色被修改，需重新登录
			if (state.ruleForm.id === userId.value && state.ruleForm.userType !== roleId.value) {
				ElMessageBox.alert(`检测到您的用户角色发生变化，请重新登录`, '提示', {
					showClose: false,
					type: 'warning',
				})
					.then(() => {
						Session.clear();
						window.location.reload();
					})
					.catch(() => {});
			}
		});
	});
};
defineExpose({
	openDialog,
});
</script>

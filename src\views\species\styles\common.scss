.data-container {
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow-x: hidden;
}

.main-container {
	width: 100%;
	flex: 1;
	overflow: hidden;

	.flex-warp {
		height: calc(100% - 37px);
		overflow-x: hidden;
		overflow-y: auto;
		margin: 0;

		:deep(.el-row) {
			width: 100%;
		}

  .flex-warp-item {
    width: 100%;
    position: relative;
    padding: 0;

			.item-img {
				width: 100%;
				padding-top: 75%;
				overflow: hidden;
				position: relative;

      .el-image {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.3s ease;
        // aspect-ratio: 1/1;
        cursor: pointer;
      }
      &:hover .el-image {
        transform: scale(1.05);
      }

				.item-txt {
					position: absolute;
					left: 0;
					bottom: 0;
					width: 100%;
					line-height: 30px;
					color: #fff;
					background-color: hsla(0, 0%, 46.7%, 0.6);
					font-size: 12px;
					text-align: center;
					white-space: nowrap;
				}

				.item-name {
					position: absolute;
					top: 0;
					left: 0;
					font-size: 16px;
					color: #fff;
					background: rgba(207, 45, 45, 0.5);
					padding: 5px;
					word-wrap: break-word;
					-webkit-writing-mode: vertical-lr;
					-ms-writing-mode: tb-lr;
					writing-mode: vertical-lr;
					max-height: 130px;
				}
			}

    .item-latinName {
      background: #4e525e;
      color: #fff;
      font-size: 14px;
      line-height: 30px;
      height: 30px;
      padding: 0 5px;
      display: flex;
      & > div:first-child {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-align: center;
      }
      .icons {
        opacity: 0;
        transition: all 0.3s;
        .el-icon {
          margin: 5px 0 0 2px;
          cursor: pointer;
        }
      }
    }

			&:hover .item-latinName .icons {
				opacity: 1;
			}

			.pro-level {
				position: absolute;
				top: 4px;
				right: 4px;
				transform: rotate(30deg);
				z-index: 100;
				span {
					color: #ffef9c;
					font-size: 15px;
					font-weight: bolder;
					position: absolute;
					top: 2%;
					left: 50%;
					transform: translateX(-50%);
				}
				img {
					width: 30px;
				}
			}
		}
	}

	:deep(.el-pagination) {
		display: flex;
		justify-content: flex-end;
		padding: 0 15px;
	}
}
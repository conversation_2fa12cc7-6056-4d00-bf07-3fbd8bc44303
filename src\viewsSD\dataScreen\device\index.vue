<template>
  <div class="main-container map">
    <LeftCon :selectedDevices="state.selectedDevices" :timeType="state.dateRange"></LeftCon>
    <!-- # -->
    <div class="center-container">
      <div class="filter-container">
        <el-select
          class="data-screen-select"
          v-model="state.selectedDevices"
          placeholder="请选择"
          :collapse-tags="true"
          :multiple="true"
          :teleported="false"
        >
          <el-option v-for="item in deviceList" :key="item.id" :value="item.num">
            <div class="custom-option">
              <span>{{ item.name }}</span>
              <el-radio
                class="data-screen-radio"
                :model-value="state.selectedDevices.includes(item.num)"
                :label="true"
              ></el-radio>
            </div>
          </el-option>
        </el-select>
        <!-- date range -->
        <DateRange :dateRange="state.dateRange" @dateRangeChange="onDateRangeChange"></DateRange>
      </div>
      <Map :timeType="state.dateRange"></Map>
    </div>
    <!-- # -->
    <RightCon :selectedDevices="state.selectedDevices" :timeType="state.dateRange"></RightCon>
  </div>
</template>

<script setup lang="ts">
import { reactive, inject } from 'vue';
// 子组件
import LeftCon from './leftCon.vue';
import RightCon from './rightCon.vue';
import DateRange from '../component/dateRange/index.vue';
import Map from '../component/map/index.vue';
// import Map from '../component/map/index2.vue';

const deviceList = inject<DeviceRow[]>('deviceList');
const state = reactive({
  dateRange: 1 as (number | null), // 时间类型 1.近一月 2.近三月 3.近一年 null.累计
  selectedDevices: [] as string[],
  mainType: 1, // 1地图 2图集
});

// 日期范围改变
const onDateRangeChange = (value: number | null) =>{
  state.dateRange = value;
};
</script>
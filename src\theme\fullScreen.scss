
.fullScreen {
  .el-popper {
    border: 1px solid rgba(104, 207, 173, 1) !important;
    background-color: #010b07 !important;
    z-index: 2000;
  
    .el-select-dropdown__item {
      color: rgba(255, 255, 255, 1);
      padding: 0 10px !important;
    }
  
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-color: transparent !important;
      color: rgba(104, 207, 173, 1);
    }
  
    .el-select-dropdown__item.selected {
      color: rgba(104, 207, 173, 1);
      font-weight: 700;
    }
  }
  
  .el-popper.is-light .el-popper__arrow::before {
    border: 1px solid rgba(104, 207, 173, 1) !important;
    border-top-color: transparent !important;
    border-left-color: transparent !important;
    background: #010b07 !important;
  }

 
}

.fullScreen-container {
  .el-popper.is-light .el-popper__arrow::before {
    border: 1px solid rgba(104, 207, 173, 1) !important;
    border-bottom-color: transparent !important;
    border-right-color: transparent !important;
    background: #010b07 !important;
  }
}


.scrollbar-lg {
  // overflow-y: scroll;
  overflow-y: overlay;
  // &::-webkit-scrollbar {
  //   width: 10px;
  // }
  // &::-webkit-scrollbar-thumb {
  //   background-image: linear-gradient(to bottom, #186047, #41a97f);
  //   border-radius: 5px; /* 滑块的圆角 */
  //   // border-left: 6px solid transparent; /* 左边透明边框，占滚动条宽度的一半 */
  //   opacity: 0.65;
  // }
  &::-webkit-scrollbar-track-piece {
    background-color: transparent;
  }
  &::-webkit-scrollbar-track {
    background-color: transparent; /* 设置滚动条轨道颜色为透明 */
  }
}


.load-error {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  img{
    object-fit: contain
  }
  .small-img-error {
    width: 53px; 
    height: 40px;
  }
  .big-img-error {
    width: 105px; 
    height: 60px;
  }
}


.slide-backlog {
  position: absolute;
  top: 10px;
  right: 5px;
  z-index: 555;
  width: 40px;
  height: 17px;
  border-radius: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 1);
  // line-height: 17px;
  text-align: center;
  background-color: #68cfad;
  &.danger {
    background-color: rgba(230, 45, 45, 1);
  }
  &.warning {
    background-color: rgba(247, 135, 61, 1);
  }
}





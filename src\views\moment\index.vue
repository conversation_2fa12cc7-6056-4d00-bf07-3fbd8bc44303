<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<el-form-item label="物种类型">
					<el-select
						v-model="state.tableData.filter.animalTag"
						placeholder="请选择"
						clearable
						@change="onHandleAnChange"
					>
						<el-option
							v-for="item in Bird_Tag"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="物种名称">
					<el-select
						v-model="state.tableData.filter.animalNameList"
						placeholder="请选择"
						:collapse-tags="true"
						:multiple="true"
						:teleported="false"
					>
						<el-option
							v-for="item in state.speciesOptions"
							:key="item.animalId"
							:value="item.name"
							:label="item.name"
						>
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="">
					<el-date-picker
						v-model="state.timePeriod"
						type="datetimerange"
						range-separator="-"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						value-format="YYYY-MM-DD HH:mm:ss"
						clearable
					/>
				</el-form-item>
			</template>

			<template #searchBtns>
				<el-button type="primary" :disabled="selectIds.length === 0" @click="onBatchAudit">
					<template #icon>
						<el-icon><ele-User /></el-icon>
					</template>
					审核
				</el-button>
				<el-button type="danger" :disabled="selectIds.length === 0" @click="onBatchDelete">
					<template #icon>
						<el-icon><ele-Delete /></el-icon>
					</template>
					删除
				</el-button>
				<el-button type="info" @click="onSelectAll">全选</el-button>
			</template>
		</ViewSearch>

		<el-row class="atlas" v-loading="state.tableData.loading" :gutter="20">
			<template v-if="state.tableData.data.length > 0">
				<el-col
					:xs="24"
					:sm="12"
					:md="8"
					:lg="4"
					:xl="4"
					v-for="(item, $index) in state.tableData.data"
					:key="item.id"
				>
					<el-card class="atlas-item">
						<el-checkbox v-model="item.checked" />
						<div class="thumb-rec">
							<el-image
								class="thumbnail"
								:src="
									item.monitorEventFileModel.pictureUrl ||
									item.monitorEventFileModel.oriPictureThumbnailUrl
								"
								fit="contain"
								:preview-src-list="perviewPicList"
								:initial-index="$index"
								lazy
							></el-image>
							<div class="recResult-content">
								<template v-if="item.monitorEventFileModel?.monitorEventDetails?.length === 0">
									未发现物种
								</template>
								<template v-else>
									<span
										v-for="res in item.monitorEventFileModel.monitorEventDetails"
										:key="res.recResult"
									>
										{{ res.recResult }}{{ res.recResultCnt && `（${res.recResultCnt}）` }}
									</span>
								</template>
							</div>
						</div>
						<div class="other-content">
							<div>
								<el-tooltip :hide-after="0">
									{{ item.deviceName }}
									<template #content>{{ item.deviceName }}</template>
								</el-tooltip>
							</div>
							<div class="mt5">
								<el-tooltip :hide-after="0" :disabled="!item.monitorEventFileModel.longitude">
									GPS：{{ item.gps }}
									<template #content>{{ item.gps }}</template>
								</el-tooltip>
							</div>
							<div class="mt5">{{ item.monitorEventFileModel.recTime }}</div>
							<div class="operate-btns">
								<el-button type="danger" text @click="onDelete(item.id)">
									<el-icon><ele-Delete /></el-icon>
								</el-button>
							</div>
							<div class="lower-right-corner" v-if="item.audit === 1">
								<el-tooltip effect="dark" content="已审核" placement="bottom">
									<el-link>
										<el-icon><ele-User /></el-icon>
									</el-link>
								</el-tooltip>
							</div>
						</div>
					</el-card>
				</el-col>
			</template>
			<el-empty class="flex-margin" v-else></el-empty>
		</el-row>
		<el-pagination
			class="mt10"
			v-model:current-page="state.tableData.pageParams.page"
			:page-sizes="[12, 24, 38, 48]"
			background
			v-model:page-size="state.tableData.pageParams.size"
			layout="total, sizes, prev, pager, next, jumper"
			:total="state.tableData.total"
			@size-change="onHandleSizeChange"
			@current-change="onHandleCurrentChange"
		>
		</el-pagination>
		<!-- <SpeciesDialog ref="speciesDialogRef"></SpeciesDialog> -->
	</div>
</template>

<script setup lang="ts" name="Moment">
import { reactive, onBeforeMount, computed, defineAsyncComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getAnimals } from '/@/api/species';
import { deleteBatchMoments, auditBatchMoments, getMoments, deleteMoments } from '/@/api/moments';
import { Bird_Tag } from '/@/utils/constants';
const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
// 定义变量内容
const state = reactive<ViewBaseState<any>>({
	METypeOptions: [],
	speciesOptions: [],
	tableData: {
		filter: {},
		data: [],
		loading: false,
		pageParams: {
			page: 1,
			size: 12,
		},
		total: 0,
	},
	timePeriod: [],
	selectAll: false,
});

// 页面加载时
onBeforeMount(() => {
	getTableData();
});

// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	const { timePeriod } = state;
	const query = {
		page: state.tableData.pageParams.page - 1,
		size: state.tableData.pageParams.size,
		startTime: timePeriod && timePeriod.length > 0 ? timePeriod[0] : null,
		endTime: timePeriod && timePeriod.length > 0 ? timePeriod[1] : null,
		...state.tableData.filter,
	};

	console.log(query);
	const { payload } = await getMoments(query);
	state.tableData.data = payload.content.map((item: any) => {
		const { monitorEventFileModel } = item;
		return {
			...item,
			deviceName:
				monitorEventFileModel.device.name +
				(monitorEventFileModel.device.channelName
					? ` [${monitorEventFileModel.device.channelName}]`
					: ''),
			gps:
				monitorEventFileModel.longitude && monitorEventFileModel.latitude
					? `${monitorEventFileModel.longitude},${monitorEventFileModel.latitude}`
					: '-',
			checked: false,
		};
	});
	state.tableData.total = payload.totalElements;
	state.selectAll = false;
	state.tableData.loading = false;
};

const getSpeciesOptions = async () => {
	const { animalTag } = state.tableData.filter;
	const query = {
		tag: animalTag,
	};
	const { payload } = await getAnimals(query);
	state.speciesOptions = payload;
};

const onHandleAnChange = () => {
	state.tableData.filter.animalNameList = [];
	getSpeciesOptions();
};

const perviewPicList = computed(() => {
	return state.tableData.data.map(
		(item) => item.monitorEventFileModel.pictureUrl || item.monitorEventFileModel.oriPictureUrl
	);
});

// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getTableData();
};
const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getTableData();
};
// 全选
const onSelectAll = () => {
	state.selectAll = !state.selectAll;
	state.tableData.data = state.tableData.data.map((item) => ({
		...item,
		checked: state.selectAll,
	}));
};
const selectIds = computed(() => {
	return state.tableData.data.filter((item) => item.checked).map((item) => item.id);
});

// 删除
const onDelete = (id: string) => {
	ElMessageBox.confirm(`此操作将永久删除，是否继续?`, '提示', {
		type: 'warning',
	})
		.then(async () => {
			await deleteMoments(id);
			getTableData();
			ElMessage.success('删除成功');
		})
		.catch(() => {});
};
const onBatchDelete = () => {
	ElMessageBox.confirm(`此操作将永久删除，是否继续?`, '提示', {
		type: 'warning',
	})
		.then(async () => {
			await deleteBatchMoments(selectIds.value);
			getTableData();
			ElMessage.success('批量删除成功');
		})
		.catch(() => {});
};

const onBatchAudit = () => {
	ElMessageBox.confirm(`此操作将批量审核，是否继续?`, '提示', {
		type: 'warning',
	})
		.then(async () => {
			const query: { id: string; audit: 1 }[] = [];
			selectIds.value.forEach(async (id) => {
				query.push({
					id,
					audit: 1,
				});
			});
			await auditBatchMoments(query);
			getTableData();
			ElMessage.success('批量审核成功');
		})
		.catch(() => {});
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetPage) state.tableData.pageParams.page = 1;
	if (resetFilter) {
		state.tableData.filter.animalTag = null;
		state.tableData.filter.animalNameList = [];
		state.timePeriod = [];
	}
	getTableData();
};

const o = {
	id: 656,
	name: 'System',
	icon: 'el-icon-setting',
	title: '系统管理',
	path: '/system',
	component: 'Layout',
	action: null,
	hidden: false,
	type: 1,
	permissions: '',
	children: [
		{
			id: 668,
			name: 'DataView',
			icon: '',
			title: '数据调试',
			path: '',
			component: 'system/index',
			action: null,
			hidden: false,
			type: 1,
			permissions: '',
			children: [],
		},
		{
			id: 669,
			name: 'DamageStatistic',
			icon: '',
			title: '病害统计',
			path: 'disease-accuracy',
			component: 'disease-accuracy/index',
			action: null,
			hidden: false,
			type: 1,
			permissions: 'users:view:users',
			children: [],
		},
		{
			id: 670,
			name: 'AppVersion',
			icon: '',
			title: '版本管理',
			path: 'app-version',
			component: 'app-version/index',
			action: null,
			hidden: false,
			type: 1,
			permissions: 'users:view:users',
			children: [],
		},
	],
};
</script>

<style scoped lang="scss">
@import '../../theme/atlas.scss';

.other-content {
	position: relative;
	.lower-right-corner {
		position: absolute;
		right: 10px;
		bottom: 10px;
	}
}
</style>

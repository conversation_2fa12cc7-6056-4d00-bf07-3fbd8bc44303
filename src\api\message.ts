import request from '/@/utils/request';

/**
 * @method getMessages 获取消息列表
 * @method createMessage 消息创建
 * @method getMessagesDetail 消息详情（查看后消息会变成已读）
 * @method batchRead 批量消息已读
 * @method closeSSE 关闭SSE连接
 */

export function getMessages(params?: Object) {
  return request({
    url: `/messages`,
    method: 'get',
    params,
  });
}

export function createMessage(data: Object) {
  return request({
    url: `/messages`,
    method: 'post',
    data,
  });
}

export function getMessagesDetail(id: string) {
  return request({
    url: `/messages/${id}`,
    method: 'get',
  });
}

export function batchRead(data: string[]) {
  return request({
    url: `/messages/batch/read`,
    method: 'post',
    data
  });
}


export function closeSSE(clientId: string) {
  return request({
    url: '/sse/close',
    method: 'get',
    params: {
      clientId,
    },
  });
}
<template>
	<div class="img-preview">
		<el-image-viewer
			v-if="viewerState.showViewer"
			ref="imageViewerRef"
			:url-list="perviewPicList"
			:infinite="false"
			:initial-index="viewerState.initialIndex"
			@close="handleImageClose"
		>
		</el-image-viewer>

		<!-- 遮罩层loading -->
		<div v-if="viewerState.showViewer && maskLoading && loading" class="img-preview-mask">
			<div class="loading-spinner">
				<el-icon class="is-loading">
					<Loading />
				</el-icon>
				<span class="loading-text">加载中...</span>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, computed, watch, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElImageViewer, ElIcon } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';

/**
 * ===== ImgPreview 图片预览组件 =====
 *
 * 主要功能：
 * 1. 基于 Element Plus 的 ElImageViewer 实现分页图片预览
 * 2. 支持键盘导航 (方向键) 和鼠标点击
 * 3. 自动处理跨页翻页 (当前页看完自动加载下一页)
 * 4. 显示进度信息 (如 "3/10")
 * 5. 支持自定义按钮 (如订正按钮)
 *
 * 使用场景：
 * - 监控事件页面：浏览监控图片，支持订正功能
 * - 任务页面：查看任务相关图片
 * - 图集页面：浏览图片集合
 *
 * 关键设计思路：
 * 1. 索引管理器：统一处理复杂的索引计算和验证
 * 2. 导航处理：统一处理所有翻页逻辑，避免代码重复
 * 3. DOM管理器：统一管理自定义DOM元素的创建和销毁
 * 4. 闪烁优化：延迟setActiveItem调用，避免翻页时的视觉闪烁
 *
 * 维护注意事项：
 * - 修改翻页逻辑时，注意保持handleNavigation和watch的一致性
 * - 添加新的DOM元素时，记得在removeProgressElement中清理
 * - 索引计算涉及分页，修改时要考虑边界情况
 */

// ===== 索引管理器 =====
/**
 * 索引管理器 - 为什么需要它？
 *
 * 想象一下：你在看一本有1000页的相册，每页显示10张照片
 * - 你现在看的是第5页的第3张照片
 * - 这张照片在整本相册中是第43张 (4*10 + 3)
 * - 当你翻页时，需要确保不会跳到不存在的照片上
 *
 * 这个管理器就是帮你处理这些复杂的计算，避免出现"第5页有15张照片"这种错误
 */
const useIndexManager = (props: any) => {
	/**
	 * 验证索引是否合法
	 *
	 * 场景：用户从第1页（10张图）跳到第2页（只有3张图）
	 * 如果用户之前看的是第8张，第2页没有第8张，就重置到第1张
	 *
	 * @param index 想要显示的图片索引
	 * @param listLength 当前页面实际有多少张图片
	 * @returns 修正后的安全索引
	 */
	const validateIndex = (index: number, listLength: number): number => {
		if (listLength === 0) return 0; // 没有图片时，索引为0
		if (index < 0) return 0; // 索引不能是负数
		if (index >= listLength) return 0; // 索引超出范围时，回到第一张
		return index; // 索引正常，直接返回
	};

	/**
	 * 计算这张图片在整个数据集中的绝对位置
	 *
	 * 例子：总共有100张图片，每页显示10张
	 * - 第1页第1张 = 绝对位置 0
	 * - 第3页第5张 = (3-1) * 10 + 4 = 24 (从0开始计数)
	 *
	 * 用途：判断是否到达了整个数据集的边界
	 */
	const calculateAbsoluteIndex = (currentIndex: number): number => {
		return (props.currentPage - 1) * props.pageSize + currentIndex;
	};

	/**
	 * 检查是否到达边界（不能再翻页了）
	 *
	 * 两种边界情况：
	 * 1. 向前翻页：已经是第1页第1张图片
	 * 2. 向后翻页：已经是最后一页最后一张图片
	 *
	 * 为什么要检查？避免用户点击时出现空白或错误
	 */
	const checkBoundary = (currentIndex: number, direction: 'prev' | 'next') => {
		const absoluteIndex = calculateAbsoluteIndex(currentIndex);

		if (direction === 'prev') {
			// 向前翻页：检查是否已经是第一张
			return props.currentPage === 1 && currentIndex === 0;
		} else {
			// 向后翻页：检查是否已经是最后一张
			return absoluteIndex >= props.total - 1;
		}
	};

	/**
	 * 计算进度显示内容
	 *
	 * 显示逻辑：
	 * - 正在加载时：返回特殊标识，用于显示Loading图标
	 * - 正常情况：返回"当前位置/总数" (如 "3/10")
	 *
	 * 注意：这里显示的是在当前页面中的位置，不是整个数据集的位置
	 */
	const calculateProgress = (index: number): string => {
		if (props.loading) {
			return '__LOADING_ICON__'; // 特殊标识，用于在DOM操作中识别需要显示图标
		}
		const currentPosition = (index % props.pageSize) + 1; // +1 因为用户看到的是从1开始
		return `${currentPosition}/${props.perviewPicList.length}`;
	};

	return {
		validateIndex,
		calculateAbsoluteIndex,
		checkBoundary,
		calculateProgress,
	};
};

// 定义组件属性
const props = defineProps({
	perviewPicList: {
		type: Array as () => string[],
		default: () => [],
	},
	pageSize: {
		type: Number,
		required: true,
	},
	loading: {
		type: Boolean,
		default: false,
	},
	data: {
		type: Object as () => Record<string, any>,
		default: () => ({}),
	},
	total: {
		type: Number,
		default: 0,
	},
	currentPage: {
		type: Number,
		default: 1,
	},
	correctShow: {
		type: Boolean,
		default: false,
	},
	// 遮罩层loading - 在图片预览器上显示半透明遮罩和加载动画
	// 与loading属性的区别：loading控制翻页逻辑，maskLoading控制视觉反馈
	maskLoading: {
		type: Boolean,
		default: false,
	},
});

// 定义事件
const emit = defineEmits(['loadNextPage', 'loadPrevPage', 'correct']);
// 组件引用
const imageViewerRef = ref<InstanceType<typeof ElImageViewer> | null>(null);

// 初始化索引管理器
const indexManager = useIndexManager(props);

// 图片查看器状态（简化后的状态结构）
const viewerState = reactive({
	showViewer: false,
	initialIndex: 0,
});

// 翻页方向状态
const pageDirection = ref<'prev' | 'next' | null>(null);

// ===== 统一导航处理函数 =====
/**
 * 统一的导航处理函数 - 处理用户的翻页操作
 *
 * 这个函数处理所有的翻页逻辑，无论是点击按钮还是按键盘都会调用它
 *
 * 翻页流程：
 * 1. 检查是否正在加载数据 → 如果是，就不允许翻页
 * 2. 检查是否到达边界 → 如果是，提示用户并停止
 * 3. 尝试在当前页内翻页 → 如果可以，直接切换图片
 * 4. 如果当前页翻完了 → 触发加载新页面数据
 *
 * 为什么不立即调用setActiveItem？
 * 因为会导致闪烁！用户会先看到当前页第一张，然后才看到新页面的图片
 *
 * @param direction 'prev'=向前翻页, 'next'=向后翻页
 * @returns true=成功处理, false=无法翻页(到边界或正在加载)
 */
const handleNavigation = (direction: 'prev' | 'next'): boolean => {
	// 正在加载数据时，不允许翻页
	if (props.loading) {
		return false;
	}

	const lastIndex = props.perviewPicList.length - 1;

	// 检查边界：是否已经到头了？
	if (indexManager.checkBoundary(viewerState.initialIndex, direction)) {
		const message =
			direction === 'prev'
				? '已经是第一张图片，无法继续向前浏览'
				: '已经是最后一张图片，无法继续向后浏览';
		ElMessage.warning(message);
		return false;
	}

	// 开始翻页逻辑
	if (direction === 'prev') {
		// 向前翻页
		viewerState.initialIndex -= 1;
		if (viewerState.initialIndex < 0) {
			// 当前页翻完了，需要加载上一页
			// 例如：从第2页第1张 → 第1页最后一张
			pageDirection.value = 'prev';
			emit('loadPrevPage');
			// 关键：不立即设置索引！等数据加载完成后再设置，避免闪烁
			return true;
		}
	} else {
		// 向后翻页
		viewerState.initialIndex += 1;
		if (viewerState.initialIndex > lastIndex) {
			// 当前页翻完了，需要加载下一页
			// 例如：从第1页最后一张 → 第2页第1张
			pageDirection.value = 'next';
			emit('loadNextPage');
			// ⚠️ 关键：不立即设置索引！等数据加载完成后再设置，避免闪烁
			return true;
		}
	}

	// 在当前页内翻页成功，更新进度显示和图片显示
	updateProgressElement(viewerState.initialIndex);
	// 立即更新图片查看器显示当前索引的图片
	imageViewerRef.value?.setActiveItem(viewerState.initialIndex);
	return true;
};

/**
 * 监听数据变化 - 处理翻页完成后的逻辑
 *
 * 监听的数据：
 * 1. currentPage - 当前页码
 * 2. perviewPicList - 当前页的图片列表
 * 3. loading - 数据加载状态
 *
 * 为什么要监听这些？
 * 当用户翻页时，父组件会：
 * 1. 设置 loading = true
 * 2. 调用接口获取新页面数据
 * 3. 更新 currentPage 和 perviewPicList
 * 4. 设置 loading = false
 *
 * 我们需要在数据加载完成后，正确设置图片索引，避免闪烁
 */
watch(
	[() => props.currentPage, () => props.perviewPicList, () => props.loading],
	([newPage, newList, isLoading], [, , wasLoading]) => {
		if (!viewerState.showViewer) return;

		// 正在加载时，只更新进度显示
		if (isLoading) {
			updateProgressElement(viewerState.initialIndex);
			return;
		}

		// 数据加载完成时的处理
		if (wasLoading && !isLoading) {
			// 第一步：验证索引是否还有效
			// 例如：从10张图的页面跳到3张图的页面，索引8就无效了
			viewerState.initialIndex = indexManager.validateIndex(
				viewerState.initialIndex,
				newList.length
			);

			// 第二步：根据翻页方向设置正确的索引
			let shouldShowMessage = false;
			if (pageDirection.value === 'prev' && newList.length > 0) {
				// 向前翻页：显示上一页的最后一张图
				viewerState.initialIndex = newList.length - 1;
				shouldShowMessage = true;
				pageDirection.value = null;
			} else if (pageDirection.value === 'next') {
				// 向后翻页：显示下一页的第一张图
				viewerState.initialIndex = 0;
				shouldShowMessage = true;
				pageDirection.value = null;
			}

			// 第三步：更新进度显示
			updateProgressElement(viewerState.initialIndex);

			// 第四步：设置图片查看器索引（关键：在nextTick中执行，避免闪烁）
			nextTick(() => {
				if (imageViewerRef.value && typeof imageViewerRef.value.setActiveItem === 'function') {
					// 跨页翻页时不使用节流，确保能正确设置
					imageViewerRef.value.setActiveItem(viewerState.initialIndex);
				}
				// 翻页完成后显示成功消息
				if (shouldShowMessage) {
					ElMessage.success(`已切换到第 ${newPage} 页`);
				}
			});
		}
	}
);

// ===== DOM管理器 =====
/**
 * DOM管理器 - 为什么要统一管理DOM操作？
 *
 * Element Plus的图片预览器是一个独立的组件，我们需要在它的基础上添加自定义功能：
 * 1. 进度显示 (如 "3/10")
 * 2. 自定义按钮 (如 "订正" 按钮)
 *
 * 如果不统一管理，这些DOM操作会散落在各个函数中，难以维护
 */
const useDOMManager = () => {
	/**
	 * 创建或更新进度显示元素
	 *
	 * 工作原理：
	 * 1. 先尝试找到已存在的进度元素
	 * 2. 如果没找到，就创建一个新的
	 * 3. 更新显示内容 (文字或Loading图标)
	 * 4. 如果元素还没添加到页面，就添加进去
	 *
	 * 为什么这样设计？避免重复创建元素，提高性能
	 */
	const updateProgressElement = (index: number) => {
		const progressElement =
			document.querySelector('.el-image-viewer__progress') || document.createElement('span');
		progressElement.className = 'el-image-viewer__btn el-image-viewer__progress';

		// 使用索引管理器计算进度内容
		const progressContent = indexManager.calculateProgress(index);

		// 如果是加载状态，显示Loading图标
		if (progressContent === '__LOADING_ICON__') {
			// 清空内容并创建Loading图标
			progressElement.innerHTML = '';
			progressElement.classList.add('loading');

			// 创建Loading图标元素 - 使用CSS创建的圆形loading
			const iconElement = document.createElement('div');
			iconElement.className = 'loading-spinner';
			progressElement.appendChild(iconElement);
		} else {
			// 正常状态显示文字
			progressElement.textContent = progressContent;
			progressElement.classList.remove('loading');
		}

		if (!progressElement.parentNode) {
			const viewerWrapper = document.querySelector('.el-image-viewer__wrapper');
			viewerWrapper?.appendChild(progressElement);
		}
	};

	/**
	 * 清理所有自定义元素
	 *
	 * 什么时候调用？
	 * - 用户关闭图片预览时
	 * - 组件卸载时
	 *
	 * 为什么要移除事件监听器？
	 * 防止内存泄漏，特别是correctCallback函数的引用
	 */
	const removeProgressElement = () => {
		const progressElement = document.querySelector('.el-image-viewer__progress');
		progressElement?.remove();
		const correctElement = document.querySelector('.el-image-viewer__correct');
		if (correctElement) {
			correctElement.remove();
			correctElement.removeEventListener('click', correctCallback);
		}
	};

	/**
	 * 添加自定义按钮 (如订正按钮)
	 *
	 * 使用场景：
	 * - 在监控事件页面，用户可以点击"订正"来修改识别结果
	 * - 只有当 correctShow 属性为 true 时才会显示
	 *
	 * 注意：按钮的样式通过CSS类名控制，点击事件绑定到correctCallback
	 */
	const addCustomButton = () => {
		const viewerWrapper = document.querySelector('.el-image-viewer__wrapper');
		const correctElement = document.createElement('span');
		correctElement.className = 'el-image-viewer__btn el-image-viewer__correct';
		correctElement.textContent = '订正';
		viewerWrapper?.appendChild(correctElement);
		correctElement.addEventListener('click', correctCallback);
	};

	return {
		updateProgressElement,
		removeProgressElement,
		addCustomButton,
	};
};

// 初始化DOM管理器
const domManager = useDOMManager();

// 为了保持向后兼容，导出DOM管理器的方法
const updateProgressElement = domManager.updateProgressElement;
const removeProgressElement = domManager.removeProgressElement;

/**
 * 设置预览组件按钮的点击事件监听
 */
const setupButtonListeners = () => {
	const viewerEl = document.querySelector('.el-image-viewer__wrapper');
	if (!viewerEl) return;

	viewerEl.addEventListener('click', (e) => {
		const target = e.target as HTMLElement;

		// 如果预览器未显示，不处理点击事件
		if (!viewerState.showViewer) return;

		// 检查是否点击了上一张按钮
		if (
			target.classList.contains('el-image-viewer__prev') ||
			target.closest('.el-image-viewer__prev')
		) {
			handleNavigation('prev');
		}

		// 检查是否点击了下一张按钮
		if (
			target.classList.contains('el-image-viewer__next') ||
			target.closest('.el-image-viewer__next')
		) {
			handleNavigation('next');
		}
	});
};

/**
 * 处理图片预览事件
 */
const handlePreview = (index: number) => {
	viewerState.initialIndex = index;
	viewerState.showViewer = true;

	// 在预览开始时添加键盘事件监听
	window.addEventListener('keydown', handleKeyDown);
	window.addEventListener('keyup', handleKeyUp);

	console.log('handlePreview', index);
	nextTick(() => {
		updateProgressElement(index);
		setupButtonListeners();
		if (props.correctShow) {
			domManager.addCustomButton();
		}
	});
};

const correctCallback = () => {
	emit('correct', viewerState.initialIndex);
	viewerState.showViewer = false;
};

// 键盘事件处理函数
const handleKeyDown = (e: KeyboardEvent) => {
	// 如果预览器未显示，不处理键盘事件
	if (!viewerState.showViewer) {
		return;
	}

	// 防止默认行为和事件冒泡
	e.preventDefault();
	e.stopPropagation();

	if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
		handleNavigation('prev');
	} else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
		handleNavigation('next');
	}
};

// 键盘释放事件处理函数（简化版）
const handleKeyUp = (_e: KeyboardEvent) => {
	if (!viewerState.showViewer) return;
	// 如果需要特殊的键盘释放处理逻辑，可以在这里添加
};

/**
 * 处理图片关闭事件
 */
const handleImageClose = () => {
	viewerState.showViewer = false;
	viewerState.initialIndex = 0; // 重置为0而不是-1，避免后续验证问题
	removeProgressElement();

	// 在预览关闭时移除键盘事件监听
	window.removeEventListener('keydown', handleKeyDown);
	window.removeEventListener('keyup', handleKeyUp);
};

onUnmounted(() => {
	window.removeEventListener('keydown', handleKeyDown);
	window.removeEventListener('keyup', handleKeyUp);
});

// 导出方法供父组件调用
defineExpose({
	handlePreview,
	pageDirection,
});
</script>

<style lang="scss">
$btnWH: vw(44);
$abOffset: vw(40);
.img-preview {
	.el-image-viewer__actions {
		left: auto;
		right: $abOffset;
		bottom: -1%;
		transform: translateX(0) translateY(-50%);
		width: $btnWH;
		height: vw(282);
		padding: vw(23) 0;
		.el-image-viewer__actions__inner {
			width: 100%;
			height: 100%;
			text-align: justify;
			cursor: default;
			font-size: vw(23);
			color: #fff;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;
		}
	}
	.el-image-viewer__correct {
		height: $btnWH;
		width: $btnWH;
		bottom: $abOffset;
		right: $abOffset;
		font-size: vw(16);
		color: #fff;
		user-select: none;
		background-color: var(--el-text-color-regular);
	}
	.el-image-viewer__progress {
		top: 26%;
		right: $abOffset;
		width: $btnWH;
		height: $btnWH;
		line-height: vw(40);
		font-size: vw(16);
		color: #fff;
		user-select: none;
		// border-radius: vw(10);
		background-color: var(--el-text-color-regular);
		cursor: default;

		// 加载中状态显示Loading图标
		&.loading {
			display: flex;
			align-items: center;
			justify-content: center;

			.loading-spinner {
				width: vw(16);
				height: vw(16);
				border: vw(2) solid rgba(255, 255, 255, 0.3);
				border-top: vw(2) solid #fff;
				border-radius: 50%;
				animation: loading-spin 1s linear infinite;
			}
		}

		@keyframes loading-spin {
			0% {
				transform: rotate(0deg);
			}
			100% {
				transform: rotate(360deg);
			}
		}
	}

	.el-image-viewer__prev {
		left: $abOffset;
		width: $btnWH;
		height: $btnWH;
	}
	.el-image-viewer__next {
		right: $abOffset;
		width: $btnWH;
		height: $btnWH;
	}
	.el-image-viewer__close {
		right: $abOffset;
		width: $btnWH;
		height: $btnWH;
	}

	// 遮罩层loading样式
	.img-preview-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 2000000; // 确保在图片预览器之上

		.loading-spinner {
			display: flex;
			flex-direction: column;
			align-items: center;
			color: #fff;

			.el-icon {
				font-size: vw(40);
				margin-bottom: vw(10);
			}

			.loading-text {
				font-size: vw(16);
				opacity: 0.8;
			}
		}
	}
}
</style>

<template>
	<div class="nav-wrap">
		<div
			class="nav-home-wrap"
			:class="{
				animate__animated: true,
				animate__zoomOutDown: !showNav,
				animate__zoomInUp: showNav && mounted,
			}"
			@animationend="handleAnimationEnd"
		>
			<div class="nav-circle">
				<!-- 环形导航按钮 -->
				<div
					v-for="(item, index) in navItems"
					:key="item.key"
					class="nav-item"
					:class="[
						`nav-item-${index}`,
						navCountClass,
						{ active: activeNav === item.key },
						{ 'item-show': animationFinished },
					]"
					@click="handleNavClick(item)"
				>
					<!-- 避免切换的时候闪烁 -->
					<img
						v-show="activeNav !== item.key"
						:src="item.icon"
						:alt="item.label"
						:width="item.width"
						:height="item.height"
					/>
					<img
						v-show="activeNav === item.key"
						:src="item.activeIcon"
						:alt="item.label"
						:width="item.width"
						:height="item.height"
					/>
					<span :class="{ active: activeNav === item.key }">{{ item.label }}</span>
				</div>
			</div>
		</div>
		<!-- 中心按钮 -->
		<div class="nav-center" :class="{ 'nav-center-bottom': !showNav }">
			<img
				:class="{ 'rotate-90': showNav }"
				src="/src/assets/sd/data-screen/more.png"
				alt=""
				@click="handleZoomClick"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import 'animate.css';

interface NavItem {
	key: string;
	label: string;
	path: string;
	icon: string;
	width: number;
	height: number;
	activeIcon: string;
}

const props = defineProps<{
	navItems: NavItem[];
	activeNav: string;
}>();

const emits = defineEmits<{
	(e: 'update:activeNav', key: string): void;
	(e: 'navClick', item: NavItem): void;
	(e: 'zoomClick'): void;
}>();

const router = useRouter();
const route = useRoute();

// 计算类名
const navCountClass = computed(() => `nav-count-${props.navItems.length}`);

const handleNavClick = (item: NavItem) => {
	if (!item.path) return;
	console.log('Navigating to:', item.path);
	emits('update:activeNav', item.key);
	emits('navClick', item);
	router.push(item.path);
};
const showNav = ref(true);
const handleZoomClick = () => {
	showNav.value = !showNav.value;
};

// 添加初始化动画控制
const mounted = ref(false);
onMounted(() => {
	mounted.value = true;
	props.navItems.forEach((item) => {
		[item.icon, item.activeIcon].forEach((src) => {
			if (src) {
				const img = new Image();
				img.src = src;
			}
		});
	});
});

// 监听路由变化，自动更新高亮状态

const animationFinished = ref(false);

const handleAnimationEnd = (e: AnimationEvent) => {
	if (e.animationName === 'zoomInUp') {
		animationFinished.value = true;
	} else if (e.animationName === 'zoomOutDown') {
		animationFinished.value = false;
	}
};
</script>

<style lang="scss" scoped>
.nav-wrap {
	position: absolute;
	left: 50%;
	bottom: -4.5%;
	transform: translate(-50%, 0);
	z-index: 2;
	pointer-events: none;

	.nav-center {
		position: absolute;
		left: 50%;
		top: 55%;
		transform: translate(-50%, -50%);
		z-index: 3;
		transition: all 0.3s ease;
		pointer-events: auto;

		&.nav-center-bottom {
			left: 50%;
			top: auto;
			bottom: vw(40);
			transform: translate(-50%, 0) rotate(-90deg);
		}

		img {
			cursor: pointer;
			transition: transform 0.3s ease;
			width: vw(31);
			height: vw(22);
			&.rotate-90 {
				transform: rotate(90deg);
			}
		}
	}
}

.nav-home-wrap {
	position: relative;
	width: vw(312);
	height: vw(240);
	background: url('/@/assets/hlj/nav-home-wrap/bg.png') no-repeat;
	background-size: contain;

	.nav-circle {
		position: relative;
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		pointer-events: none;
		// 环形导航按钮
		.nav-item {
			position: absolute;
			width: vw(80);
			height: vw(32);
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			transition: all 0.3s;
			border-radius: vw(4);
			pointer-events: auto;
			span {
				color: #fff;
				font-size: vw(14);
				white-space: nowrap;
				margin-top: vw(2);
			}

			&:hover {
				transform: scale(1.05);
			}

			span {
				&.active {
					color: #ff9729;
				}
			}
		}

		// 2个按钮布局 - 椭圆形
		.nav-item.nav-count-2 {
			&.nav-item-0 {
				left: 6%;
				top: 35%;
			}
			&.nav-item-1 {
				right: 6%;
				top: 35%;
			}
		}

		// 3个按钮布局 - 椭圆形
		.nav-item.nav-count-3 {
			&.nav-item-0 {
				left: 50%;
				top: 10%;
				transform: translateX(-50%);
			}
			&.nav-item-1 {
				right: 1%;
				bottom: 25%;
			}
			&.nav-item-2 {
				left: 1%;
				bottom: 25%;
			}
		}

		// 4个按钮布局 - 椭圆形
		.nav-item.nav-count-4 {
			&.nav-item-0 {
				left: 28%;
				top: 16%;
				transform: translate(-50%, 0);
				&:hover {
					transform: translate(-50%, 0) scale(1.05);
				}
			}
			&.nav-item-1 {
				right: 28%;
				top: 16.2%;
				transform: translate(50%, 0);
				&:hover {
					transform: translate(50%, 0) scale(1.05);
				}
			}
			&.nav-item-2 {
				right: 0.5%;
				bottom: 40%;
			}
			&.nav-item-3 {
				left: 0.5%;
				bottom: 40%;
			}
		}

		// 5个按钮布局 - 椭圆形
		.nav-item.nav-count-5 {
			&.nav-item-0 {
				left: 50%;
				top: 10%;
				transform: translateX(-50%);
			}
			&.nav-item-1 {
				right: 6%;
				top: 35%;
			}
			&.nav-item-2 {
				right: 1%;
				bottom: 17%;
			}
			&.nav-item-3 {
				left: 1%;
				bottom: 17%;
			}
			&.nav-item-4 {
				left: 6%;
				top: 35%;
			}
		}
	}
}

.animate__zoomOutDown {
	animation-name: zoomOutDown;
}
.animate__zoomInUp {
	animation-name: zoomInUp;
}

// 添加小按钮样式

// 动画持续时间
.animate__animated {
	animation-duration: 0.5s;
}

.nav-item {
	opacity: 0;
	transition: all 0.3s;

	&.item-show {
		opacity: 1;
	}
}

.center-btn {
	opacity: 0;
	transition: all 0.3s;

	&.btn-show {
		opacity: 1;
	}
}
</style>

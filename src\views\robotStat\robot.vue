<template>
	<div class="jqr-container fullScreen">
		<div class="jqr-item" v-for="(info, $index) in jqrInfo">
			<div class="jqr-title">
				<span class="jqr-name">
					<img :src="`/src/assets/fullScreen/robotStat/robot/${info.deviceId}.png`" alt="" />
				</span>
				<span class="jqr-tab">
					<span v-for="(r, index) in dateRangeOptions" :key="index">
						<span
							:style="{
								color: r.value === info.dateRange ? 'rgb(152, 247, 215)' : '#fff',
							}"
							@click="handleDateRangeClick(r.value, info.deviceId, $index)"
						>
							{{ r.label }}
						</span>
						<i v-if="index !== dateRangeOptions.length - 1">/</i>
					</span>
				</span>
			</div>
			<div class="jqr-content">
				<div class="jqr-info">
					<span class="jqr-info-item" v-for="jqr in info.overview" :key="jqr.label">
						<img class="jqr-icon" :src="jqr.icon" alt="" />
						<span class="jqr-label">
							<span>{{ jqr.label }}</span>
							<span
								>{{ jqr.value }}<i>{{ jqr.unit }}</i></span
							>
						</span>
					</span>
				</div>
				<div class="echarts-item pl20">
					<div class="chart-title">
						<img src="../../assets/fullScreen/robotStat/robot/chart-title1.png" alt="" />
					</div>
					<Echarts
						class="echarts"
						ref="durationChartRefs"
						:id="`durationChartId${info.deviceId}`"
					/>
				</div>
				<div class="echarts-item pl20">
					<div class="chart-title">
						<img src="../../assets/fullScreen/robotStat/robot/chart-title2.png" alt="" />
					</div>
					<Echarts class="echarts" ref="mileageChartRefs" :id="`mileageChartId${info.deviceId}`" />
				</div>
				<div class="echarts-item pl20">
					<div class="chart-title">
						<img src="../../assets/fullScreen/robotStat/robot/chart-title3.png" alt="" />
					</div>
					<Echarts class="echarts" ref="taskChartRefs" :id="`taskChartId${info.deviceId}`" />
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import _ from 'lodash';
import Echarts from '/@/components/echarts/echarts.vue';
import other from '/@/utils/other';
import echartsOption from '/@/hooks/echartsOption';
import { getOverviewStat, getTrendStat } from '/@/api/robotStat';
const { durationOption, mileageOption, taskOption } = echartsOption();

const dateRangeOptions = ref<{ label: string; value: 1 | 2 }[]>([
	{
		label: '近一周',
		value: 1,
	},
	{
		label: '近一月',
		value: 2,
	},
]);

const handleDateRangeClick = (value: 1 | 2, deviceId: number, $index: number) => {
	jqrInfo[$index].dateRange = value;
	getTrendStat({ type: value, deviceId }).then(({ payload }) => {
		disposeEchartData(payload, jqrInfo[$index]);
		renderCharts($index, jqrInfo[$index]);
	});
};
const robotTemplateInfo = {
	deviceId: 591,
	dateRange: 1,
	robotName: '万象2-02',
	robotSn: 'F2230204226',
	overview: {
		totalTime: {
			icon: other.getStaticImag('fullScreen/robotStat/robot/zongshichang.png'),
			label: '总时长',
			value: 0,
			unit: 'h',
		},
		totalMileage: {
			icon: other.getStaticImag('fullScreen/robotStat/robot/zonglicheng.png'),
			label: '总里程',
			value: 0,
			unit: 'km',
		},
		totalTaskCount: {
			icon: other.getStaticImag('fullScreen/robotStat/robot/zongrenwu.png'),
			label: '总任务数',
			value: 0,
			unit: '个',
		},
	},
};
const jqrInfo = reactive<any[]>([]);

type Chart = InstanceType<typeof Echarts>;
// 时长统计
const durationChartRefs = ref<Chart[]>();
// 里程统计
const mileageChartRefs = ref<Chart[]>();
// 任务统计
const taskChartRefs = ref<Chart[]>();

const renderCharts = ($tIndex: number, _options: any) => {
	(<Chart[]>durationChartRefs.value)[$tIndex].resetOption(_options.durationOption);
	(<Chart[]>mileageChartRefs.value)[$tIndex].resetOption(_options.mileageOption);
	(<Chart[]>taskChartRefs.value)[$tIndex].resetOption(_options.taskOption);
};

const disposeEchartData = (trend: any, newRobotInfo: any) => {
	const xData: string[] = [];
	const seriesTime: number[] = [];
	const seriesMileage: number[] = [];
	const seriesTask: number[] = [];
	trend.forEach((tr: any) => {
		xData.push(tr.date.substring(5, 10));
		seriesTime.push(tr.totalTime / 3600); // 秒换小时
		seriesMileage.push(parseFloat((tr.totalMileage / 1000).toFixed(2)));
		seriesTask.push(tr.totalTaskCount || 0);
	});
	newRobotInfo.seriesTime = seriesTime;
	newRobotInfo.durationOption = durationOption(xData);

	newRobotInfo.durationOption.series.forEach((serie: any) => {
		serie.data = seriesTime;
		if (xData.length > 10) {
			serie.barWidth = 13;
			serie.symbolSize = [13, 6.5];
		}
	});

	newRobotInfo.mileageOption = mileageOption(xData, seriesMileage);
	newRobotInfo.taskOption = taskOption(xData, seriesTask);

	if (xData.length > 10) {
		newRobotInfo.durationOption.legend.itemWidth = 14;
		newRobotInfo.durationOption.legend.itemHeight = 7;

		newRobotInfo.taskOption.series.barWidth = '14px';
	}
};

onMounted(async () => {
	getOverviewStat({ type: 1 }).then(({ payload }) => {
		payload?.forEach((item: any, index: number) => {
			const newRobotInfo = Object.assign(_.cloneDeep(robotTemplateInfo), item.robot);
			const { totalTime, totalMileage, totalTaskCount } = item.overview;
			newRobotInfo.overview.totalTime.value = (totalTime / 60).toFixed(1);
			newRobotInfo.overview.totalMileage.value = (totalMileage / 1000).toFixed(1);
			newRobotInfo.overview.totalTaskCount.value = totalTaskCount;

			disposeEchartData(item.trend, newRobotInfo);

			jqrInfo.push(newRobotInfo);
			nextTick(() => {
				renderCharts(index, jqrInfo[index]);
			});
		});
	});
});

onUnmounted(() => {});

defineExpose({});
</script>

<style lang="scss" scoped>
$border-light: rgba(104, 207, 173, 0.5);
$label-color: rgb(152, 247, 215);

.jqr-container {
	width: 100%;
	height: 100%;
	.jqr-item {
		width: 100%;
		height: vh(425);
		// border: 1px solid $border-light;
		margin-bottom: vh(20);
		background: url('/src/assets/fullScreen/robotStat/title.png');

		background-repeat: no-repeat;
		background-size: 100% 100%;
		.jqr-title {
			height: vh(45);
			display: flex;
			justify-content: space-between;
			align-items: center;
			// background: url('../../assets/fullScreen/robotStat/title-bg.png');

			// background-repeat: no-repeat;
			// background-size: 26% 54%;
			// background-position: 0 48%;
			.jqr-name {
				padding-left: vw(20);
				img {
					height: vh(29);
				}
			}
			.jqr-tab {
				height: 100%;
				display: flex;
				align-items: center;
				font-size: 14px;
				padding-right: vw(20);
				span {
					cursor: pointer;
				}
				i {
					margin-left: vw(8);
					margin-right: vw(8);
				}
			}
		}
		.jqr-content {
			height: calc(100% - vh(45));
			display: flex;
			padding: 0 20px 20px;
			.jqr-info {
				width: 230px;
				height: 100%;
				display: flex;
				flex-direction: column;
				background: url('/src/assets/fullScreen/robotStat/robot-info-bg.png');
				background-size: 100% 100%;

				.jqr-info-item {
					flex: 1;
					display: flex;
					align-items: center;
					.jqr-icon {
						width: 45px;
						height: 45px;
						margin-right: 17px;
						object-fit: cover;
					}
					.jqr-label {
						flex: 1;
						height: 45px;
						display: flex;
						flex-direction: column;
						span {
							height: 33px;
							color: rgba(255, 255, 255, 1);
							font-size: 24px;
							line-height: 33px;
							font-weight: bold;
							&:first-child {
								height: 17px;
								line-height: 17px;
								font-size: 12px;
								color: $label-color;
								font-weight: normal;
							}
							i {
								font-style: normal;
								font-size: 10px;
								color: $label-color;
								margin-left: 2px;
								font-weight: normal;
							}
						}
					}
				}
			}

			.echarts-item {
				width: calc((100% - 230px) / 3);
				height: 100%;
				.chart-title img {
					height: 16px;
					transform: translateX(18%);
				}
				.echarts {
					height: calc((100% - 16px)) !important;
				}
			}
		}
	}
}

.echarts-tooltip {
	background-color: rgba(255, 255, 255, 0.9); /* 自定义背景色 */
	border-color: #333; /* 自定义边框色 */
	border-width: 1px; /* 自定义边框宽度 */
	border-style: solid; /* 自定义边框样式 */
	padding: 10px; /* 自定义内边距 */
	border-radius: 5px; /* 自定义边框圆角 */
	box-shadow: 0 0 10px rgba(0, 0, 0, 0.3); /* 自定义阴影 */
}
</style>

<template>
	<div :id="props.id" class="echarts-container"></div>
</template>

<script lang="ts" setup>
import { ref, markRaw, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
	id: { type: String, required: true },
});

const myChart = ref();

onMounted(() => {
	const chartDom = document.getElementById(props.id);
	if (chartDom) {
		myChart.value = markRaw(echarts.init(chartDom));
		window.addEventListener('resize', resizeEvent);
	}
});

const resizeEvent = () => {
	console.log('resizeEvent');
	myChart.value.resize();
};

onUnmounted(() => {
	window.removeEventListener('resize', resizeEvent);
	myChart.value && myChart.value.dispose();
});

const resetOption = (option: Object) => {
	myChart.value.setOption(option);
};

defineExpose({
	resetOption,
	myChart,
});
</script>

<style lang="scss" scoped>
.echarts-container {
	width: 100%;
	height: 100%;
}
</style>

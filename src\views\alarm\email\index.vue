<template>
  <div class="layout-pd">
    <div class="mb15" v-auths="['*:*:*', 'recipient-emails:*:*']">
      <el-button type="primary" @click="onCreate">
        <template #icon>
          <el-icon><ele-Plus /></el-icon>
        </template>
        新增
      </el-button>
      <el-button type="success" :disabled="tableRef?.selectRows.length !== 1"
        @click="onUpdateRow(null)">
        <template #icon>
          <el-icon><ele-Edit /></el-icon>
        </template>
        修改
      </el-button>
      <el-button type="danger" :disabled="tableRef?.selectRows.length === 0"
        @click="onBatchDelete">
        <template #icon>
          <el-icon><ele-Delete /></el-icon>
        </template>
        删除
      </el-button>
    </div>

    <Table v-bind="tableOptions" ref="tableRef" @pageChange="onPageChange">
      <template #enable="{ row }">
        <el-switch
          :model-value="!!row.enable"
          inline-prompt
          active-text="启用"
          @change="(value: boolean) => { onEnableChange(value, row) }"
        />
      </template>
      <template #operate="{ row }">
        <el-button size="small" text type="primary" @click="onUpdateRow(row)">
          <el-icon><ele-EditPen /></el-icon>
          修改
        </el-button>
        <el-button size="small" text type="primary" @click="onDelRow(row.id)">
          <el-icon><ele-Delete /></el-icon>
          删除
        </el-button>
      </template>
    </Table>
    <CreateDialog ref="createDialogRef" @refresh="onRefresh"></CreateDialog>
  </div>
</template>

<script setup lang="ts" name="AlarmEmail">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getEmails, deleteEmail, batchDeleteEmail, updateEmail } from '/@/api/alarm/email';
import { AUTHS } from '/@/directive/authDirective';

// 引入异步组件
const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));
const CreateDialog = defineAsyncComponent(() => import('./dialog.vue'));

const tableRef = ref<InstanceType<typeof Table>>();
const searchOptions = reactive({
  filter: {
    sort: 'id,asc',
  },
});
const tableOptions: GlobalTableOptions<EmailRow> = reactive({
  data: [],
  header: [
    {
      title: '邮箱',
      key: 'email',
      isCheck: true,
    },
    {
      title: '描述',
      key: 'remark',
      isCheck: true,
    },
    {
      title: '是否启用',
      key: 'enable',
      isCheck: true,
    },
    {
      title: '创建时间',
      key: 'createTime',
      isCheck: true,
    },
  ],
  config: {
    loading: true,
    isSelection: true,
    isSerialNo: true,
    isOperate: AUTHS(['*:*:*', 'recipient-emails:*:*']),
    operateWidth: 150, // 操作列宽
    total: 0, // 总条数
  },
  pageParams: {
    page: 1,
    size: 10,
  },
});

onMounted(() => {
  initTableData();
});


const initTableData = async () => {
  tableOptions.config.loading = true;
  try {
    const query: any = {
      page: tableOptions.pageParams?.page && tableOptions.pageParams.page - 1,
      size: tableOptions.pageParams?.size,
      ...searchOptions.filter,
    };
    const { payload } = await getEmails(query)
    tableOptions.config.loading = false;
    tableOptions.data = payload.content;
    tableOptions.config.total = payload.totalElements;
  } catch (e) {
    tableOptions.config.loading = false;
    tableOptions.config.total = 0;
    tableOptions.data = [];
  }
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
  if (resetFilter) {
    // reset filter
  }
  if (resetPage) {
    tableRef.value?.pageReset();
  } else {
    initTableData();
  }
  tableRef.value?.clearSelection();
};

const onPageChange = async (page: { pageNum: number; pageSize: number; }) => {
  if (tableOptions.pageParams) {
    tableOptions.pageParams.page = page.pageNum;
    tableOptions.pageParams.size = page.pageSize;
  }
  initTableData();
};

// 切换是否启用状态
// el-switch设置active-value或inactive-value时，会先调用change
const onEnableChange = async (value: boolean, row: EmailRow) => {
  const query = {
    id: row.id,
    email: row.email,
    enable: value ? 1 : 0,
  };
  await updateEmail(query);
  row.enable = query.enable;
  if (value) {
    ElMessage.success('防火告警通过开启成功');
  } else {
    ElMessage.success('防火告警通知已关闭');
  }
};

// # 操作
// 新增、修改
const createDialogRef = ref<InstanceType<typeof CreateDialog>>()
const onCreate = () => {
  createDialogRef.value?.openDialog();
};
const onUpdateRow = (row: EmailRow | null) => {
  if (!row) {
    row = tableOptions.data.find((item) => item.id === tableRef?.value?.selectRows[0].id) as EmailRow;
  }
  createDialogRef.value?.openDialog(row);
};
// 删除、批量删除
const onDelRow = (rowId: string) => {
  ElMessageBox({
    title: '提示',
    message: '此操作将永久删除，是否继续?',
    type: 'warning',
    showCancelButton: true,
  }).then(async () => {
    await deleteEmail(rowId);
    ElMessage.success('删除成功');
    onRefresh();
  }).catch(() => { })
};

const onBatchDelete = () => {
  ElMessageBox({
    title: '提示',
    message: '此操作将永久删除，是否继续?',
    type: 'warning',
    showCancelButton: true,
  }).then(async () => {
    const ids = tableRef?.value?.selectRows.map((item) => item.id) as string[];
    await batchDeleteEmail(ids);
    ElMessage.success('删除成功');
    onRefresh();
  }).catch(() => { })
}
</script>

<template>
	<div class="event-details-container fullScreen">
		<div class="title">
			<img src="/src/assets/fullScreen/title-name.png" alt="" draggable="false" />
		</div>
		<img class="fanhui" src="/src/assets/live/fanhui.png" alt="返回" @click="goBack" />
		<div class="main">
			<div class="tab">
				<template v-for="item in tab" :key="item.groupEventType">
					<div class="tab-item" @click="handleTabClick(item.groupEventType)">
						<img
							class="tab-item-img"
							:width="item.width"
							:src="activeTab === item.groupEventType ? item.activePicture : item.defaultPicture"
							alt=""
						/>

						<div
							class="a-line"
							:style="{ width: item.width }"
							:class="{ 'a-line-bg': activeTab === item.groupEventType }"
						></div>
					</div>
				</template>
			</div>
			<div class="search">
				<span class="search-item dark">
					选择日期：
					<el-date-picker
						v-model="state.tableData.filter.startTime"
						type="date"
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD HH:mm:ss"
						placeholder="点击选择日期"
						:teleported="false"
					/>
				</span>
				<span class="search-item">
					事件类型：
					<el-select
						v-model="state.tableData.filter.eventTypes"
						placeholder="点击选择事件类型"
						multiple
						collapse-tags
						clearable
						:teleported="false"
					>
						<el-option
							v-for="item in state.METypeOptions"
							:key="item.id"
							:label="item.name"
							:value="item.eventType"
						>
						</el-option> </el-select
				></span>
				<!-- <span
					class="button-round"
					style="margin-left: 30px; margin-right: 70px"
					@click="onSelectAll"
					>全选</span
				> -->
				<span class="button-round" @click="onRefresh(true, false)">搜索</span>
				<span class="button-round" style="margin-left: 30px" @click="onRefresh(true, true)"
					>重置</span
				>
			</div>
			<div class="table-box scrollbar-lg" v-loading="state.tableData.loading">
				<template v-if="state.tableData.data.length > 0">
					<div
						v-for="(item, $index) in state.tableData.data"
						:key="item.id"
						class="atlas-item"
						@click="onClickAtlasItem(item, $index)"
					>
						<!-- <el-checkbox class="checkbox" v-model="item.checked" @click.stop /> -->
						<span
							class="atlas-type slide-backlog"
							:key="item.id"
							:class="item.eventLevel === 2 ? 'warning' : item.eventLevel === 3 ? 'danger' : ''"
						>
							{{
								item.eventLevel === 1
									? '一般'
									: item.eventLevel === 2
									? '严重'
									: item.eventLevel === 3
									? '紧急'
									: ''
							}}
						</span>
						<!-- <div class="">{{ item.parentEventTypeName }}</div> -->
						<div class="thumb-rec">
							<el-image
								class="thumbnail"
								:src="item.pictureUrl || item.oriPictureThumbnailUrl"
								fit="contain"
								lazy
							>
								<template #placeholder>
									<div class="image-placeholder">
										<el-icon class="is-loading">
											<ele-Loading />
										</el-icon>
									</div>
								</template>
								<template #error>
									<div class="load-error">
										<img
											class="small-img-error"
											src="/src/assets/fullScreen/small-load-error.png"
											title="加载失败"
											alt=""
										/>
									</div>
								</template>
							</el-image>
						</div>
						<div class="other-content">
							<span
								v-if="item?.monitorEventDetails && item.monitorEventDetails.length"
								v-for="res in item.monitorEventDetails"
								:key="res.recResult"
							>
								{{ item.pointName }} {{ res.recResult }}
								<template v-if="activeTab === 0">
									{{ res.recResultCnt && `（${res.recResultCnt}）` }}
								</template>
							</span>
							<span>{{ extractTimePart(item.recTime, 'MM-DD HH:mm:ss') }}</span>
						</div>
					</div>
					<el-pagination
						class="mt10"
						v-model:current-page="state.tableData.pageParams.page"
						:page-sizes="[15, 30, 45]"
						background
						v-model:page-size="state.tableData.pageParams.size"
						layout="total, sizes, prev, pager, next, jumper"
						:total="state.tableData.total"
						@size-change="onHandleSizeChange"
						@current-change="onHandleCurrentChange"
						:teleported="false"
					>
					</el-pagination>
				</template>
				<div v-else class="text-loding">暂无数据</div>
			</div>
		</div>
		<div class="footer">
			<img src="/src/assets/fullScreen/footer-bg.png" alt="底部" />
		</div>
		<DetailDig
			ref="detailDigRef"
			:perviewPicList="perviewPicList"
			:perviewPicIndex="perviewPicIndex"
			:eventDatas="state.tableData.data"
			:markers="markers"
		/>
	</div>
</template>

<script setup lang="ts">
import {
	ref,
	reactive,
	onBeforeMount,
	onMounted,
	onUnmounted,
	nextTick,
	computed,
	defineAsyncComponent,
} from 'vue';
import { extractTimePart } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';
import other from '/@/utils/other';
import { useRouter, useRoute } from 'vue-router';
// import 'viewerjs/dist/viewer.css';
import { liveInfo } from '/@/stores/fullScreen';
import { formatDate } from '/@/utils/formatTime';
import { getMonitorFilesEvents, getMonitorEventTypes } from '/@/api/monitorEvents';

const DetailDig = defineAsyncComponent(() => import('./detailDig.vue'));
const detailDigRef = ref();
const emits = defineEmits(['currentIndex', 'updateStat', 'location', 'filterMarker']);
const liveStore = liveInfo();

const perviewPicList = computed(() => {
	return state.tableData.data.map((item) => item.pictureUrl || item.oriPictureUrl);
});

const perviewPicIndex = ref(0);
const router = useRouter();
const route = useRoute();

const state = reactive<ViewBaseState<MonitorEventRow>>({
	METypeOptions: [],
	tableData: {
		filter: {
			deviceType: 4,
			num: '',
			eventTypes: [], // eventType：0鸟类 1动物
			name: '', // 物种名称模糊匹配
			// speciesName: '', // 物种名称全匹配
			noResult: 0,
			startTime: '',
			endTime: '',
			sort: 'recTime,desc',
		},
		data: [],
		loading: false,
		pageParams: {
			page: 1,
			size: 15,
		},
		total: 0,
	},
	selectAll: false,
});

// tab 切换
const activeTab = ref<number | null>(null);
const tab = reactive([
	{
		defaultPicture: other.getStaticImag('eventDetails/quanbu.png'),
		activePicture: other.getStaticImag('eventDetails/a-quanbu.png'),
		groupEventType: null,
		width: 34,
	},
	{
		defaultPicture: other.getStaticImag('eventDetails/gongyuananfang.png'),
		activePicture: other.getStaticImag('eventDetails/a-gongyuananfang.png'),
		groupEventType: 2,
		width: 67,
	},
	{
		defaultPicture: other.getStaticImag('eventDetails/gongyuanzhibao.png'),
		activePicture: other.getStaticImag('eventDetails/a-gongyuanzhibao.png'),
		groupEventType: 1,
		width: 67,
	},
	{
		defaultPicture: other.getStaticImag('eventDetails/gongyuansheshi.png'),
		activePicture: other.getStaticImag('eventDetails/a-gongyuansheshi.png'),
		groupEventType: 4,
		width: 67,
	},
	{
		defaultPicture: other.getStaticImag('eventDetails/gongyuanhuanjing.png'),
		activePicture: other.getStaticImag('eventDetails/a-gongyuanhuanjing.png'),
		groupEventType: 5,
		width: 67,
	},
	{
		defaultPicture: other.getStaticImag('eventDetails/shengwuduoyangxing.png'),
		activePicture: other.getStaticImag('eventDetails/a-shengwuduoyangxing.png'),
		groupEventType: 0,
		width: 83,
	},
	{
		defaultPicture: other.getStaticImag('eventDetails/buwenmingxingwei.png'),
		activePicture: other.getStaticImag('eventDetails/a-buwenmingxingwei.png'),
		groupEventType: 3,
		width: 83,
	},
]);
const handleTabClick = (groupEventType: number | null) => {
	activeTab.value = groupEventType;
	onRefresh(true, true, true);
	if (groupEventType === null) {
		state.tableData.filter.noResult = '';
	} else {
		state.tableData.filter.noResult = 0;
	}
	getTableData();
	getMETypeOptions();
};

// 全选
const onSelectAll = () => {
	state.selectAll = !state.selectAll;
	state.tableData.data = state.tableData.data.map((item) => ({
		...item,
		checked: state.selectAll,
	}));
};

// 返回大屏页面
const goBack = () => {
	router.push('/full-screen');
};

const onClickAtlasItem = (data: MonitorEventRow, $index: number) => {
	perviewPicIndex.value = $index;
	console.log('111111111111111');
	detailDigRef.value?.onOpen($index);
};

const markers = ref<MarkerInfo[]>([]);
// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	state.tableData.data = [];
	state.tableData.total = 0;

	if (state.tableData.filter.startTime) {
		const startDate = new Date(state.tableData.filter.startTime);
		state.tableData.filter.endTime = formatDate(startDate, 'YYYY-mm-dd') + ' 23:59:59';
	} else {
		state.tableData.filter.endTime = '';
	}

	const query = {
		page: state.tableData.pageParams.page - 1,
		size: state.tableData.pageParams.size,
		...state.tableData.filter,
		groupEventType: activeTab.value,
	};
	const { payload } = await getMonitorFilesEvents(query);
	state.tableData.data = payload.content;
	state.tableData.total = payload.totalElements;
	markers.value = [];
	payload.content.forEach((item: any, index: number) => {
		const temp = liveStore.notInAMapped(item, index);
		markers.value.push(temp);
	});
	state.selectAll = false;
	state.tableData.loading = false;
};
const getMETypeOptions = async () => {
	const query = {
		groupEventType: activeTab.value,
	};
	const { payload } = await getMonitorEventTypes(query);
	state.METypeOptions = payload;
};

// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getTableData();
};
const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getTableData();
};
const onRefresh = (
	resetPage: boolean = false,
	resetFilter: boolean = false,
	tab: boolean = false
) => {
	if (resetPage) state.tableData.pageParams.page = 1;
	if (resetFilter) {
		state.tableData.filter.name = '';
		state.tableData.filter.deviceType = '';
		state.tableData.filter.num = '';
		state.tableData.filter.eventTypes = [];
		state.tableData.filter.noResult = 0;
		if (!tab) {
			state.tableData.filter.startTime = '';
			state.tableData.filter.endTime = '';
		}
	}
	getTableData();
};

onBeforeMount(async () => {});

onMounted(() => {
	NextLoading.done();
	getTableData();
	getMETypeOptions();
});

onUnmounted(() => {});
</script>

<style lang="scss" scoped>
/* @import url(); 引入css类 */

.bg-lg {
	background-image: linear-gradient(to bottom, #186047, #41a97f);
}
.event-details-container {
	position: relative;
	height: 100%;
	background-color: #000000;

	.fanhui {
		width: 84px;
		height: 35px;
		object-fit: cover;
		position: absolute;
		top: 45px;
		left: 40px;
		z-index: 10;
		cursor: pointer;
	}
	.main {
		width: 100%;
		height: calc(100% - 43px);
		padding: 100px 39px 0px;
		.tab {
			width: 100%;
			height: 23px;
			margin-bottom: 28px;
			display: flex;

			.tab-item {
				margin-right: 50px;
				cursor: pointer;
				display: flex;
				flex-direction: column;
				align-items: center;
				.tab-item-img {
					height: 23px;
					object-fit: contain;
				}
				.a-line {
					width: 100%;
					height: 4px;
					background-repeat: no-repeat;
					background-size: 100% 100%;
				}
				.a-line-bg {
					background-image: url('/src/assets/eventDetails/a-line.png');
				}
			}
		}

		.search {
			width: 100%;
			height: 34px;
			margin-bottom: 30px;
			color: #fff;
			.search-item {
				margin-right: 80px;

				.el-select {
					width: 254px;
				}
				&:deep(.el-tag) {
					@extend .bg-lg;
					color: #fff;
				}
				&:deep(.el-select-dropdown__item.selected) {
					background-color: transparent !important;
					color: rgb(104, 207, 173);
				}

				&:deep(.el-input__prefix) {
					display: none;
				}
			}

			&:deep(.el-popper.is-light .el-popper__arrow::before) {
				border: 1px solid rgba(104, 207, 173, 1) !important;
				border-bottom-color: transparent !important;
				border-right-color: transparent !important;
				background: #010b07 !important;
			}

			&:deep(.el-form-item__label) {
				color: #fff;
				font-size: 14px;
			}

			&:deep(.el-input__inner::placeholder) {
				color: #767876 !important;
				font-size: 14px;
			}

			&:deep(.el-input__wrapper) {
				border-radius: 50px !important;
				border: 1px solid rgba(104, 207, 173, 1) !important;
				background-color: transparent !important;
				box-shadow: none !important;
				padding: 0 11px !important;
			}
			&:deep(.el-input__inner) {
				--el-input-inner-height: 30px !important;
				color: rgba(255, 255, 255, 1);
			}
			&:deep(.el-select .el-input.is-focus .el-input__wrapper) {
				box-shadow: none !important;
			}
			&:deep(.el-select .el-input__inner::placeholder) {
				color: rgba(255, 255, 255, 1);
				font-size: 14px;
			}

			.el-icon :deep(svg) {
				color: rgba(255, 255, 255, 1);
			}

			&:deep(.el-select) {
				--el-select-input-focus-border-color: none !important;
			}

			.button-round {
				display: inline-block;
				width: 108px;
				height: 34px;
				border-radius: 54px;
				color: #fff;
				line-height: 34px;
				text-align: center;
				cursor: pointer;
				@extend .bg-lg;
				-webkit-user-select: none; /* Safari */
				-moz-user-select: none; /* Firefox */
				-ms-user-select: none; /* IE/Edge */
				user-select: none; /* 标准语法 */
			}
		}

		.table-box {
			height: calc(100% - 115px);
			display: flex;
			flex-wrap: wrap;
			overflow-y: auto;

			.text-loding {
				width: 100%;
				margin-top: 200px;
				text-align: center;
				font-size: 14px;
				color: #bbb;
			}
			&:deep(.el-loading-mask) {
				background-color: rgba(0, 0, 0, 0.7);
				.path {
					stroke: #68cfad;
				}
			}

			// &::-webkit-scrollbar {
			// 	width: 10px;
			// }
			// &::-webkit-scrollbar-thumb {
			// 	@extend .bg-lg;
			// 	border-radius: 0; /* 滑块的圆角 */
			// 	border-left: 6px solid transparent; /* 左边透明边框，占滚动条宽度的一半 */
			// 	opacity: 0.65;

			// 	// background-clip: content-box; /* 使背景色不延伸到边框 */
			// }
			// &::-webkit-scrollbar-track-piece {
			// 	background-color: transparent;
			// }

			&:deep(.el-pagination) {
				width: 100% !important;

				.el-popper.is-light .el-popper__arrow::before {
					border-top-color: transparent !important;
					border-left-color: transparent !important;
				}
				.el-input__wrapper {
					border: 1px solid rgb(22, 90, 67) !important;
					background-color: transparent !important;
					box-shadow: none !important;
					padding: 0 11px !important;
				}
				.el-input__inner {
					color: rgba(255, 255, 255, 1);
				}

				.is-active {
					background-color: transparent !important;
					color: #fff !important;
					&::before {
						opacity: 0.65 !important;
					}
				}
				.el-pagination__total,
				.el-pagination__jump {
					color: rgba(255, 255, 255, 0.65);
				}
				.btn-prev,
				.btn-next,
				.number,
				.more {
					position: relative;
					background: transparent;
					color: #fff !important;
					&::before {
						content: '';
						position: absolute;
						top: 0;
						left: 0;
						width: 100%;
						height: 100%;
						@extend .bg-lg;
						opacity: 0.2;
					}
				}
			}
			.atlas-item {
				position: relative;
				width: calc((100% - 40px) / 5);
				height: 248px;
				margin-right: 10px;
				margin-bottom: 0.926vh;
				background-image: url('/src/assets/eventDetails/atlas-bg.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
				padding: 10px;
				cursor: pointer;

				&:nth-last-child(-n + 5) {
					margin-bottom: 0;
				}

				.thumbnail {
					width: 100%;
					height: 189px;

					.image-placeholder {
						width: 100%;
						height: 100%;
						display: flex;
						justify-content: center;
						align-items: center;
						.is-loading {
							color: #22d69f;
							font-size: 20px;
						}
					}
					:deep(.el-image__inner) {
						&:hover {
							transform: scale(1.05);
						}
					}
				}

				.checkbox {
					position: absolute;
					top: 10px;
					left: 20px;
				}

				&:deep(.el-checkbox__inner) {
					background-color: rgba(1, 11, 7, 0.3);
					border: 1px solid rgba(104, 207, 173, 1);
				}

				/* 当鼠标悬停时改变边框颜色 */
				&:deep(.el-checkbox__inner:hover) {
					border-color: #409eff;
				}

				&:deep(.el-checkbox__inner.is-checked) {
					background-color: #409eff;
					border-color: #409eff;
				}

				.atlas-type {
					position: absolute;
					top: 20px;
					right: 20px;
					z-index: 2;
				}

				&:nth-child(5n) {
					margin-right: 0;
				}
			}

			.other-content {
				height: 25px;
				margin-top: 10px;
				color: #fff;
				display: flex;
				justify-content: space-between;
				span {
					line-height: 25px;
					&:first-child {
						font-size: 17px;
					}
					&:last-child {
						font-size: 14px;
					}
				}
			}
		}
	}

	.title {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 80px;
		img {
			display: block;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.footer {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 187px;
		// background-image: url('/src/assets/fullScreen/footer.png');
		// background-repeat: no-repeat;
		// background-size: 100% 103%;
		pointer-events: none;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
		// background-color: #fff;

		img {
			display: block;
			width: 100%;
			height: 33px;
			object-fit: cover;
		}
	}

	::v-deep(.el-picker-panel) {
		background: #000 !important;
	}

	.animated {
		-webkit-animation-duration: 1s;
		animation-duration: 1s;
		-webkit-animation-fill-mode: both;
		animation-fill-mode: both;
		-webkit-animation-iteration-count: 2; /* 针对WebKit浏览器 */
		animation-iteration-count: 2; /* 标准语法 */
	}
	.animated.infinite {
		-webkit-animation-iteration-count: infinite;
		animation-iteration-count: infinite;
	}
	.animated.hinge {
		-webkit-animation-duration: 2s;
		animation-duration: 2s;
	}
	/*the animation definition*/
	@-webkit-keyframes bounce {
		0%,
		100%,
		20%,
		53%,
		80% {
			-webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
			transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
			-webkit-transform: translate3d(0, 0, 0);
			transform: translate3d(0, 0, 0);
		}
		40%,
		43% {
			-webkit-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
			transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
			-webkit-transform: translate3d(0, -30px, 0);
			transform: translate3d(0, -30px, 0);
		}
		70% {
			-webkit-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
			transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
			-webkit-transform: translate3d(0, -15px, 0);
			transform: translate3d(0, -15px, 0);
		}
		90% {
			-webkit-transform: translate3d(0, -4px, 0);
			transform: translate3d(0, -4px, 0);
		}
	}
	@keyframes bounce {
		0%,
		100%,
		20%,
		53%,
		80% {
			-webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
			transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
			-webkit-transform: translate3d(0, 0, 0);
			-ms-transform: translate3d(0, 0, 0);
			transform: translate3d(0, 0, 0);
		}
		40%,
		43% {
			-webkit-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
			transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
			-webkit-transform: translate3d(0, -30px, 0);
			-ms-transform: translate3d(0, -30px, 0);
			transform: translate3d(0, -30px, 0);
		}
		70% {
			-webkit-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
			transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
			-webkit-transform: translate3d(0, -15px, 0);
			-ms-transform: translate3d(0, -15px, 0);
			transform: translate3d(0, -15px, 0);
		}
		90% {
			-webkit-transform: translate3d(0, -4px, 0);
			-ms-transform: translate3d(0, -4px, 0);
			transform: translate3d(0, -4px, 0);
		}
	}
	.bounce {
		-webkit-animation-name: bounce;
		animation-name: bounce;
		-webkit-transform-origin: center bottom;
		-ms-transform-origin: center bottom;
		transform-origin: center bottom;
	}
}

:deep(.el-popper) {
	.el-icon {
		color: #fff;
	}
	.el-date-picker {
		--el-datepicker-active-color: #68cfad;
		--el-datepicker-hover-text-color: #68cfad;
	}

	.el-date-picker__header-label {
		color: #fff;
	}

	.el-date-table th {
		color: #fff;
		border-bottom: solid 1px #606266;
	}
	.el-date-table td.next-month,
	.el-date-table td.prev-month {
		color: var(--el-text-color-regular);
	}

	.el-picker-panel {
		color: #fff;
	}

	.el-date-table td.today .el-date-table-cell__text {
		color: #68cfad;
		font-weight: 700;
	}
	.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
		color: #fff !important;
	}
}
</style>

<template>
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		width="480px"
		align-center
	>
		<el-form
			ref="dialogFormRef"
			:model="state.ruleForm"
			label-width="80px"
			size="large"
			label-position="right"
		>
			<el-form-item
				label="邮箱地址"
				prop="email"
				:rules="[
					{ required: true, message: '邮箱地址不能为空', trigger: 'blur' },
					{ validator: validEmail, trigger: 'blur' },
				]"
			>
				<el-input
					v-model="state.ruleForm.email"
					placeholder="请输入"
					clearable
				></el-input>
			</el-form-item>

      <el-form-item
				label="邮箱备注"
				prop="remark"
			>
				<el-input
					v-model="state.ruleForm.remark"
          type="textarea"
					placeholder="请输入"
					clearable
				></el-input>
			</el-form-item>

      <el-form-item label="是否启用" prop="enable">
        <el-switch
          v-model="state.ruleForm.enable"
          :active-value="1"
          :inactive-value="0"
          inline-prompt
          active-text="启用"
        />
        <span class="font12" style="font-style: italic">启用后检测到防火告警时，系统将自动向指定地址发送电子邮件。</span>
			</el-form-item>
		</el-form>

		<template #footer>
			<span class="dialog-footer">
				<el-button @click="onCancel">取 消</el-button>
				<el-button type="primary" @click="onSubmit">{{ state.dialog.submitTxt }}</el-button>
			</span>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';
import { createEmail, updateEmail } from '/@/api/alarm/email';
import { verifyEmail } from '/@/utils/toolsValidate';

const emits = defineEmits(['refresh']);

const dialogFormRef = ref<FormInstance>();
const state: EmailDialogState = reactive({
	ruleForm: {
		email: '',
    remark: '',
    enable: 1,
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
});

const openDialog = (row?: EmailRow) => {
	state.dialog.isShowDialog = true;
	nextTick(() => {
		dialogFormRef.value?.resetFields();
		if (row && row.id) {
			state.dialog.type = 'edit';
			state.dialog.title = '修改邮箱';
			state.dialog.submitTxt = '修 改';
			state.ruleForm = {
				id: row.id,
				email: row.email,
        remark: row.remark,
        enable: row.enable,
			};
		} else {
			state.dialog.type = 'add';
			state.dialog.title = '新增邮箱';
			state.dialog.submitTxt = '新 增';
			state.ruleForm = {
				email: '',
        remark: '',
        enable: 1,
			};
		}
	});
};

const validEmail = (rule: any, value: string, callback: Function) => {
	if (!verifyEmail(value)) {
		callback(new Error('邮箱格式不正确'));
		return;
	}
	callback();
};

const onCancel = () => {
	state.dialog.isShowDialog = false;
};

const onSubmit = () => {
	dialogFormRef.value?.validate((valid: boolean) => {
		if (!valid) return;
		if (state.dialog.type === 'add') {
			createEmail(state.ruleForm).then(() => {
				ElMessage.success('新增成功');
				onCancel();
				emits('refresh', true, true);
			});
			return;
		}
		updateEmail(state.ruleForm).then(() => {
			ElMessage.success('修改成功');
			onCancel();
			emits('refresh');
		});
	});
};

defineExpose({
	openDialog,
});
</script>

<template>
	<div class="map-layer-control" :class="{ 'is-collapsed': !isCollapsed }">
		<div class="map-layer-control-container">
			<div class="map-layer-control-button" @click="toggleLayerOptionVisible"></div>
			<transition name="fade-slide">
				<div class="map-layer-option" v-if="layerOptionVisible">
					<div
						v-for="(item, index) in options"
						:key="index"
						class="map-layer-option-item"
						@click="handleLayerSelect(index)"
						:class="{ 'border-checked': selectedLayers.includes(index) }"
					>
						<span
							class="map-layer-option-item-check"
							:class="{ checked: selectedLayers.includes(index) }"
						></span>
						<span :class="{ 'label-checked': selectedLayers.includes(index) }">
							{{ item.label }}
						</span>
					</div>
				</div>
			</transition>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, defineEmits } from 'vue';

// 定义图层选项类型
interface LayerOption {
	label: string;
	value: string;
}

// 修改为接收外部传入的图层选项
const props = defineProps({
	options: {
		type: Array as () => LayerOption[],
		required: true,
		default: () => [],
	},
	initialSelected: {
		type: Array as () => string[],
		default: () => [],
	},
	isCollapsed: {
		type: Boolean,
		default: true,
	},
});

// 定义组件事件
const emit = defineEmits(['change']);

// 根据初始选中的值来确定索引
const getInitialSelectedIndices = () => {
	if (props.initialSelected.length === 0) {
		// 如果没有传入初始值，则默认全选
		return Array.from({ length: props.options.length }, (_, i) => i);
	}

	// 根据传入的值找到对应的索引
	return props.initialSelected
		.map((value) => {
			const index = props.options.findIndex((option) => option.value === value);
			return index !== -1 ? index : -1;
		})
		.filter((index) => index !== -1);
};

// 选中的图层
const selectedLayers = ref(getInitialSelectedIndices());

// 图层选项是否可见
const layerOptionVisible = ref(false);

// 切换图层选项可见性
const toggleLayerOptionVisible = () => {
	layerOptionVisible.value = !layerOptionVisible.value;
};

// 处理图层选择
const handleLayerSelect = (index: number) => {
	// 检查图层是否已选中
	const selectedIndex = selectedLayers.value.indexOf(index);

	if (selectedIndex === -1) {
		// 未选中，添加到选中数组
		selectedLayers.value.push(index);
	} else {
		// 已选中，从选中数组中移除
		selectedLayers.value.splice(selectedIndex, 1);
	}

	// 向父组件发送选中状态变更事件及选中的图层值
	emit(
		'change',
		selectedLayers.value.map((i) => props.options[i].value)
	);
};
</script>

<style scoped lang="scss">
.map-layer-control {
	position: absolute;
	top: vw(10);
	left: vw(10);
	width: vw(36);
	height: vw(36);
	cursor: pointer;
	.map-layer-control-container {
		position: relative;
		width: 100%;
		height: 100%;
		.map-layer-control-button {
			width: 100%;
			height: 100%;
			background: url('/src/assets/hlj/map/layer-control.png') no-repeat center center;
			background-size: 100% 100%;
			transition: all 0.2s ease-in-out;

			&:hover {
				transform: scale(1.05);
				filter: brightness(1.2);
			}
		}
		.map-layer-option {
			position: absolute;
			top: 0;
			left: vw(46);
			width: vw(125);
			// height: vw(50);
			background: url('/src/assets/hlj/map/layer-control-option.png') no-repeat center center;
			background-size: 100% 100%;
			display: flex;
			flex-direction: column;
			padding: vw(10);
			.map-layer-option-item {
				height: vw(45);
				// flex: 1;
				display: flex;
				align-items: center;
				border-bottom: 1px solid rgba(255, 255, 255, 0.5);
				font-size: vw(16);
				user-select: none;
				&:last-child {
					border-bottom: none;
				}
				.map-layer-option-item-check {
					width: vw(10);
					height: vw(10);
					margin-right: vw(10);
					background-color: rgba(255, 255, 255, 0.5);
				}
				.checked {
					background-color: rgba(55, 255, 157, 1);
				}
				.label-checked {
					color: rgba(55, 255, 157, 1);
				}
			}
			.border-checked {
				border-bottom: 1px solid rgba(255, 255, 255, 1);
			}
		}
	}
}

.is-collapsed {
	transition: all 0.2s ease-in-out;
	left: vw(400);
}

.fade-slide-enter-active,
.fade-slide-leave-active {
	transition: all 0.25s cubic-bezier(0.25, 0.1, 0.25, 1);
	transform-origin: left center;
}
.fade-slide-enter-from,
.fade-slide-leave-to {
	opacity: 0;
	transform: translateX(-10px) scale(0.95);
}
</style>

<template>
	<div ref="homeRef" class="home-left-container fullScreen">
		<div class="home-left-container-main">
			<div class="home-left-container-main-z-index">
				<div class="pie-Stat">
					<img
						class="more"
						width="53"
						height="29"
						src="/src/assets/fullScreen/more.png"
						title="点击查看更多"
						alt=""
						@click="handelGoToPage('/robot-stat')"
					/>

					<div class="echarts-stat">
						<div class="echarts-tab stat">
							<div v-for="(t, index) in tab">
								<span
									:style="{
										color: t.value === pieActiveIndex ? '#22D69F' : '#fff',
									}"
									@click="handletabClick('pie', t.value)"
								>
									{{ t.label }}
								</span>
								<i v-if="index !== tab.length - 1">/</i>
							</div>
						</div>

						<Echarts class="echarts-item" ref="pieStatisticsRef" id="pie-statistics" />
					</div>
				</div>
				<div class="robot-info">
					<img
						class="more"
						width="53"
						height="29"
						src="/src/assets/fullScreen/more.png"
						title="点击查看更多"
						alt=""
						@click="handelGoToPage('/robot-detail', 'robot')"
					/>
					<div class="robot-info-title">
						<span class="robot-info-title-name">
							<el-select
								v-model="deviceId"
								placeholder="筛选"
								size="small"
								style="width: 70%"
								:fit-input-width="true"
								:teleported="false"
								@change="handleSwitchRobot"
							>
								<el-option
									v-for="(key, index) in robotsInfo.keys()"
									:label="robotsInfo.get(key)?.robotName"
									:value="key"
								/>
							</el-select>
						</span>
						<span
							class="robot-info-live"
							:class="{ disabled: robotsInfo.get(deviceId)?.state === '离线' }"
							@click="handelJumpTo"
						>
							<img
								:src="robotsInfo.get(deviceId)?.state === '离线' ? liveDisabledIcon : liveIcon"
								alt=""
								width="17"
								height="15"
							/>
							<span>实时视频</span>
						</span>
					</div>
					<div class="robot-info-main">
						<!-- <template v-if="Object.keys(robotsInfo).length">
							<div v-for="item in robotsInfo">{{  }}</div>
						</template> -->

						<span class="robot-info-main-item">
							<span class="robot-info-main-item-value">{{
								robotsInfo.get(deviceId)?.electricity
							}}</span>
							<span class="robot-info-main-item-label">电量</span>
						</span>
						<span class="robot-info-main-item">
							<span class="robot-info-main-item-value">{{ robotsInfo.get(deviceId)?.state }}</span>
							<span class="robot-info-main-item-label">状态</span>
						</span>
						<span class="robot-info-main-item">
							<span class="robot-info-main-item-value">{{
								robotsInfo.get(deviceId)?.currentDuration
							}}</span>
							<span class="robot-info-main-item-label">开机时长</span>
						</span>
						<span class="robot-info-main-item">
							<span class="robot-info-main-item-value">{{
								robotsInfo.get(deviceId)?.currentMileage
							}}</span>
							<span class="robot-info-main-item-label">行驶里程</span>
						</span>
					</div>
				</div>

				<div class="task">
					<img
						class="more"
						width="53"
						height="29"
						src="/src/assets/fullScreen/more.png"
						title="点击查看更多"
						alt=""
						@click="handelGoToPage('/task', 'task')"
					/>
					<div class="task-name">{{ taskInfo.patrolPlanName }}({{ taskInfo.robotName }})</div>
					<div class="time start-time"><span></span>开始时间：{{ taskInfo.beginTime || '-' }}</div>
					<div class="time end-time"><span></span>结束时间：{{ taskInfo.endTime || '-' }}</div>
					<div class="event-types">
						<template v-for="(item, index) in eventTypeList" :key="item.eventType">
							<div class="event-type" :style="{ backgroundImage: `url(${item[1].bgimg})` }">
								<div class="event-type-name">{{ item[1].eventTypeName }}</div>
								<div class="event-type-count">{{ item[1].alarmCount }}</div>
							</div>
						</template>
					</div>
					<template v-for="(item, index) in taskInfo.eventTypeStatus" :key="item.name">
						<div class="task-bottom" :class="item.eventTypeName === '公园环境' ? 'trash-full' : ''">
							<div class="task-bottom-title">
								{{ item.eventTypeName }}
							</div>
							<div class="task-bottom-status">
								<span><i></i>{{ item.normal.name }}: {{ item.normal.count }}</span>
								<span><i></i>{{ item.alarm.name }}: {{ item.alarm.count }}</span>
								<span><i></i>{{ item.omit.name }}: {{ item.omit.count }}</span>
							</div>
						</div>
					</template>
				</div>
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { ref, onMounted, onBeforeMount, nextTick, reactive, computed } from 'vue';
import { getMonitorEventStat, getLastTask } from '/@/api/home';
import other from '/@/utils/other';
import { useRouter } from 'vue-router';
import 'animate.css';
import Echarts from '/@/components/echarts/echarts.vue';
import { storeToRefs } from 'pinia';
import { liveInfo } from '/@/stores/fullScreen';
import liveDisabledIcon from '/@/assets/fullScreen/robot-info-live-disabled-icon.png';
import liveIcon from '/@/assets/fullScreen/robot-info-live-icon.png';
const emits = defineEmits(['switchRobot']);

const router = useRouter();
const homeRef = ref();

const liveStore = liveInfo();
const { deviceId, robotsInfo } = storeToRefs(liveStore);

const tab = ref<{ label: string; value: StatType }[]>([
	{
		label: '当天',
		value: 0,
	},
	{
		label: '近7天',
		value: 1,
	},
	{
		label: '近30天',
		value: 2,
	},
]);

const handelJumpTo = () => {
	// console.log(robotsInfo.value[deviceId.value].state);
	if (robotsInfo.value.get(deviceId.value)?.state === '离线') return;
	const currentUrl = window.location.href;
	const hashIndex = currentUrl.indexOf('#/');
	if (hashIndex) {
		const desiredUrl = currentUrl.substring(0, hashIndex + 2);
		window.open(`${desiredUrl}robot-monitor?deviceId=${deviceId.value}`, '_blank');
	}
};

// 今日统计
const pieActiveIndex = ref<StatType>(0);
const lineActiveIndex = ref<StatType>(0);

const handletabClick = (type: string, value: StatType) => {
	if (type === 'pie') {
		pieActiveIndex.value = value;
		handleOverviewStat();
	}
};
const pieStatisticsRef = ref();
const lineStatisticsRef = ref();
interface Statistics {
	count: number;
	name: string;
	percent?: number;
	value: number;
	eventLevel: number;
	color: string;
	eventType: number;
}
const seriesColors: EmptyObjectType = {
	2: '#114CFF',
	4: '#FF6000',
	1: '#F8CE6B',
	0: '#22D69F',
	3: '#9123C8',
	5: '#0BBDFF',
	qita: '#4753ff',
};

// 概览统计
const handleOverviewStat = async () => {
	const {
		payload: { total, statistics },
	} = await getMonitorEventStat({ type: pieActiveIndex.value });

	// 按事件等级（eventLevel）排序
	// 当事件类型超过7种时，仅展示前六个事件类型，其余都归[其他]
	// const sortStat = statistics.sort((a: Statistics, b: Statistics) => b.eventLevel - a.eventLevel);

	const sortStat = statistics;
	const otherData: Statistics[] = [];
	const icon = {
		align: 'left',
		width: 11,
		height: 11,
	};
	if (sortStat.length > 7) {
		const temp = sortStat.slice(6).reduce(
			(prev: Statistics, item: Statistics) => {
				prev.count += item.count;
				return prev;
			},
			{ name: '其他', count: 0 }
		);
		otherData.push(temp);
	}

	const allDatalegend: string[] = [];
	const allData = sortStat
		.slice(0, 6)
		.concat(otherData)
		.map((item: Statistics) => {
			// const name = item.name.replace(/告警|识别$/, '').slice(0, 5);
			allDatalegend.push(item.name);
			return {
				...item,
				name: item.name,
				// percent: Number(((item.count / total) * 100).toFixed(2)),
				value: item.count,
				itemStyle: {
					color: seriesColors[item.eventType],
				},
			};
		});

	const pieStatOption = {
		backgroundColor: 'transparent',
		tooltip: {
			trigger: 'item',
			axisPointer: {
				type: 'shadow',
			},
			textStyle: {
				color: '#fff',
			},
			backgroundColor: 'rgba(50,50,50,0.7)',
			borderColor: 'rgba(104, 207, 173, 1)',
			formatter: (params: any) => {
				let html = ``;
				params.data.subEventStatistics.forEach((item: any) => {
					html += `<div>${item.name}：${item.count}</div>`;
				});
				return html;
			},
		},
		legend: {
			show: true,
			type: 'scroll',
			orient: 'vertical',
			right: 4,
			padding: [30, 0, 0, 0],
			top: 'center',
			itemGap: 1,
			icon: 'none',
			pageIconColor: '#67cfac',
			pageTextStyle: {
				color: '#fff',
			},
			pageIconSize: [12, 12],
			textStyle: {
				color: '#fff',
				rich: {
					2: {
						...icon,
						backgroundColor: seriesColors[2], // 设置线的颜色
					},
					4: {
						...icon,
						backgroundColor: seriesColors[4], // 设置线的颜色
					},
					1: {
						...icon,
						backgroundColor: seriesColors[1], // 设置线的颜色
					},
					0: {
						...icon,
						backgroundColor: seriesColors[0], // 设置线的颜色
					},
					3: {
						...icon,
						backgroundColor: seriesColors[3], // 设置线的颜色
					},
					5: {
						...icon,
						backgroundColor: seriesColors[5], // 设置线的颜色
					},
					uname: {
						align: 'left',
						width: 60,
						verticalAlign: 'bottom',
					},
					unum: {
						width: 40,
						align: 'center',
						verticalAlign: 'middle',
					},
					middle: {
						width: 100,
						height: 1,
						backgroundColor: 'rgba(255, 255, 255, .2)',
						verticalAlign: 'middle',
					},
					square: {
						width: 8,
						height: 8,
						backgroundColor: 'rgba(255, 255, 255, .2)',
						borderWidth: 0,
						verticalAlign: 'middle',
					},
				},
			},
			data: allDatalegend,
			formatter(name: string) {
				const nameItem = allData.find((item: Statistics) => item.name === name);
				if (nameItem) {
					return `{${nameItem.eventType}|} {uname|${name}}{unum|${nameItem.value}}\n{middle|}{square|}`;
				}
			},
		},
		series: [
			{
				name: '统计信息',
				type: 'pie',
				left: -117,
				radius: ['34%', '45%'],
				color: seriesColors,
				label: {
					show: true,
					color: '#fff',
					position: 'outside',
					formatter: (params: any) => {
						return `{green|${params.data.value}}\n${params.name}`;
					},
					rich: {
						green: {
							padding: [3, 0],
							color: '#67cfac',
						},
					},
				},
				labelLine: {
					length: 9,
					length2: 5,
					showAbove: true,
					lineStyle: {
						color: '#67cfac',
					},
				},
				data: allData,
			},
			{
				name: '内部虚线',
				type: 'pie',
				silent: true, // 不响应和触发鼠标事件
				left: -117,
				radius: ['27%', '28%'],
				color: ['#606664'],
				labelline: {
					show: false,
				},
				label: {
					show: false,
				},
				itemStyle: {
					normal: {
						color: (a: { data: number }) => {
							if (a.data == 1) {
								return '#606664';
							}
							if (a.data == 1) {
								return 'transparent';
							}
						},
					},
				},
				data: [
					2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1,
					2, 1,
				],
			},
			{
				name: '内部实线',
				type: 'pie',
				silent: true,
				left: -117,
				radius: ['6%', '7%'],
				color: ['#67cfac'],
				labelline: {
					show: false,
				},
				label: {
					show: false,
				},
				data: [{ value: 222, name: '内部实线' }],
			},
		],
	};
	nextTick(() => {
		pieStatisticsRef.value?.myChart.clear();
		pieStatisticsRef.value?.resetOption(pieStatOption);
	});
};

const handleSwitchRobot = () => {
	const center = [
		robotsInfo.value.get(deviceId.value)?.lng,
		robotsInfo.value.get(deviceId.value)?.lat,
	];

	console.log('center', center);
	getTaskInfo();
	emits('switchRobot', center);
};

const handelGoToPage = (path: string, queryType: string = '') => {
	let routeQuery: { [key: string]: any } = {
		robot: {
			deviceId: deviceId.value,
			robotSn: robotsInfo.value.get(deviceId.value)?.robotSn,
		},
		task: {
			deviceId: deviceId.value,
			robotSn: robotsInfo.value.get(deviceId.value)?.robotSn,
			beginTime: taskInfo.beginTime,
		},
	};

	router.push({
		path,
		query: routeQuery[queryType],
	});
};
const eventTypeKeys = [2, 1, 0, 3];
const eventTypeObjects = new Map();
eventTypeKeys.forEach((item) => {
	eventTypeObjects.set(item, {
		eventTypeName: '',
		eventType: item,
		alarmCount: 0,
		bgimg: other.getStaticImag(`fullScreen/task/${item}.png`),
	});
});
const eventTypeList = computed(() => Array.from(eventTypeObjects.entries()));
const taskInfo = reactive({
	robotName: '',
	patrolPlanName: '',
	beginTime: '',
	endTime: '-',
	eventTypeList: eventTypeObjects,
	eventTypeStatus: {
		4: {
			eventTypeName: '公园设施',
			eventType: 4,
			normal: {
				name: '正常',
				count: 0,
			},
			alarm: {
				name: '路灯故障',
				count: 0,
			},
			omit: {
				name: '漏检',
				count: 0,
			},
			bgimg: other.getStaticImag('fullScreen/anfang.png'),
		},
		5: {
			eventTypeName: '公园环境',
			eventType: 5,
			normal: {
				name: '正常',
				count: 0,
			},
			alarm: {
				name: '垃圾桶满溢',
				count: 0,
			},
			omit: {
				name: '漏检',
				count: 0,
			},
			bgimg: other.getStaticImag('fullScreen/duoyangxing.png'),
		},
	} as EmptyObjectType,
});

const getTaskInfo = async () => {
	const { payload } = await getLastTask(deviceId.value);

	if (payload) {
		taskInfo.robotName = payload.robotName;
		taskInfo.patrolPlanName = payload.patrolPlanName;
		taskInfo.beginTime = payload.beginTime;
		taskInfo.endTime = payload.endTime;
		let order: number[] = [];
		payload?.statistics?.length &&
			payload.statistics.forEach((item: any) => {
				order.push(item.eventType);
				const eventData = taskInfo.eventTypeList.get(item.eventType);
				const statusData = taskInfo.eventTypeStatus[item.eventType];
				if (eventData) {
					eventData.eventTypeName = item.eventTypeName;
					eventData.alarmCount = item.alarmCount;
				} else if (statusData) {
					statusData.eventTypeName = item.eventTypeName;
					statusData.normal.count = item.normalCount;
					statusData.alarm.count = item.alarmCount;
					// 任务巡检结束，漏检=总数-正常-告警
					if (item.totalCount) {
						const omitCount = item.totalCount - item.normalCount - item.alarmCount;
						omitCount < 0 ? (statusData.omit.count = 0) : (statusData.omit.count = omitCount);
					}
				}
			});
	}
};

onBeforeMount(() => {
	getTaskInfo();
});
onMounted(() => {
	handleOverviewStat();
});
defineExpose({
	handleOverviewStat,
	getTaskInfo,
	// handleTrendStat,
});
</script>
<style lang="scss" scoped>
.home-left-container {
	// width: 523px;
	width: 390px;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	background-image: url('/src/assets/fullScreen/zuo.png');
	background-repeat: no-repeat;
	background-size: contain;
	pointer-events: auto;
	.more {
		position: absolute;
		top: 0;
		right: 20px;
		object-fit: contain;
		cursor: pointer;
	}
	.home-left-container-main {
		width: 354px;
		height: 100%;
		position: relative;
		// padding-top: 71px;
		.home-left-container-main-z-index {
			position: absolute;
			width: 100%;
			height: calc(100% - 71px - 58px);
			top: 8.8vh;
			left: 20px;
			z-index: 11;
		}
		.robot-info {
			position: relative;
			height: 25.37vh;
			margin: 2.04vh 0;
			// margin-bottom: 0.6vh;
			background-image: url('/src/assets/fullScreen/robot-bg.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			position: relative;
			padding: 4.17vh 10px 1.85vh;
			&::before {
				content: '';
				position: absolute;
				top: 0.9vh;
				left: 24px;
				width: 86px;
				height: 2.69vh;
				background-image: url('/src/assets/fullScreen/bia.png');
				background-repeat: no-repeat;
				background-size: 100%;
			}
			.more {
				top: 0.85vh;
			}
			.robot-info-title {
				height: 2.96vh;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 1.39vh;
				margin-bottom: 1.39vh;
				.robot-info-title-name {
					height: 2.96vh;
					color: rgba(255, 255, 255, 1);
					font-size: 15px;
					padding-left: 20px;

					&:deep(.el-input__wrapper) {
						border: none;
						background-color: transparent !important;
						box-shadow: none !important;
					}
					&:deep(.el-input__inner) {
						// text-align: right !important;
						color: rgba(255, 255, 255, 1);
					}
					&:deep(.el-select .el-input.is-focus .el-input__wrapper) {
						box-shadow: none !important;
					}
					&:deep(.el-select .el-input__inner::placeholder) {
						color: rgba(255, 255, 255, 1);
						font-size: 14px;
					}

					:deep(.el-select__caret) {
						color: rgba(104, 207, 173, 1);
					}

					&:deep(.el-select) {
						--el-select-input-focus-border-color: none !important;
					}
				}
				.robot-info-live {
					color: rgba(255, 255, 255, 1);
					width: 109px;
					height: 100%;
					background-image: url('/src/assets/fullScreen/robot-info-live.png');
					background-repeat: no-repeat;
					background-size: 100% 100%;
					display: flex;
					justify-content: center;
					align-items: center;
					font-style: 14px;
					cursor: pointer;
					img {
						margin-right: 6px;
					}
				}
				.disabled {
					background-image: url('/src/assets/fullScreen/robot-info-live-disabled.png');
					cursor: not-allowed;
					color: #999;
				}
			}
			.robot-info-main {
				position: relative;
				height: 14vh;
				display: flex;
				// padding-top: 0.65vh;

				.robot-info-main-item {
					flex: 1;
					background-repeat: no-repeat;
					background-size: 100% 100%;
					margin-right: 8px;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: flex-end;
					padding-bottom: 0.556vh;

					.robot-info-main-item-label {
						color: rgba(255, 255, 255, 1);
						font-size: 14px;
						margin-top: 0.833vh;
					}
					&:nth-child(1) {
						background-image: url('/src/assets/fullScreen/dianliang.png');
						color: rgba(151, 206, 255, 1);
						font-size: 20px;
					}
					&:nth-child(2) {
						background-image: url('/src/assets/fullScreen/zhuangtai.png');
						color: rgba(167, 255, 210, 1);
						font-size: 16px;
					}
					&:nth-child(3) {
						background-image: url('/src/assets/fullScreen/kaijishichang.png');
						color: rgba(74, 197, 255, 1);
						font-size: 16px;
					}
					&:nth-child(4) {
						background-image: url('/src/assets/fullScreen/licheng.png');
						margin-right: 0px;
						color: rgba(248, 206, 107, 1);
						font-size: 16px;
					}
				}
			}
		}

		.stat {
			width: 100%;
			height: 41px;
			color: #fff;
			display: flex;
			padding-right: 5px;
			justify-content: end;
			align-items: center;
			font-size: 11px;
			line-height: 41px;
			span {
				margin: 0 5px;
				cursor: pointer;
			}
			i {
				font-style: normal;
			}
		}
		.echarts-stat {
			width: 100%;
			height: 100%;
			position: relative;
			.echarts-tab {
				position: absolute;
				top: vh(5);
				right: 0;
				z-index: 2;
				height: 16px;
				line-height: 16px;
			}
			.echarts-item {
				width: 100%;
				height: 100%;
			}
		}
		.pie-Stat {
			position: relative;
			height: 24.35vh;
			padding-top: 4.17vh;
			background-color: #010c08;
			background-image: url('/src/assets/fullScreen/pie-Stat.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			&::before {
				content: '';
				position: absolute;
				top: 0.9vh;
				left: 24px;
				width: 66px;
				height: 2.69vh;
				background-image: url('/src/assets/fullScreen/gailan.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
			}
			.more {
				top: 0.85vh;
			}
			.emptyTips {
				color: #bbb;
				padding: 28px;
			}
		}

		.task {
			position: relative;
			height: 31.48vh;
			padding-top: 4.17vh;
			padding-left: 9px;
			padding-right: 9px;
			background-color: #010c08;
			background-image: url('/src/assets/fullScreen/task-bg.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			color: rgba(255, 255, 255, 1);
			font-size: 14px;

			.more {
				top: 0.85vh;
			}
			&::before {
				content: '';
				position: absolute;
				top: 0.85vh;
				left: 24px;
				width: 66px;
				height: 2.69vh;
				background-image: url('/src/assets/fullScreen/task-title.png');
				background-repeat: no-repeat;
				background-size: 100%;
			}
			.task-name {
				height: 2.04vh;
				margin-top: 17px;
				margin-bottom: 0.83vh;
				line-height: 2.04vh;
				font-size: 16px;
				font-weight: 600;
			}
			.time {
				height: 1.85vh;
				display: flex;
				align-items: center;
				span {
					display: block;
					width: 7px;
					height: 7px;
					border-radius: 50%;
					margin-right: 6px;
				}
			}
			.start-time {
				span {
					background-color: rgba(104, 207, 173, 1);
				}
				margin-bottom: 0.463vh;
			}
			.end-time {
				span {
					background-color: rgba(155, 155, 155, 1);
				}
				margin-bottom: 0.926vh;
			}

			.event-types {
				height: 4.81vh;
				margin-bottom: 0.926vh;
				display: flex;
				.event-type {
					flex: 1;
					margin-right: 10px;
					background-repeat: no-repeat;
					background-size: 100% 100%;
					padding: 0.463vh;
					&:last-child {
						margin-right: 0;
					}

					.event-type-name {
						height: 1.57vh;
						line-height: 1.57vh;
						font-size: 12px;
						text-align: center;
						margin-bottom: 0.74vh;
					}
				}
			}

			.task-bottom {
				height: 4.81vh;
				background-repeat: no-repeat;
				background-size: 100% 100%;
				background-image: url('/src/assets/fullScreen/park-equipment-bg.png');
				padding-left: 10px;
				margin-bottom: 0.926vh;
				&:last-child {
					margin-bottom: 0;
				}
				.task-bottom-title {
					margin-bottom: 0.463vh;
				}
				.task-bottom-status {
					width: 100%;
					display: flex;
					span {
						display: flex;
						align-items: center;
						margin-right: 20px;

						i {
							width: 4px;
							height: 4px;
							border-radius: 50%;
							margin-right: 5px;
							background-color: rgba(71, 194, 132, 1);
						}
						&:nth-child(2n) {
							i {
								background-color: rgba(255, 0, 0, 1);
							}
						}
						&:last-child {
							margin-right: 0;
							i {
								background-color: rgba(255, 209, 57, 1);
							}
						}
					}
				}
			}

			// .park-equipment {
			// 	margin-bottom: 0.926vh;
			// 	background-image: url('/src/assets/fullScreen/park-equipment-bg.png');
			// }

			.trash-full {
				background-image: url('/src/assets/fullScreen/trash-full-bg.png');
			}
		}
	}
}

.swiper {
	height: 100%;
	:deep(.swiper-wrapper) {
		height: 100% !important;
	}
	.swiper-slide {
		height: 100%;
	}
}
</style>

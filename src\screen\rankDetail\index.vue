<template>
	<div class="detail-page-container">
		<div class="detail-page-title">
			<img class="back" @click="onBack" src="/src/assets/sd/data-screen/more.png" alt="" />
			<Breadcrumb />
		</div>
		<div class="detail-page-filters">
			<div class="filter-group">
				<Select
					class="mr20"
					v-model="state.tableData.filter.eventType"
					:options="animalTypesStore.screenAnimalTypeOptions"
					:clearable="false"
					placeholder="请选择物种类型"
				/>
				<Select
					class="mr20"
					v-model="state.tableData.filter.protectLevel"
					:options="protectLevelOptions"
					:clearable="false"
					placeholder="请选择保护等级"
				/>
				<DatePicker
					v-model:startTime="state.tableData.filter.startDateTime"
					v-model:endTime="state.tableData.filter.endDateTime"
				/>
			</div>
			<div class="btn-group">
				<div class="hlj-btn" @click="handleSearch">搜索</div>
				<div class="hlj-btn" @click="handleReset">重置</div>
			</div>
		</div>
		<div class="detail-page-table-box detail-page-pagination detail-page-loading">
			<el-table
				:data="state.tableData.list"
				v-loading="state.loading"
				style="width: 100%"
				max-height="calc(100vh - 250px)"
				:header-cell-style="{
					color: '#fff',
					background: 'transparent',
					backgroundImage:
						'linear-gradient(to bottom, rgba(65, 169, 127, 0.05), rgba(65, 169, 127, 0.35))',
				}"
				:cell-style="{
					color: '#fff',
					borderBottom: '1px solid rgba(151, 151, 151, 1)',
				}"
				:row-style="{
					color: '#fff',
					borderBottom: '1px solid rgba(151, 151, 151, 1)',
				}"
			>
				<el-table-column label="名次">
					<template #default="{ $index }">
						{{
							$index + 1 + (state.tableData.pageParams.page - 1) * state.tableData.pageParams.size
						}}
					</template>
				</el-table-column>
				<el-table-column prop="speciesAlias" label="名称">
					<template #default="{ row }">
						{{ row.speciesAlias || row.speciesName }}
					</template>
				</el-table-column>
				<el-table-column prop="protectLevel" label="保护等级"></el-table-column>
				<el-table-column prop="speciesFrequency" label="频次" />
				<el-table-column prop="speciesNum" label="数量" />
				<el-table-column label="操作">
					<template #default="{ row }">
						<el-button type="success" link @click="viewHistory(row)" style="color: #2fdb89">
							查看物种历史
						</el-button>
					</template>
				</el-table-column>

				<template #empty> 暂无数据 </template>
			</el-table>
			<el-pagination
				v-model:current-page="state.tableData.pageParams.page"
				:page-sizes="[10, 20, 40]"
				background
				:teleported="false"
				v-model:page-size="state.tableData.pageParams.size"
				layout="total, sizes, prev, pager, next, jumper"
				:total="state.tableData.total"
				@size-change="onHandleSizeChange"
				@current-change="onHandleCurrentChange"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { reactive, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { createQuery } from '/@/screen/utils/query';
import { protectLevelOptions } from '/@/utils/constants';
import { calculateTimeRange } from '/@/utils/timeRange';
import Select from '/@/screen/component/select/index.vue';
import DatePicker from '/@/screen/component/datePicker/index.vue';
import Breadcrumb from '/@/screen/component/breadcrumb/index.vue';
import { getSpeciesRank, getDeviceRank } from '/@/api/screen';
import { useAnimalTypesStore } from '/@/stores/animalTypes';

interface RankItem {
	time: string;
	speciesName: string;
	frequency: number;
	count: number;
	photoUrl: string;
	num: number;
	speciesAlias: string;
}

interface TableState {
	loading: boolean;
	tableData: {
		filter: {
			protectLevel: string;
			startDateTime: string;
			endDateTime: string;
			eventType: string | number;
		};
		list: RankItem[];
		total: number;
		pageParams: {
			page: number;
			size: number;
		};
	};
}

const router = useRouter();
const route = useRoute();
// 常量定义
const ALL_SPECIES_TYPE = 'all';
// 使用动物类型 store
const animalTypesStore = useAnimalTypesStore();
const state = reactive<TableState>({
	loading: false,
	tableData: {
		filter: {
			protectLevel: '',
			startDateTime: '',
			endDateTime: '',
			eventType: ALL_SPECIES_TYPE,
		},
		list: [],
		total: 0,
		pageParams: {
			page: 1,
			size: 10,
		},
	},
});

const isDevice = computed(() => {
	return route.query.pagePath === 'device';
});

const getRankList = async () => {
	state.loading = true;
	const api = isDevice.value ? getDeviceRank : getSpeciesRank;
	try {
		const query: Record<string, any> = {
			page: state.tableData.pageParams.page - 1,
			size: state.tableData.pageParams.size,
			...state.tableData.filter,
		};

		if (query.eventType === ALL_SPECIES_TYPE) {
			delete query.eventType;
		}
		if (isDevice.value) {
			query.filterType = route.query.filterType;
		}

		const { payload } = await api(createQuery(query) as any);
		state.tableData.list = payload.content;
		state.tableData.total = payload.totalElements;
	} catch (error) {
		console.error('获取排名数据失败:', error);
	} finally {
		state.loading = false;
	}
};

const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getRankList();
};

const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getRankList();
};

const handleSearch = () => {
	getRankList();
};
const handleReset = () => {
	state.tableData.filter.protectLevel = '';
	state.tableData.filter.startDateTime = '';
	state.tableData.filter.endDateTime = '';
	state.tableData.filter.eventType = ALL_SPECIES_TYPE;
	state.tableData.pageParams.page = 1;
	state.tableData.pageParams.size = 10;
	state.tableData.list = [];
	state.tableData.total = 0;
	getRankList();
};

const viewHistory = (item: RankItem) => {
	console.log('查看历史:', item);
	const routeQuery = route.query;
	const query: Record<string, any> = {
		pagePath: routeQuery.pagePath,
		eventType: state.tableData.filter.eventType,
		fromRank: 'true',
	};
	if (isDevice.value) {
		query.deviceNum = item.speciesName;
	} else {
		query.speciesAlias = item.speciesAlias;
		query.filterType = routeQuery.typeFilte;
	}
	router.push({
		path: '/screen/atlas',
		query,
	});
};

const onBack = () => {
	router.back();
};

onMounted(() => {
	// 根据传入的timeType自动设置时间范围
	const timeTypeParam = route.query.timeType;
	const eventType = route.query.eventType as string;

	if (timeTypeParam && timeTypeParam !== ALL_SPECIES_TYPE) {
		// 将字符串转换为数字类型
		const timeType =
			timeTypeParam === ALL_SPECIES_TYPE ? ALL_SPECIES_TYPE : (Number(timeTypeParam) as TimeType);

		const { startDateTime, endDateTime } = calculateTimeRange(timeType);

		state.tableData.filter.startDateTime = startDateTime;
		state.tableData.filter.endDateTime = endDateTime;
	}

	if (eventType) {
		state.tableData.filter.eventType =
			eventType === ALL_SPECIES_TYPE ? ALL_SPECIES_TYPE : Number(eventType);
	}

	getRankList();
});
</script>

<style lang="scss" scoped>
@import '/@/screen/styles/dataScreen.scss';
@import '/@/screen/styles/detailPage.scss';
</style>

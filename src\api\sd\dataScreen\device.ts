import request from '/@/utils/request';

// 物种种类统计 - 数量统计
export function getNumStat(params: Object) {
  return request({
    url: `/dashboard/num/species/statistics`,
    method: 'get',
    params,
  });
}

// 素材数量统计 - 数量统计
export function getMaterialQuantity(params: Object) {
  return request({
    url: `/dashboard/material/quantity`,
    method: 'get',
    params,
  });
}

// 素材数量统计 - 趋势统计
export function getMaterialQuantityTrend(params: Object) {
  return request({
    url: `/dashboard/trend/material/quantity`,
    method: 'get',
    params,
  });
}

// 聆动
export function toLindong() {
  return request({
    url: `/lindong/index`,
    method: 'get',
  });
}

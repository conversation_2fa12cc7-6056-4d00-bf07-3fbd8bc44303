<template>
	<div class="legend" :class="{ r10: isCollapsed }">
		<div class="legend-content">
			<div class="title">图例</div>
			<slot></slot>
		</div>
		<div class="legend-button">
			<img :src="LegnedIcon" alt="" />
		</div>
	</div>
</template>

<script setup lang="ts">
import LegnedIcon from '/@/assets/hlj/map/map-btn-legend.png';

defineProps({
	isCollapsed: {
		type: Boolean,
		default: false,
	},
});
</script>

<style lang="scss" scoped>
$baseSize: 40;
$btnSize: 44;

.legend {
	display: flex;
	align-items: flex-end;
	position: absolute;
	right: vw(400 + 20);
	bottom: vw($btnSize + 27);
	cursor: pointer;
	transition: all 0.2s ease-in-out;
	.legend-content {
		display: none;
		width: vw(175);
		height: max-content;
		padding: vw(10) vw(20);
		background: rgba(9, 23, 32, 0.8);
		box-shadow: 0 0 0 2px rgba(33, 227, 221, 0.1) inset;
		.title {
			text-align: center;
			padding: vw(10) 0;
		}
	}
	.legend-button {
		width: vw($btnSize);
		height: vw($btnSize);
		background: url('../../../../assets/hlj/map/map-btn-bg.png');
		background-size: 100% 100%;
		background-position: 0 0;
		opacity: 0.9;
		img {
			width: 56%;
			margin: 50%;
			transform: translate(-50%, -50%);
		}
		&:hover {
			opacity: 1;
		}
	}
	&:hover .legend-content {
		display: block;
	}
}
.legend.r10 {
	right: vw(10) !important;
}
</style>

import { reactive } from 'vue';
export function targetTypesFun() {
  const targetTypes = reactive([
    {
      label: '设备告警',
      value: 0
    },
    {
      label: '物种鉴定',
      value: 1
    },
    {
      label: '珍稀鸟类',
      value: 2
    },
    {
      label: '特定鸟类',
      value: 3
    },
    {
      label: '内蒙古鸟类',
      value: 4
    },
    {
      label: '防火设备',
      value: 10
    },
  ]);
  return {
    data: targetTypes,
    getLabel: (value: number) => targetTypes.find(item => item.value === value)?.label || '-'
  };
};

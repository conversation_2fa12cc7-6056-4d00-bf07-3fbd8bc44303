<template>
	<div class="task-container fullScreen">
		<div class="task-item" v-for="(tItem, $tIndex) in tasks" :key="tItem.id">
			<div class="task-top">
				<div class="task-top-title">
					<img :src="tItem.titlePic" alt="" />
				</div>
				<div class="task-top-search">
					<span v-for="(r, index) in dateRangeOptions" :key="index">
						<span
							:style="{
								color: r.value === tItem.dateRange ? 'rgb(152, 247, 215)' : '#fff',
							}"
							@click="handleDateRangeClick(r.value, $tIndex)"
						>
							{{ r.label }}
						</span>
						<i v-if="index !== dateRangeOptions.length - 1">/</i>
					</span>
				</div>
			</div>
			<!-- main -->
			<div class="task-content">
				<div class="task-content-info">
					<div class="task-content-info-item" v-for="(tc, tcKey) in tItem.taskCount" :key="tcKey">
						<img class="icon" :src="tc.picture" alt="" />
						<div class="label">
							<div>任务数</div>
							<div>{{ tc.count }}</div>
						</div>
						<div class="progress">
							<div>完成率</div>
							<div class="progress-bar">
								<span class="progress-bar-rounds">
									<span
										v-for="i in 24"
										:key="i"
										:style="{
											background:
												i < Math.floor(24 * tc.rate * 0.01)
													? `${roundColors[i - 1]}`
													: i === 24 && Math.floor(24 * tc.rate * 0.01) === 24
													? `${roundColors[roundColors.length - 1]}`
													: '',
										}"
									></span>
								</span>
								<span>{{ tc.rate }}%</span>
							</div>
						</div>
					</div>
				</div>
				<div class="echarts-item pl20">
					<div class="chart-title">
						<img style="transform: translateX(50%)" :src="tItem.lineChartTitlePic" alt="" />
					</div>
					<Echarts class="echarts" ref="taskChartRefs" :id="`taskChartId${tItem.id}`" />
				</div>
				<div class="echarts-item pl20">
					<div class="chart-title">
						<img :src="tItem.barChartTitlePic" alt="" />
					</div>
					<Echarts class="echarts" ref="overflowChartRefs" :id="`overflowChartId${tItem.id}`" />
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import * as echarts from 'echarts';
import Echarts from '/@/components/echarts/echarts.vue';
import other from '/@/utils/other';
import roundColors from './roundColors';
import { getTaskOverviewStat, getTaskTrendStat } from '/@/api/robotStat';

interface Task {
	id: number;
	eventType: number;
	name: string;
	titlePic: string;
	dateRange: StatType;
	taskCount: {
		[key: string]: {
			picture: string;
			count: number;
			compled: number;
			rate: number;
		};
	};
	lineChart: {
		xAxisData: string[];
		yAxisData: number[];
	};
	barChart: {
		xAxisData: string[];
		yAxisData: number[];
	};
	lineChartTitlePic: string;
	barChartTitlePic: string;
}
const tasks: Task[] = reactive([
	{
		id: 1,
		eventType: 17,
		name: '垃圾桶',
		titlePic: other.getStaticImag('fullScreen/robotStat/task/title-ljt.png'),
		dateRange: 1,
		taskCount: {
			total: {
				picture: other.getStaticImag('fullScreen/robotStat/task/tc-total.png'),
				count: 0,
				compled: 0,
				rate: 0,
			},
			week: {
				picture: other.getStaticImag('fullScreen/robotStat/task/tc-week.png'),
				count: 0,
				compled: 0,
				rate: 0,
			},
			month: {
				picture: other.getStaticImag('fullScreen/robotStat/task/tc-month.png'),
				count: 0,
				compled: 0,
				rate: 0,
			},
			year: {
				picture: other.getStaticImag('fullScreen/robotStat/task/tc-year.png'),
				count: 0,
				compled: 0,
				rate: 0,
			},
		},
		lineChart: {
			xAxisData: [],
			yAxisData: [],
		},
		barChart: {
			xAxisData: [],
			yAxisData: [],
		},
		lineChartTitlePic: other.getStaticImag('fullScreen/robotStat/task/chart-task.png'),
		barChartTitlePic: other.getStaticImag('fullScreen/robotStat/task/chart-ljt.png'),
	},
	{
		id: 2,
		eventType: 4,
		name: '路灯',
		titlePic: other.getStaticImag('fullScreen/robotStat/task/title-ld.png'),
		dateRange: 1,
		taskCount: {
			total: {
				picture: other.getStaticImag('fullScreen/robotStat/task/tc-total.png'),
				count: 0,
				compled: 0,
				rate: 0,
			},
			week: {
				picture: other.getStaticImag('fullScreen/robotStat/task/tc-week.png'),
				count: 0,
				compled: 0,
				rate: 0,
			},
			month: {
				picture: other.getStaticImag('fullScreen/robotStat/task/tc-month.png'),
				count: 0,
				compled: 0,
				rate: 0,
			},
			year: {
				picture: other.getStaticImag('fullScreen/robotStat/task/tc-year.png'),
				count: 0,
				compled: 0,
				rate: 0,
			},
		},
		lineChart: {
			xAxisData: [],
			yAxisData: [],
		},
		barChart: {
			xAxisData: [],
			yAxisData: [],
		},
		lineChartTitlePic: other.getStaticImag('fullScreen/robotStat/task/chart-task.png'),
		barChartTitlePic: other.getStaticImag('fullScreen/robotStat/task/chart-ld.png'),
	},
	{
		id: 3,
		eventType: 3,
		name: '病虫害',
		titlePic: other.getStaticImag('fullScreen/robotStat/task/title-bch.png'),
		dateRange: 1,
		taskCount: {
			total: {
				picture: other.getStaticImag('fullScreen/robotStat/task/tc-total.png'),
				count: 0,
				compled: 0,
				rate: 0,
			},
			week: {
				picture: other.getStaticImag('fullScreen/robotStat/task/tc-week.png'),
				count: 0,
				compled: 0,
				rate: 0,
			},
			month: {
				picture: other.getStaticImag('fullScreen/robotStat/task/tc-month.png'),
				count: 0,
				compled: 0,
				rate: 0,
			},
			year: {
				picture: other.getStaticImag('fullScreen/robotStat/task/tc-year.png'),
				count: 0,
				compled: 0,
				rate: 0,
			},
		},
		lineChart: {
			xAxisData: [],
			yAxisData: [],
		},
		barChart: {
			xAxisData: [],
			yAxisData: [],
		},
		lineChartTitlePic: other.getStaticImag('fullScreen/robotStat/task/chart-task.png'),
		barChartTitlePic: other.getStaticImag('fullScreen/robotStat/task/chart-bch.png'),
	},
]);

// 切换日期
const dateRangeOptions = ref<{ label: string; value: StatType }[]>([
	{
		label: '近一周',
		value: 1,
	},
	{
		label: '近一月',
		value: 2,
	},
]);
const handleDateRangeClick = (value: StatType, $tIndex: number) => {
	tasks[$tIndex].dateRange = value;
	// renderCharts($tIndex);
	getTaskTrendStat({
		type: value,
		eventType: tasks[$tIndex].eventType,
	}).then(({ payload }) => {
		tasks[$tIndex].lineChart.xAxisData = [];
		tasks[$tIndex].lineChart.yAxisData = [];
		tasks[$tIndex].barChart.xAxisData = [];
		tasks[$tIndex].barChart.yAxisData = [];
		payload.forEach((item: any) => {
			tasks[$tIndex].lineChart.xAxisData.push(item.date.substring(5, 10));
			tasks[$tIndex].lineChart.yAxisData.push(item.totalTaskCount || 0);
			tasks[$tIndex].barChart.xAxisData.push(item.date.substring(5, 10));
			tasks[$tIndex].barChart.yAxisData.push(item.alarmPointCount || 0);
		});
		renderCharts($tIndex);
	});
};

// 每个类型图表的ref实例集合
type Chart = InstanceType<typeof Echarts>;
const taskChartRefs = ref<Chart[]>();
const overflowChartRefs = ref<Chart[]>();

const renderCharts = ($tIndex: number) => {
	(<Chart[]>taskChartRefs.value)[$tIndex].resetOption(
		getLineChartOption(tasks[$tIndex].lineChart.xAxisData, tasks[$tIndex].lineChart.yAxisData)
	);
	(<Chart[]>overflowChartRefs.value)[$tIndex].resetOption(
		getBarChartOption(tasks[$tIndex].barChart.xAxisData, tasks[$tIndex].barChart.yAxisData)
	);
};
const bgColor = 'transparent';
const axisLine = {
	show: true,
	lineStyle: {
		width: 2,
		color: 'rgba(90, 202, 176, 1)',
	},
	onZero: true,
};
// 任务数
const getLineChartOption = (xData: string[], data: number[]) => {
	return {
		backgroundColor: 'transparent',
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				lineStyle: {
					color: 'rgba(90, 202, 176, 0.5)',
				},
			},
			textStyle: {
				color: '#fff',
			},
			backgroundColor: 'rgba(50,50,50,0.7)',
			borderColor: 'rgba(104, 207, 173, 1)',
			formatter: (params: any) => {
				if (!params[0]) return;
				var axisValueLabel = params[0].axisValueLabel;
				var seriesName = params[0].seriesName;
				var value = params[0].value;
				return `<div>
                  <h3>${axisValueLabel}</h3>
                  <p>${value}${seriesName}</p>
                </div>`;
			},
		},
		legend: {
			show: true,
			icon: 'roundRect',
			right: 0,
			itemWidth: 20,
			itemHeight: 3,
			textStyle: {
				fontSize: 14,
				color: '#fff',
			},
			itemStyle: {
				color: 'rgba(90, 202, 176, 1)',
			},
		},
		grid: {
			top: '9%',
			left: '3%',
			right: 0,
			bottom: 0,
			containLabel: true,
		},
		textStyle: {
			fontSize: 14,
			color: 'rgba(255, 255, 255, .5)',
		},
		xAxis: [
			{
				type: 'category',
				axisLine,
				axisLabel: {
					margin: 10,
				},
				axisTick: { show: false },
				data: xData,
			},
		],
		yAxis: [
			{
				type: 'value',
				axisLine,
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: 'rgba(90, 202, 176, 0.5)',
					},
				},
				axisTick: { show: false },
			},
		],
		series: [
			{
				name: '任务数量',
				type: 'line',
				smooth: false, //是否平滑曲线显示
				// 			symbol:'circle',  // 默认是空心圆（中间是白色的），改成实心圆
				symbolSize: 0,
				lineStyle: {
					width: 2,
					color: 'rgba(90, 202, 176, 1)',
				},
				areaStyle: {
					//区域填充样式
					//线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
					color: new echarts.graphic.LinearGradient(
						0,
						0,
						0,
						1,
						[
							{ offset: 0, color: 'rgba(104, 207, 173, 1)' },
							{ offset: 0.95, color: 'rgba(35, 147, 200, 0)' },
						],
						false
					),
					// shadowColor: 'rgba(53,142,215, 0.9)', //阴影颜色
					// shadowBlur: 20, //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
				},
				data,
			},
		],
	};
};
// 发现溢满次数
const getBarChartOption = (xData: string[], data: number[]) => {
	const barWidth = xData.length > 7 ? '20px' : '14px';
	return {
		backgroundColor: bgColor,
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow',
			},
			textStyle: {
				color: '#fff',
			},
			backgroundColor: 'rgba(50,50,50,0.7)',
			borderColor: 'rgba(104, 207, 173, 1)',
			formatter: (params: any) => {
				if (!params[0]) return;
				var axisValueLabel = params[0].axisValueLabel;
				var seriesName = params[0].seriesName;
				var value = params[0].value;
				return `<div>
                  <h3>${axisValueLabel}</h3>
                  <p>${value}${seriesName}</p>
                </div>`;
			},
		},
		legend: {
			show: true,
			icon: 'roundRect',
			right: 0,
			itemWidth: 20,
			itemHeight: 10,
			textStyle: {
				fontSize: 14,
				color: '#fff',
			},
		},
		textStyle: {
			fontSize: 14,
			color: 'rgba(255, 255, 255, .5)',
		},
		grid: {
			top: '9%',
			left: '3%',
			right: 0,
			bottom: 0,
			containLabel: true,
		},
		xAxis: [
			{
				type: 'category',
				data: xData,
				offset: 1,
				axisLine,
				axisTick: {
					show: false,
				},
				axisLabel: {
					margin: 10,
				},
			},
		],
		yAxis: [
			{
				axisTick: {
					show: false,
				},
				axisLine,
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: 'rgba(90, 202, 176, 0.5)',
					},
				},
			},
		],
		series: [
			{
				type: 'bar',
				name: '次',
				barWidth,
				itemStyle: {
					color: new echarts.graphic.LinearGradient(
						0,
						0,
						0,
						1,
						[
							{
								offset: 0,
								color: 'rgba(104, 207, 173, 1)', // 0% 处的颜色
							},
							{
								offset: 1,
								color: 'rgba(35, 147, 200, 0)', // 100% 处的颜色
							},
						],
						false
					),
				},
				data,
			},
		],
	};
};

onMounted(async () => {
	getTaskOverviewStat({ type: 1 }).then(({ payload }) => {
		payload.forEach((item: any) => {
			const $index = tasks.findIndex((v) => v.eventType === item.eventType);

			Object.keys(item).forEach((key: string) => {
				if (tasks[$index].taskCount[key]) {
					tasks[$index].taskCount[key].count = item[key].totalTaskCount;
					tasks[$index].taskCount[key].rate = parseFloat(
						(item[key].completionRate * 100).toFixed(2)
					);
				}
			});
			// 任务数
			item.trend.forEach((tr: any) => {
				tasks[$index].lineChart.xAxisData.push(tr.date.substring(5, 10));
				tasks[$index].lineChart.yAxisData.push(tr.totalTaskCount || 0);
				tasks[$index].barChart.xAxisData.push(tr.date.substring(5, 10));
				tasks[$index].barChart.yAxisData.push(tr.alarmPointCount || 0);
			});

			nextTick(() => {
				renderCharts($index);
			});
		});
	});
	// console.log();
});
</script>

<style lang="scss" scoped>
$border-light: rgba(104, 207, 173, 0.5);
$label-color: rgb(152, 247, 215);

.task-container {
	width: 100%;
	height: 100%;
	.task-item {
		width: 100%;
		height: 360px;
		// border: 1px solid $border-light;
		margin-bottom: 20px;
		background: url('../../assets/fullScreen/robotStat/title.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		.task-top {
			height: 45px;
			display: flex;
			justify-content: space-between;
			align-items: center;

			// background-position: 0 48%;
			&-title {
				padding-left: 20px;
				img {
					height: 29px;
				}
			}
			&-search {
				padding-right: 20px;
				font-size: 14px;
				span {
					cursor: pointer;
				}
				i {
					margin-left: 8px;
					margin-right: 8px;
				}
			}
		}
		.task-content {
			height: calc(100% - 45px);
			display: flex;
			padding: 0 20px 20px;
			box-sizing: border-box;
			&-info {
				width: 620px;
				height: 100%;
				background: url('/src/assets/fullScreen/robotStat/robot-info-bg.png');
				background-size: 100% 100%;
				&-item {
					height: calc(100% / 4);
					display: flex;
					align-items: center;
					.icon {
						height: 90%;
						object-fit: cover;
					}
					.label {
						width: 24%;
						height: 45px;
						display: flex;
						flex-direction: column;
						line-height: 33px;
						color: rgba(255, 255, 255, 1);
						font-size: 24px;
						font-weight: bold;
						div:first-child {
							line-height: 17px;
							font-size: 12px;
							color: $label-color;
						}
					}
					.progress {
						flex: 1;
						line-height: 33px;
						color: rgba(255, 255, 255, 1);
						font-size: 24px;
						font-weight: bold;
						div:first-child {
							line-height: 17px;
							font-size: 12px;
							color: $label-color;
						}
						.progress-bar {
							display: flex;
							flex-wrap: wrap;
							align-items: flex-end;
							&-rounds {
								white-space: nowrap;
								margin-right: 10px;
								span {
									display: inline-block;
									width: 7px;
									height: 18px;
									background-color: rgba(45, 106, 91, 0.5);
									border-radius: 4px;
									vertical-align: middle;
									margin: 0 1.5px;
								}
							}
						}
					}
				}
			}
			.echarts-item {
				width: calc((100% - 450px) / 2);
				height: 100%;
				box-sizing: border-box;
				.chart-title {
					img {
						height: 14px;
					}
				}
				.echarts {
					height: calc(100% - 14px);
				}
			}
		}
	}
}
</style>

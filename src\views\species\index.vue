<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<el-form-item label="物种名称">
					<el-input v-model="searchOptions.filter.name" placeholder="请输入物种名称" clearable>
						<template #suffix>
							<el-icon><ele-Search /></el-icon>
						</template>
					</el-input>
				</el-form-item>
				<el-form-item label="保护等级">
					<el-select v-model="searchOptions.filter.protectFilter" placeholder="请选择" clearable>
						<el-option
							v-for="item in searchOptions.proOptions"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
			</template>

			<template #searchBtns>
				<div v-auths="['*:*:*', 'species:*:*']">
					<el-button type="primary" @click="onCreate">
						<template #icon>
							<el-icon><ele-Plus /></el-icon>
						</template>
						新增
					</el-button>
					<!-- <el-button type="success">
						<template #icon>
							<el-icon><ele-Upload /></el-icon>
						</template>
						导入
					</el-button>
					<el-button class="ml0" type="info" text size="small">
						下载导入模板？
					</el-button> -->
				</div>
			</template>
		</ViewSearch>

		<div class="main-container">
			<div class="flex-warp" v-if="state.data.length > 0" v-loading="state.loading">
				<el-row :gutter="20">
					<el-col
						:xs="24"
						:sm="12"
						:md="8"
						:lg="4"
						:xl="4"
						class="mb10"
						v-for="(v, k) in state.data"
						:key="k"
					>
						<div class="flex-warp-item">
							<div class="pro-level" v-if="v.protectLevel">
								<img src="../../assets/pretection-bg.png" alt="" />
								<span>{{ transformLevel(v.protectLevel) }}</span>
							</div>
							<div class="item-img" v-loading="v.loading">
								<el-image :src="v.imageUrl" fit="cover" lazy @click="onViewDetail(v)"></el-image>
								<div class="item-name">{{ v.specie || v.name }}</div>
								<div class="item-txt">
									<div>{{ v.className }} {{ v.order }} {{ v.family }} {{ v.genus }}</div>
								</div>
							</div>
							<div class="item-latinName">
								<div>{{ v.latinName }}</div>
								<div class="icons" v-auths="['*:*:*', 'species:*:*']">
									<el-icon :size="20" title="修改" @click.stop="onUpdate(v)">
										<ele-Edit />
									</el-icon>
									<el-icon :size="20" title="删除" @click.stop="onDelete(v.id)">
										<ele-Delete />
									</el-icon>
								</div>
							</div>
						</div>
					</el-col>
				</el-row>
			</div>
			<el-empty
				v-if="state.data.length === 0 && state.loading === false"
				description="暂无数据"
			></el-empty>

			<el-pagination
				class="mt10"
				v-show="state.data.length > 0"
				background
				:page-sizes="[12, 24, 38, 48]"
				:current-page="state.pageParams.page"
				:page-size="state.pageParams.size"
				layout="total, prev, pager, next, jumper"
				:total="state.total"
				@current-change="onHandleCurrentChange"
			>
			</el-pagination>
		</div>

		<CreateDialog
			ref="createDialogRef"
			:speciesType="speciesType"
			@refreshData="onRefresh"
		></CreateDialog>
	</div>
</template>

<script setup lang="ts" name="SpeciesPlant">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getDirectories, deleteDirectory } from '/@/api/species';
import type { Species } from './type';
import { useSpeciesType } from '/@/hooks/useSpeciesType';
import { Session } from '/@/utils/storage';

const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const CreateDialog = defineAsyncComponent(() => import('./component/dialog.vue'));

// 定义变量内容
const { currentPath, speciesType } = useSpeciesType();
const searchOptions = reactive({
	filter: {
		name: '',
		protectFilter: '',
	},
	proOptions: [
		{ label: '一级', value: '一级' },
		{ label: '二级', value: '二级' },
		{ label: '普通', value: '普通' },
	],
});
const state = reactive({
  data: <Species[]>[],
  total: 100,
  loading: false,
  pageParams: {
    page: 1,
    size: 18,
  },
});

onMounted(() => {
	initTableData();
});

const initTableData = () => {
  const query = {
    ...searchOptions.filter,
    page: state.pageParams.page - 1,
    size: state.pageParams.size,
  }
  state.loading = true;
  getDirectories(speciesType.value, query).then(({ payload }) => {
    state.loading = false;
    state.total = payload.totalElements;
    state.data = payload.content;
  }).catch(() => {
    state.loading = false;
    state.total = 0;
    state.data = [];
  });
};
const onHandleCurrentChange = (val: number) => {
	state.pageParams.page = val;
	initTableData();
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetPage) state.pageParams.page = 1;
	if (resetFilter) {
		searchOptions.filter.name = '';
		searchOptions.filter.protectFilter = '';
	}
	initTableData();
};

const transformLevel = (str: string): number => {
	const temp = ['零', '一', '二', '三', '四'];
	return temp.findIndex((item) => str.indexOf(item) > -1);
};

// 查看详情
const router = useRouter();
// const detailDialogRef = ref<InstanceType<typeof DetailDialog>>();
const onViewDetail = (item: Species) => {
  let query;
  // 植物详情根据specie查询，其他根据id查询
  if (speciesType.value !== 0) {
    query = {
      id: item.id,
    }
  } else {
    query = { name: item.specie }
  }
  Session.set('speciesDetailQuery', JSON.stringify(query));
  router.push({
    path: currentPath.value + '/detail',
  })
  // detailDialogRef.value?.show(query);
}

// 新增 和 修改
const createDialogRef = ref<InstanceType<typeof CreateDialog>>();
const onCreate = () => {
  createDialogRef.value?.show();
}
const onUpdate = (item: Species) => {
  createDialogRef.value?.show(item);
}

// 删除
const onDelete = (id: number) => {
  ElMessageBox.confirm('此操作将永久删除，是否继续?', '提示', {
    type: 'warning',
  }).then(() => {
    deleteDirectory(speciesType.value, id).then(() => {
      ElMessage.success('删除成功');
      onRefresh();
    });
  })
  .catch(() => {})
}
</script>

<style scoped lang="scss">
@import './styles/common.scss';
</style>

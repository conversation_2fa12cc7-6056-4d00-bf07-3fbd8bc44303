/* eslint-disable no-console */
import { ref, provide, onMounted } from 'vue';
import { closeSSE } from '/@/api/message';

enum MessageType {
  CONNECT = 'connect',
  MESSAGE = 'message',
}



export function useSSE(url: string = '/sse/connect') {
  const source = ref<EventSource | null>(null);
  const clientId = ref('');

  const sseData = ref<SSEData>();

  onMounted(() => {
    provide('sseData', sseData);
  });

  const createSource = () => {
    if ('EventSource' in window) {
      source.value = new EventSource(import.meta.env.VITE_API_URL + url, {});

      source.value.onopen = (event: Event) => {
        console.log('SSE连接成功', event);
      }
      source.value.onerror = (event) => {
        if (source.value?.readyState === EventSource.CLOSED) {
          console.log('SSE连接关闭');
          disconnect();
        } else {
          console.log('连接出错，自动重连', event);
        }
      };
      // 默认event类型为message
      source.value.onmessage = (event) => {
        console.log('接收到消息通知', event);
        // 在连接成功后，服务端会发送连接成功的通知消息，消息的类型为connect且包含一个clientId，记录clientId
        if (event.lastEventId === MessageType.CONNECT) {
          clientId.value = event.data;
        } else if (event.lastEventId === MessageType.MESSAGE) {
          sseData.value = event.data && JSON.parse(event.data);
        }
      };
      // 自定义event类型
      // source.value.addEventListener('', (event) => {});
    } else {
      console.log('当前浏览器不支持SSE');
    }
  }
  const connect = () => {
    if (!source.value || source.value.readyState === 2) {
      createSource();
    }
  };
  // 断开SSE
  const disconnect = () => {
    source.value?.close();
    source.value = null;
    if (clientId.value) {
      closeSSE(clientId.value);
      clientId.value = '';
    }
  };
  // 重连
  const reconnect = () => {
    disconnect();
    createSource();
  };

  return {
    sseData,
    connect,
    disconnect,
    reconnect,
  };
}
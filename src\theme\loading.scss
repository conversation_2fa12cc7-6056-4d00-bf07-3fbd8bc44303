// loading样式1
.loader-wrapper {
	width: 100%;
	height: 100%;
}
.loader-wrapper .loader-wrapper-box {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
.loader-wrapper .loader-wrapper-box-warp {
	width: 80px;
	height: 80px; 
}
.loader-wrapper .loader-wrapper-box-warp .loader-wrapper-box-item {
	width: 33.333333%;
	height: 33.333333%;
	background: var(--el-color-primary);
	float: left;
	animation: loader-wrapper-animation 1.2s infinite ease;
	border-radius: 1px;
}
.loader-wrapper .loader-wrapper-box-warp .loader-wrapper-box-item:nth-child(7) {
	animation-delay: 0s;
}
.loader-wrapper .loader-wrapper-box-warp .loader-wrapper-box-item:nth-child(4),
.loader-wrapper .loader-wrapper-box-warp .loader-wrapper-box-item:nth-child(8) {
	animation-delay: 0.1s;
}
.loader-wrapper .loader-wrapper-box-warp .loader-wrapper-box-item:nth-child(1),
.loader-wrapper .loader-wrapper-box-warp .loader-wrapper-box-item:nth-child(5),
.loader-wrapper .loader-wrapper-box-warp .loader-wrapper-box-item:nth-child(9) {
	animation-delay: 0.2s;
}
.loader-wrapper .loader-wrapper-box-warp .loader-wrapper-box-item:nth-child(2),
.loader-wrapper .loader-wrapper-box-warp .loader-wrapper-box-item:nth-child(6) {
	animation-delay: 0.3s;
}
.loader-wrapper .loader-wrapper-box-warp .loader-wrapper-box-item:nth-child(3) {
	animation-delay: 0.4s;
}
@keyframes loader-wrapper-animation {
	0%,
	70%,
	100% {
		transform: scale3D(1, 1, 1);
	}
	35% {
		transform: scale3D(0, 0, 1);
	}
}


// loading样式2
.loader-wrapper {
	background: var(--next-bg-loadingBg);
	position: relative;
}

.loader-wrapper .loader {
	width: 150px;
	height: 150px;
	position: absolute;
	left: calc(50% - 75px);
	top: calc(50% - 75px);
	border: 3px solid transparent;
	border-top-color: #fff;
	border-radius: 50%;
	transform-origin: center;
	animation: rotate 2s linear infinite;

	&::before {
		content: "";
		position: absolute;
		top: 5px;
		left: 5px;
		right: 5px;
		bottom: 5px;
		border-radius: 50%;
		border: 3px solid transparent;
		border-top-color: #FFF;
		animation: rotate 3s linear infinite;
		-webkit-animation: rotate 3s linear infinite;
	}

	&::after {
		content: "";
		position: absolute;
		top: 15px;
		left: 15px;
		right: 15px;
		bottom: 15px;
		border-radius: 50%;
		border: 3px solid transparent;
		border-top-color: #FFF;
		animation: rotate 1.5s linear infinite;
		-webkit-animation: rotate 1.5s linear infinite;
	}
}

@keyframes rotate {
	0% {
		transform: rotate(0deg);
		-moz-transform: rotate(0deg);
		-ms-transform: rotate(0deg);
		-o-transform: rotate(0deg);
		-webkit-transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
		-webkit-transform: rotate(360deg);
		-moz-transform: rotate(360deg);
		-ms-transform: rotate(360deg);
		-o-transform: rotate(360deg);
	}
}

.loader-wrapper .loader-title {
	width: 100%;
	text-align: center;
	line-height: 30px;
	position: absolute;
	top: 60%;
	font-family: 'Open Sans';
	color: #FFF;
	font-size: 19px;
}
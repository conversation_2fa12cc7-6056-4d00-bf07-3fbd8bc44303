<template>
	<el-form
		ref="ruleFormRef"
		size="large"
		:rules="formRules"
		:model="state.ruleForm"
		class="login-content-form"
	>
		<el-form-item class="login-animation1" prop="principal">
			<el-input
				text
				placeholder="用户名"
				v-model="state.ruleForm.principal"
				clearable
				autocomplete="off"
			>
				<template #prefix>
					<el-icon class="el-input__icon font25"><ele-User /></el-icon>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation2" prop="password">
			<el-input
				type="password"
				placeholder="密码"
				v-model="state.ruleForm.password"
				autocomplete="off"
				show-password
			>
				<template #prefix>
					<el-icon class="el-input__icon font25"><ele-Unlock /></el-icon>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation3" prop="captcha.response">
			<el-col :span="15">
				<el-input
					v-model="state.ruleForm.captcha.response"
					text
					maxlength="5"
					placeholder="请输入验证码"
					clearable
					autocomplete="off"
					@keyup.enter.native="onSignIn"
				>
					<template #prefix>
						<el-icon class="el-input__icon font25"><ele-Key /></el-icon>
					</template>
				</el-input>
			</el-col>
			<el-col :span="1"></el-col>
			<el-col :span="8">
				<img
					class="login-content-code"
					:src="'data:image/png;base64,' + state.captchaInfo.data"
					@click="getCaptchaInfo"
				/>
			</el-col>
		</el-form-item>
		<el-form-item class="login-animation4">
			<el-button
				type="primary"
				class="login-content-submit"
				:loading="state.btnLoading"
				round
				v-waves
				@click="onSignIn"
			>
				<span>登 录</span>
			</el-button>
		</el-form-item>
	</el-form>
</template>

<script setup lang="ts" name="loginAccount">
import { ref, reactive, computed, onBeforeMount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import type { FormInstance } from 'element-plus';
import { storeToRefs } from 'pinia';
import { useThemeConfig } from '/@/stores/themeConfig';
import { initFrontEndControlRoutes } from '/@/router/frontEnd';
import { initBackEndControlRoutes } from '/@/router/backEnd';
import { Session } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { NextLoading } from '/@/utils/loading';
import { signIn, getCaptcha, verifyCaptcha } from '/@/api/auth';

// 定义变量内容
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const route = useRoute();
const router = useRouter();
const state = reactive({
	ruleForm: {
		principal: '',
		password: '',
		captcha: {
			challenge: '',
			response: '',
		},
	},
	captchaInfo: {
		// 验证码信息
		challenge: '', // 本次验证的唯一标识
		data: '', // 图片文件，base64编码
	},
	btnLoading: false,
});
const ruleFormRef = ref<FormInstance>();
const formRules = {
	principal: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
	password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
	'captcha.response': [{ required: true, message: '验证码不能为空', trigger: 'blur' }],
};

onBeforeMount(() => {
	if (import.meta.env.MODE === 'development') {
		state.ruleForm.principal = 'admin';
		state.ruleForm.password = 'rObus0ft';
	}
	getCaptchaInfo();
});

const getCaptchaInfo = async () => {
	state.ruleForm.captcha.response = '';
	const { payload } = await getCaptcha();
	state.captchaInfo = payload;
	state.ruleForm.captcha.challenge = payload.challenge || '';
};

// 时间获取
const currentTime = computed(() => {
	return formatAxis(new Date());
});
// 登录
const onSignIn = () => {
	ruleFormRef.value?.validate(async (vaild) => {
		if (!vaild) return;
		// 调用登录接口前，先进行第一次验证码校验
		try {
			await verifyCaptcha(state.ruleForm.captcha);
			if (!themeConfig.value.isRequestRoutes) {
				// 前端控制路由，2、请注意执行顺序
				const isNoPower = await initFrontEndControlRoutes();
				signInSuccess(isNoPower);
			} else {
				// 模拟后端控制路由，isRequestRoutes 为 true，则开启后端控制路由
				// 添加完动态路由，再进行 router 跳转，否则可能报错 No match found for location with path "/"
				const isNoPower = await initBackEndControlRoutes();
				// 执行完 initBackEndControlRoutes，再执行 signInSuccess
				signInSuccess(isNoPower);
			}
		} catch (error: any) {
			// 校验失败时，如果错误码`CAPT_0000（验证码输入错误）`，则允许用户继续输入；否则表示验证码失效需刷新验证码；
			if (!(error.data && error.data.errorCode === 'CAPT_0000')) {
				getCaptchaInfo();
			}
		}
	});
};
// 登录成功后的跳转
const signInSuccess = async (isNoPower: boolean | undefined) => {
	if (isNoPower) {
		ElMessage.warning('抱歉，您没有登录权限');
		Session.clear();
	} else {
		// 初始化登录成功时间问候语
		let currentTimeInfo = currentTime.value;
		state.btnLoading = true;
		signIn(state.ruleForm)
			.then(() => {
				// Session.set('token', payload.accessToken);
				state.btnLoading = false;
				// 登录成功，跳到转首页
				// 如果是复制粘贴的路径，非首页/登录页，那么登录成功后重定向到对应的路径中
				if (route.query?.redirect) {
					router.push({
						path: <string>route.query?.redirect,
						query:
							Object.keys(<string>route.query?.params).length > 0
								? JSON.parse(<string>route.query?.params)
								: '',
					});
				} else {
					router.push('/screen');
				}
				// 登录成功提示
				const signInText = '欢迎回来！';
				ElMessage.success(`${currentTimeInfo}，${signInText}`);
				// 添加 loading，防止第一次进入界面时出现短暂空白
				NextLoading.start();
			})
			.catch(() => {
				state.btnLoading = false;
				getCaptchaInfo();
			});
	}
};
</script>

<style scoped lang="scss">
.login-content-form {
	margin-top: 20px;
	@for $i from 1 through 4 {
		.login-animation#{$i} {
			opacity: 0;
			animation-name: error-num;
			animation-duration: 0.5s;
			animation-fill-mode: forwards;
			animation-delay: calc($i/10) + s;
		}
	}
	.login-content-password {
		display: inline-block;
		width: 20px;
		cursor: pointer;
		&:hover {
			color: #909399;
		}
	}
	.login-content-code {
		width: 100%;
		height: 48px;
		margin-top: calc((64px - 48px) / 2);
		cursor: pointer;
	}
	.login-content-submit {
		width: 100%;
		letter-spacing: 2px;
		font-weight: 300;
		margin-top: 15px;
	}

	&:deep(.el-input__suffix) {
		.el-icon {
			font-size: 20px;
		}
	}

	&:deep(.el-input__wrapper) {
		background-color: transparent;
		border-radius: 32px;
		height: 64px;
		.el-input__inner {
			font-size: 16px;
			color: #fff;
		}
	}
	&:deep(.el-button) {
		background-color: var(--next-bg-menuActiveColor);
		height: 64px;
		border-radius: 32px;
		color: rgba(255, 255, 255, 1);
		font-size: 18px;
	}
}

:deep() {
	input:-webkit-autofill {
		-webkit-box-shadow: 0 0 0 0px transparent inset !important;
	}

	input:-internal-autofill-previewed,
	input:-internal-autofill-selected {
		-webkit-text-fill-color: #ffffff !important;
		transition: background-color 5000s ease-in-out 0s !important;
	}

	input:-internal-autofill-selected {
		transition: background-color 5000s ease-in-out 0s !important;
		background-color: transparent !important;
	}
}
</style>

import request from '/@/utils/request';

/**
 * @method getEmails 分页查询邮件列表
 * @method createEmail 新增
 * @method updateEmail 修改
 * @method deleteEmail 删除
 * @method batchDeleteEmail 批量删除
 */

export function getEmails(params?: Object) {
  return request({
    url: '/recipient-emails',
    method: 'get',
    params,
  });
}

export function createEmail(data: Object) {
  return request({
    url: `/recipient-emails`,
    method: 'post',
    data,
  });
}

export function updateEmail(data: EmptyObjectType) {
  return request({
    url: `/recipient-emails/${data.id}`,
    method: 'patch',
    data,
  });
}

export function deleteEmail(id: string) {
  return request({
    url: `/recipient-emails/${id}`,
    method: 'delete',
  });
}

export function batchDeleteEmail(ids: string[]) {
  return request({
    url: '/recipient-emails/batch',
    method: 'delete',
    data: ids
  });
}
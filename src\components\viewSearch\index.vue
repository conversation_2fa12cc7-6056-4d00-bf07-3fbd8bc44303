<template>
	<div class="view-search-container mb15">
		<el-form v-show="searchShow" :inline="true">
			<slot name="searchFilters"></slot>
			<el-form-item class="rightFiltrate">
				<el-button type="primary" @click="onSearch">
					<template #icon>
						<el-icon><ele-Search /></el-icon>
					</template>
					搜索
				</el-button>
				<el-button @click="onReset">
					<template #icon>
						<el-icon><ele-Refresh /></el-icon>
					</template>
					重置
				</el-button>
				<slot name="rightFiltrate"></slot>
			</el-form-item>
		</el-form>

		<div>
      <slot name="searchBtns"></slot>
    </div>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 定义变量内容
const searchShow = ref(true);

const emits = defineEmits(['onRefreshData']);

const onSearch = () => {
	emits('onRefreshData', true, false);
};
const onReset = () => {
	emits('onRefreshData', true, true);
};
</script>

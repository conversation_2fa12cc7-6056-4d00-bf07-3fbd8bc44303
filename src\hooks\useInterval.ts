import { onMounted, onBeforeUnmount } from 'vue';

export function useInterval(handler: Function, delay: number = 20 * 1000): void {
  let interval: NodeJS.Timeout | null = null;
  onMounted(() => {
    interval = setInterval(() => {
      handler();
    }, delay);
  });
  onBeforeUnmount(() => {
    if (interval) {
      clearInterval(interval);
      interval = null;
    }
  });
}

// 对比新旧事件列表，标记出新列表中存在且旧列表中不存在数据
export function markNewData(newArr: any[], oldArr: any[], key: string = 'id'): any[] {
  // `isNewData = true`，新发现
  if (oldArr.length === 0) {
    return newArr.map((item: any, $index: number) => {
      return {
        ...item,
        isNewData: $index === 0,
      }
    });
  }
  // 对比新旧数据列表，标记新发现
  const oldKeys = oldArr.map((item) => {
    item.isNewData = false;
    return item[key];
  });
  return newArr.map((item: any) => {
    return {
      ...item,
      isNewData: !oldKeys.includes(item[key]),
    }
  });
}
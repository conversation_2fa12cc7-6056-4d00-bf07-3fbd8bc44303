<template>
	<div class="toggle-buttons">
		<div
			:class="props.mainType === item.value ? 'is-active' : ''"
			v-for="item in toggleButtons"
			:key="item.value"
			@click="onTypeChange(item.value)"
		>
			{{ item.label }}
		</div>
	</div>
</template>

<script setup lang="ts">
const props = defineProps<{
	mainType: number;
}>();
const emits = defineEmits<{
	(e: 'mainTypeChange', value: number): void;
}>();

const toggleButtons = [
	{ label: '地图', value: 1 },
	{ label: '精选', value: 2 },
];
const onTypeChange = (value: number) => {
	emits('mainTypeChange', value);
};
</script>

<style lang="scss" scoped>
.toggle-buttons {
	display: flex;
	div {
		width: vw(70);
		height: vw(30);
		text-align: center;
		line-height: vw(30);
		font-family: 'ALiMaMaShuHeiTi';
		letter-spacing: 1px;
		color: rgba(255, 255, 255, 0.7);
		cursor: pointer;
		margin-left: vw(10);
		&:hover {
			color: rgba(255, 255, 255, 1);
		}
	}
	div.is-active {
		color: rgba(255, 255, 255, 1);
		background: url('/@/assets/sd/data-screen/box-bg1.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
	}
}
</style>

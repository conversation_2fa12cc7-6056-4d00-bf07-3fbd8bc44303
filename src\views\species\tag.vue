<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<el-form-item label="物种名称">
					<el-input v-model="searchOptions.filter.name" placeholder="请输入物种名称" clearable>
						<template #suffix>
							<el-icon><ele-Search /></el-icon>
						</template>
					</el-input>
				</el-form-item>
			</template>
		</ViewSearch>

		<div class="main-container">
			<div class="flex-warp" v-if="state.data.length > 0" v-loading="state.loading">
				<el-row :gutter="20">
					<el-col
						:xs="24"
						:sm="12"
						:md="8"
						:lg="4"
						:xl="4"
						class="mb10"
						v-for="(v, k) in state.data"
						:key="k"
					>
						<div class="flex-warp-item">
							<div class="item-img">
								<el-image :src="v.imageUrl" fit="cover" lazy></el-image>
								<div class="item-name">{{ v.name }}</div>
							</div>
							<div class="item-latinName">
								<div>{{ v.alias }}</div>
								<div class="icons" v-auths="['*:*:*', 'species:*:*']">
									<el-icon :size="20" title="删除" @click.stop="onDelete(v.name)">
										<ele-Delete />
									</el-icon>
								</div>
							</div>
						</div>
					</el-col>
				</el-row>
			</div>
			<el-empty
				v-if="state.data.length === 0 && state.loading === false"
				description="暂无数据"
			></el-empty>

			<el-pagination
				class="mt10"
				v-show="state.data.length > 0"
				background
				:page-sizes="[12, 24, 38, 48]"
				:current-page="state.pageParams.page"
				:page-size="state.pageParams.size"
				layout="total, prev, pager, next, jumper"
				:total="state.total"
				@current-change="onHandleCurrentChange"
			>
			</el-pagination>
		</div>

	</div>
</template>

<script setup lang="ts" name="SpeciesPlant">
import { reactive, defineAsyncComponent, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getAnimals, deleteAnimalTag, deleteAnimalResidencyType } from '/@/api/species';
import type { Animal } from './type';

const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));

// 定义变量内容
const props = defineProps<{
  tag?: number, // 物种标签
  residencyType?: number, // 
}>();
const searchOptions = reactive({
	filter: {
		name: '',
	},
});
const state = reactive({
  data: <Animal[]>[],
  total: 100,
  loading: false,
  pageParams: {
    page: 1,
    size: 18,
  },
});

onMounted(() => {
	initTableData();
});

const initTableData = () => {
  const query = {
    page: state.pageParams.page - 1,
    size: state.pageParams.size,
    name: searchOptions.filter.name,
    tag: props.tag,
    residencyType: props.residencyType,
  }
  state.loading = true;
  getAnimals(query).then(({ payload }) => {
    state.loading = false;
    state.total = payload.totalElements;
    state.data = payload.content;
  }).catch(() => {
    state.loading = false;
    state.total = 0;
    state.data = [];
  });
};
const onHandleCurrentChange = (val: number) => {
	state.pageParams.page = val;
	initTableData();
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetPage) state.pageParams.page = 1;
	if (resetFilter) {
		searchOptions.filter.name = '';
	}
	initTableData();
};

// 删除物种标签或居留类型
const onDelete = (name: string) => {
  ElMessageBox.confirm('此操作将永久删除，是否继续?', '提示', {
    type: 'warning',
  }).then(() => {
    if (typeof props.tag === 'number') {
      const data = {
        name,
        tag: props.tag,
      };
      deleteAnimalTag(data).then(() => {
        ElMessage.success('删除成功');
        onRefresh();
      });
    } else if (typeof props.residencyType === 'number') {
      const data = {
        name,
        residencyType: props.residencyType,
      };
      deleteAnimalResidencyType(data).then(() => {
        ElMessage.success('删除成功');
        onRefresh();
      });
    }
  })
  .catch(() => {})
}
</script>

<style scoped lang="scss">
@import './styles/common.scss';
</style>

<template>
	<div class="home-right-container">
		<div class="home-right-container-main">
			<div class="home-right-container-main-z-index">
				<div class="home-right-container-title">
					<img
						class="more"
						width="53"
						height="29"
						src="/src/assets/fullScreen/more.png"
						title="点击查看更多"
						alt=""
						@click="() => router.push('/event-details')"
					/>
				</div>

				<ul
					class="swiper-slide-container scrollbar-lg"
					ref="swiperRef"
					v-infinite-scroll="load"
					:infinite-scroll-immediate="false"
					:infinite-scroll-disabled="disabled"
					:infinite-scroll-distance="17"
				>
					<li
						class="swiper-slide"
						v-for="(item, index) in swiperList"
						:key="item.id"
						:data-index="index"
						:class="{ 'highlight-class': item.id === data.activeEventId }"
						@click.stop="handleLocationClick(item, index)"
					>
						<div class="swiper-slide-img" @click.stop>
							<span
								class="slide-backlog"
								:key="item.id"
								:class="item.eventLevel === 2 ? 'warning' : item.eventLevel === 3 ? 'danger' : ''"
							>
								{{
									item.eventLevel === 1
										? '一般'
										: item.eventLevel === 2
										? '严重'
										: item.eventLevel === 3
										? '紧急'
										: ''
								}}
							</span>
							<el-image
								ref="imgRef"
								title="点击放大图片"
								style="width: 100%; height: 100%"
								:src="item.picture"
								:preview-teleported="true"
								:zoom-rate="1.2"
								:max-scale="7"
								:min-scale="0.2"
								fit="contain"
								@click="handlePreview(item, index)"
							>
								<template #placeholder>
									<div class="image-placeholder">
										<el-icon class="is-loading">
											<ele-Loading />
										</el-icon>
									</div>
								</template>
								<template #error>
									<div class="load-error">
										<img
											class="small-img-error"
											src="/src/assets/fullScreen/small-load-error.png"
											title="加载失败"
											alt=""
										/>
									</div>
								</template>
							</el-image>
						</div>
						<div class="swiper-slide-info">
							<!-- <div class="swiper-slide-info-title">{{ index }}</div> -->
							<div class="swiper-slide-info-title">{{ item.pointName }}{{ item.recResult }}</div>
							<div class="swiper-slide-info-type">
								<img
									src="/src/assets/fullScreen/type.png"
									alt=""
									style="width: 15px; height: 15px"
								/>
								{{ item.parentEventTypeName }}
							</div>
							<div class="swiper-slide-info-time">
								{{ extractTimePart(item.recTime, 'MM-DD HH:mm:ss') }}
							</div>
						</div>
					</li>
					<p
						v-if="data.loading"
						class="text-loding"
						:class="{ mt80: monitorParams.page === 0 }"
						style="color: #22d69f"
					>
						加载中...
					</p>
					<p
						v-else-if="noMore"
						class="text-loding"
						:class="{ mt80: totalElements === 0 }"
						:style="{}"
					>
						{{ totalElements ? '没有更多了' : '暂无数据' }}
					</p>
				</ul>
			</div>
		</div>
	</div>
	<div v-if="dragData.show" v-drag class="viewer-remark">
		{{ dragData.remark }}
	</div>
	<template v-if="perviewPicList.length > 0">
		<el-image-viewer
			v-if="showViewer"
			ref="imageViewer"
			:url-list="perviewPicList"
			:z-index="9999"
			:infinite="true"
			:initial-index="data.initialIndex"
			@switch="handleImageSwitch"
			@close="handleImageClose"
		/>
	</template>
</template>
<script setup lang="ts">
import { ref, onBeforeMount, onUnmounted, reactive, computed, watch, nextTick } from 'vue';
import { extractTimePart } from '/@/utils/formatTime';
import { ElMessage } from 'element-plus';
import { storeToRefs } from 'pinia';
import { getMonitorEvent } from '/@/api/home';
import { useRouter } from 'vue-router';
import { liveInfo } from '/@/stores/fullScreen';
const router = useRouter();
const emits = defineEmits(['currentIndex', 'updateStat', 'location', 'updateMarker']);
const liveStore = liveInfo();

const dragData = ref({
	remark: '',
	show: false,
});
const showViewer = ref(false);
const { legendFilterList } = storeToRefs(liveStore);

const swiperList = ref<MarkerInfo[]>([]);
const swiperRef = ref();
const data = reactive({
	activeEventId: '', // 高亮事件id
	eventOptions: [],
	eventType: -1,
	loading: true,
	initialIndex: -1,
});

const handleLocationClick = (info: MarkerInfo, index: number) => {
	data.activeEventId = info.id;
	data.initialIndex = index;
	if (info.position.length) {
		emits('location', info);
	} else {
		ElMessage.error('位置异常');
	}
};

const handleImageSwitch = (index: number) => {
	const item = swiperList.value[index];
	console.log('handleImageSwitch', data);
	dragData.value.show = false;
	if (item.groupEventType === 1) {
		dragData.value.remark = item.remark;

		nextTick(() => {
			dragData.value.show = true;
		});
	}
	data.activeEventId = item.id;
};

const handleImageClose = () => {
	data.activeEventId = '';
	showViewer.value = false;
	dragData.value.show = false;
	data.initialIndex = -1;
};

const imgRef = ref();
const imageViewer = ref();
const handlePreview = (item: MarkerInfo, index: number) => {
	console.log('handlePreview', index);
	data.initialIndex = index;
	data.activeEventId = item.id;
	showViewer.value = true;
	if (item.groupEventType === 1) {
		// 植保类型在预览大图片的时候显示大模型识别出的病害信息（remark）
		dragData.value.show = true;
		dragData.value.remark = item.remark;
	}
	nextTick(() => {
		imageViewer?.value.setActiveItem(data.initialIndex);
	});
};

type MParams = {
	page: number;
	size: number;
	eventTypes: number[];
};
const monitorParams: MParams = reactive({
	page: -1,
	size: 10,
	eventTypes: legendFilterList.value,
});

const totalElements = ref(0);
// 没有数据了
const noMore = computed(() => {
	return monitorParams.page * monitorParams.size >= totalElements.value;
});
// 禁用下拉加载
const disabled = computed(() => {
	return data.loading || noMore.value;
});

const load = () => {
	data.loading = true;
	monitorParams.page += 1;
	getMonitorEvent(monitorParams)
		.then((result: any) => {
			console.log('page', result.payload.content);
			totalElements.value = result.payload.totalElements;
			result.payload.content.forEach((item: any, index: number) => {
				const temp = liveStore.notInAMapped(item, index);
				console.log(temp);
				swiperList.value.push(temp);
			});
			data.loading = false;
		})
		.catch(() => {
			data.loading = false;
		});
};
// 筛选
const onChange = () => {
	monitorParams.eventTypes = legendFilterList.value;
	monitorParams.page = -1;
	data.activeEventId = '';
	swiperList.value = [];
	load();
	emits('updateMarker', legendFilterList.value);
};

const perviewPicList = computed(() => {
	return swiperList.value.map((item) => item.picture);
});

watch(
	() => legendFilterList.value,
	() => {
		// legendFilterList更新
		// console.log('legendFilterList更新', legendFilterList.value)
		onChange();
	}
);

const dedupeAndCache = (newData: MarkerInfo[]) => {
	const dedupedData = newData.filter((item) => {
		const isDuplicate = swiperList.value.some((cachedItem) => cachedItem.id === item.id);
		if (!isDuplicate) {
			item.animation = true;
		}
		return !isDuplicate; // 返回是否为重复项，以决定是否包含在结果数组中
	});

	// console.log('dedupedData', dedupedData);
	const length = dedupedData.length;
	if (length) {
		console.log('length', length);
		swiperList.value = [...dedupedData, ...swiperList.value];
		swiperRef.value.scrollTop = 0;
		totalElements.value = swiperList.value.length;
		data.initialIndex += length;
		nextTick(() => {
			data.initialIndex !== -1 && imageViewer?.value.setActiveItem(data.initialIndex);
		});

		emits('updateStat');
	}
};

let mapEventTimeout: NodeJS.Timeout | null = null;
let isDestroyed = false;
/**
 * tab栏切换页面隐藏, 断开网络请求
 */
const onDestroy = () => {
	isDestroyed = true;
	mapEventTimeout && clearTimeout(mapEventTimeout);
};

const callMapEvent = () => {
	if (isDestroyed) return;
	getMonitorEvent({
		page: 0,
		size: 10,
		eventTypes: legendFilterList.value,
	}).then((result: any) => {
		totalElements.value = result.payload.totalElements;
		const list: MarkerInfo[] = [];
		result.payload.content.forEach((item: any, index: number) => {
			const temp = liveStore.notInAMapped(item, totalElements.value + index);
			list.push(temp);
		});
		dedupeAndCache(list);
	});
	liveStore.setEventList(true, true).finally(() => {
		mapEventTimeout = setTimeout(callMapEvent, 10000);
	});
};

/**
 * tab栏切换页面显示, 重新请求数据
 */
const onTabReactivated = () => {
	isDestroyed = false;
	callMapEvent();
};

onBeforeMount(() => {
	load();
	onTabReactivated();
});

onUnmounted(() => {
	isDestroyed = true;
	onDestroy();
});

defineExpose({
	data,
	isDestroyed,
	onDestroy,
	onTabReactivated,
});
</script>

<style lang="scss" scoped>
.animation-slide-hide {
	animation: slide-hide 0.3s forwards;
	animation-timing-function: cubic-bezier(0, 0.58, 1, 1);
}

.animation-slide-show {
	animation: slide-show 0.3s forwards;
	animation-timing-function: cubic-bezier(0, 0.58, 1, 1);
}
.home-right-container {
	position: absolute;
	top: 0;
	right: 0px;
	// width: 523px;
	width: 390px;
	height: 100%;
	// background-image: url('/src/assets/fullScreen/right-bg.png');
	background-image: url('/src/assets/fullScreen/you.png');
	background-position: right;
	background-repeat: no-repeat;
	// background-size: 100% 100%;
	background-size: contain;
	overflow: hidden;
	display: flex;
	justify-content: flex-end;
	pointer-events: auto;

	.home-right-container-main {
		position: relative;
		width: 354px;
		height: 100%;

		.home-right-container-main-z-index {
			position: absolute;
			width: 100%;
			height: 87.96vh;
			top: 6.667vh;
			right: 20px;
			z-index: 11;
			padding: 0 10px;
			background-image: url('/src/assets/fullScreen/swiper-bg.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;

			.home-right-container-title {
				position: relative;
				height: 45px;
				.more {
					position: absolute;
					top: 50%;
					right: 10px;
					transform: translateY(-50%);
					object-fit: contain;
					cursor: pointer;
				}
				&::before {
					content: '';
					position: absolute;
					top: 50%;
					left: 14px;
					width: 66px;
					height: 29px;
					transform: translateY(-50%);
					background-image: url('/src/assets/fullScreen/aq-bg.png');
					background-repeat: no-repeat;
					background-size: 100%;
				}
			}
			.filter {
				height: 29px;
				margin-top: 20px;
				position: relative;

				.filter-label {
					position: absolute;
					z-index: 999;
					top: 30%;
					left: 15px;
					transform: translateY(-30%);
					color: rgba(255, 255, 255, 1);
				}
			}
		}
	}
	.swiper-slide-container {
		height: calc(100% - 60px);

		.text-loding {
			text-align: center;
			font-size: 14px;
			color: #bbb;
		}

		.mt80 {
			margin-top: 80px;
		}
	}
	.swiper-slide {
		position: relative;
		box-sizing: border-box;
		border: 1px solid transparent;
		height: 120px;
		font-size: 30px;
		// background-color: #fff;
		padding: 10px;
		display: flex;
		margin-bottom: 10px;
		background-image: url('/src/assets/fullScreen/slide-bg.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		cursor: pointer;

		&:last-child {
			margin-bottom: 0;
		}

		.swiper-slide-location {
			position: absolute;
			top: 50px;
			right: 10px;
			height: 15px;
			font-size: 12px;
			z-index: 50;
			color: rgba(255, 255, 255, 1);
			display: flex;
			align-items: center;
			img {
				margin-right: 5px;
			}
		}
		.swiper-slide-img {
			position: relative;
			width: 160px;
			height: 100%;
			border-radius: 8px;
			overflow: hidden;
			// img {
			// 	width: 100%;
			// 	height: 100%;
			// 	object-fit: contain;
			// }
			.image-placeholder {
				width: 100%;
				height: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				.is-loading {
					color: #22d69f;
					font-size: 16px;
				}
			}
			:deep(.el-image__inner) {
				&:hover {
					transform: scale(1.05);
				}
			}
		}
		.swiper-slide-info {
			position: relative;
			flex: 1;
			font-size: 12px;
			padding-left: 10px;
			color: rgba(255, 255, 255, 1);
			font-size: 14px;
			overflow: hidden;
			display: flex;
			flex-direction: column;
			justify-content: center;

			div {
				height: 33%;
				display: flex;
				align-items: center;
				img {
					margin-right: 5px;
				}
				&:first-child {
					font-size: 16px;
				}
			}
		}
		&:nth-child(2n) {
			// background-color: pink;
		}
	}

	.swiper {
		width: 100%;
		height: calc(100% - 45px - 58px - 20px);
		margin-top: 20px;
	}

	.highlight-class {
		/* 你的高亮样式 */
		border: 1px solid #68cfad;
		box-sizing: border-box;
	}
}

.diglog {
	opacity: 0;
	background-color: rgba(12, 39, 34, 0.5);
	position: fixed;
	z-index: 99999;
	border: 1px solid #68cfad;
	.closeIcon {
		position: absolute;
		right: 20px;
		top: 20px;
		font-size: 24px;
		color: #ddd;
		cursor: pointer;
		font-weight: bold;
		z-index: 1;
		&:hover {
			color: #fff;
		}
	}
}

:deep(.diglog-elImage) {
	.el-image__placeholder {
		background-color: transparent;
	}
}

.filter {
	&:deep(.el-input__wrapper) {
		border-radius: 14px !important;
		border: 1px solid rgba(104, 207, 173, 1) !important;
		background-color: transparent !important;
		box-shadow: none !important;
	}
	&:deep(.el-input__inner) {
		text-align: right !important;
		color: rgba(255, 255, 255, 1);
	}
	&:deep(.el-select .el-input.is-focus .el-input__wrapper) {
		box-shadow: none !important;
	}
	&:deep(.el-select .el-input__inner::placeholder) {
		color: rgba(255, 255, 255, 1);
		font-size: 14px;
	}

	.el-icon :deep(svg) {
		color: rgba(255, 255, 255, 1);
	}

	&:deep(.el-select) {
		--el-select-input-focus-border-color: none !important;
	}
}

.image-slot {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	color: #68cfad;
	font-size: 18px;
	.dot {
		animation: dot 2s infinite steps(3, start);
		overflow: hidden;
	}
}
</style>

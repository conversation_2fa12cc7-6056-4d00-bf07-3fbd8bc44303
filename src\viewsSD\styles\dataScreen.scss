@import '/src/theme/mixins/index.scss';

// 湿地大屏公共样式
.data-screen {
  width: 100%;
  height: 100%;
  background: $sd-screen-bg-color;
  display: flex;
  flex-direction: column;
  color: #fff;
  font-size: vw(14);
  &-header {
    height: vw(86);
  }
  &-main {
    flex: 1;
    overflow: hidden;
  }

  // 主内容区
  .main-container {
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
    .left-container,
    .right-container {
      width: vw(402);
      height: 100%;
      padding: vw(20);
      box-sizing: border-box;
    }
    .center-container {
      flex: 1;
      position: relative;
      .filter-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: vw(60);
        padding: vw(24) 0 0;
        z-index: 20;
        display: flex;
        align-items: center;
        .toggle-buttons {
          position: absolute;
          right: 0;
        }
        .data-screen-radio-group {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
        }
        .data-screen-select {
          position: relative;
          left: vw(68);
        }
      }
    }
  }
  .main-container.map {
    .left-container,
    .right-container {
      position: absolute;
      top: 0;
      z-index: 10;
    }
    .left-container {
      left: 0;
      // 阴影
      &::before {
        display: block;
        content: '';
        width: calc(100% + vw(96));
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        background: linear-gradient(to right, $sd-screen-bg-color calc((100% - vw(96)) / 2), transparent);
        z-index: -1;
        pointer-events: none;
      }
    }
    .right-container {
      right: 0;
      // 阴影
      &::before {
        display: block;
        content: '';
        width: calc(100% + vw(96));
        height: 100%;
        position: absolute;
        right: 0;
        top: 0;
        background: linear-gradient(to left, $sd-screen-bg-color calc((100% - vw(96)) / 2),  transparent);
        z-index: -1;
        pointer-events: none;
      }
    }
    .center-container {
      &::before {
        content: '';
        display: block;
        position: absolute;
        width: 100%;
        height: vw(80);
        background: linear-gradient(to bottom, $sd-screen-bg-color, rgba(12, 30, 41, 0.2) 70%, transparent);
        z-index: 5;
      }
      .filter-container {
        width: calc(100% - vw(402 * 2));
        left: vw(402);
      }
    }
  }
}

// 地图公共样式
.amap-container {
  background-color: transparent !important;
  .amap-maps {
    background-color: $sd-screen-bg-color !important;
  }
  .amap-logo,
  .amap-copyright {
    display: none !important;
  }
  .amap-marker {
    pointer-events: none !important;
  }
}

// 各区域模块样式，比如物种统计、最近发现、物种占比等等
.module {
  .module-title {
    width: 98%;
    height: vw(40);
    background: url('/@/assets/sd/data-screen/module-title-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    font-size: vw(18);
    line-height: vw(36);
    padding-left: 12%;
    padding-right: vw(20);
    font-family: 'AliMaMaShuHeiTi';
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      display: flex;
      align-items: center;
      img {
        width: vw(30);
        height: vh(26);
        margin-right: vw(5);
      }
    }
    img {
      width: vw(22);
      height: vh(16);
      cursor: pointer;
    }
    .trend {
      width:vw(25);
    }
    .species{
      width: vw(26);
    }
    .paiming {
      width:vw(25);
    }
    .device {
      width: vw(26);
      height: 22px;
    }
    .juliu {
      width: vw(24);
    }
  }
  .module-cont {
    margin: vw(15) 0;
  }
}

// 文字阴影
.text-sd {
  text-shadow: 2px 0px 4px $sd-screen-bg-color2;
}

// map infowindow
.marker-window-modal {
  user-select: none;
  background-color: rgba(0, 0, 0, 0.2);
}
.marker-window-modal .marker-window.el-dialog {
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0 !important;
    overflow-y: hidden;
    overflow: visible;
  }
}
.map-custom-info-window {
	width: 100%;
	background: linear-gradient(to right, rgba(12, 29, 40, 0.98), rgba(12, 30, 41, 1));
	box-shadow: 1px 1px 20px 6px rgba(12, 29, 40, 0.9);
	position: relative;
	animation-duration: 0.4s !important;
  color: #fff;
	.module-title {
		width: 104%;
		margin-left: -2%;
		font-size: vw(16);
	}
	.icon-close {
		position: absolute;
		top: vw(10);
		right: 6%;
		cursor: pointer;
		img {
			width: vw(10);
			opacity: 0.8;
			&:hover {
				opacity: 1;
			}
		}
	}
  .info-window-cont {
    height: vw(230);
  }
	// .ranks {
	// 	padding: vw(5) vw(30) vw(15);
	// 	overflow: hidden;
	// 	overflow-y: overlay !important;
	// 	@include sd-scrollBar;
	// 	.rank-item {
	// 		display: flex;
	// 		justify-content: center;
	// 		align-items: center;
	// 		margin-top: vw(10);
	// 		.serial-num {
	// 			min-width: vw(60);
	// 			font-family: 'ALiMaMaShuHeiTi';
	// 			&.threeBefore {
	// 				color: #ffe700;
	// 			}
	// 		}
  //     .info {
  //       flex: 1;
  //       height: vw(26);
  //       display: flex;
  //       align-items: center;
  //       justify-content: space-between;
  //       padding: 0 vw(10) 0;
  //       background: url('/@/assets/sd/data-screen/box-bg2.png');
  //       background-repeat: no-repeat;
  //       background-size: 100% 100%;
  //       .count {
  //         font-family: 'ALiMaMaShuHeiTi';
  //       }
  //     }
	// 	}
	// }
  // .ranks .el-loading-mask {
  //   background-color: transparent;
  // }
}

// # reset element ui
.popper {
  background-color: $sd-screen-bg-color;
  border-color: $sd-screen-border-color;
  .el-popper__arrow::before {
    background-color: $sd-screen-bg-color !important;
    border-top-color: $sd-screen-border-color !important;
    border-left-color: $sd-screen-border-color !important;
  }
}
.bg {
  background: linear-gradient(to right, rgba(22, 226, 231, 0), rgba(33, 227, 221, 0.3), rgba(79, 229, 181, 1), rgba(118, 231, 146, 0.3), rgba(136, 232, 130, 0));
}

// Select
.data-screen-select.el-select {
  width: vw(253);
  height: vw(40);
  background-color: transparent;
  border: none;
  padding: vw(5) 0;
  box-shadow: 0 0 0 1px rgba(33, 227, 221, 0.1) inset;
  .el-input__wrapper {
    color: #fff;
    padding: 0 vw(20);
    box-shadow: none;
    border-radius: 0;
    @extend .bg;
    &:hover {
      box-shadow: none;
    }
  }
  .el-select-tags-wrapper  .el-tag {
    background-color: transparent;
    color: #fff;
    .el-tag__close {
      color: #ccc;
      &:hover {
        background-color: transparent;
        color: #fff;
      }
    }
  }
  .el-select__suffix .el-icon {
    color: #fff;
  }
  .el-popper {
    border: none;
    background-color: rgba(8, 25, 50, 0.5);
    border-radius: 0;
    .el-popper__arrow {
      display: none;
    }
    .el-select-dropdown__item {
      padding: 0 vw(10) 0 vw(20);
      margin-bottom: vw(5);
      background: linear-gradient(to right, rgba(22, 226, 231, 0), rgba(33, 227, 221, 0.3), rgba(79, 229, 181, 0.2), rgba(136, 232, 130, 0));
      .custom-option {
        display: flex;
        justify-content: space-between;
        color: #fff;
        .el-radio .el-radio__label {
          display: none;
        }
      }
    }
    .el-select-dropdown__item.selected {
      @extend .bg;
      &::after {
        display: none;
      }
    }
  }
}
// Radio
.data-screen-radio.el-radio {
  .el-radio__inner {
    width: 16px;
    height: 16px;
    background-color: transparent;
    &:hover {
      border-color: $sd-screen-success;
    }
  }
  .el-radio__input.is-checked .el-radio__inner {
    background-color: transparent !important;
    border-color: #fff !important;
    &::after {
      width: 6px;
      height: 6px;
      background-color: $sd-screen-success;
    }
  }
}
// Radio Group
.data-screen-radio-group.el-radio-group {
  width: vw(300);
  height: vw(36) !important;
  padding: vw(5) 0;
  background: url('/@/assets/sd/data-screen/box-bg2.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  .el-radio-button {
    height: 100%;
    .el-radio-button__inner {
      width: vw(75);
      height: 100%;
      line-height: vw(26);
      padding: 0;
      background-color: transparent;
      border-color: transparent;
      color: #9ACFFF;
    }
  }
  .el-radio-button.is-active .el-radio-button__original-radio:not(:disabled)+.el-radio-button__inner {
    color: #fff;
    box-shadow: -1px 0 0 0 $sd-screen-border-color;
    border-color: $sd-screen-border-color;
    @extend .bg;
  }
}
// Progress
.data-screen-progress.el-progress {
  .el-progress-bar {
    min-width: vw(100);
    .el-progress-bar__outer {
      // height: vw(20) !important;
      background: linear-gradient(to right, rgba(22, 226, 231, 0), rgba(33, 227, 221, 0.3), rgba(79, 229, 181, 0.2), rgba(136, 232, 130, 0));
      border: vw(2) solid rgba(151, 151, 151, 0.2);
    }
    .el-progress-bar__inner--striped {
      background-image: linear-gradient(-45deg, rgba(0,0,0,.1) 25%, transparent 0, transparent 50%, rgba(0,0,0,.1) 0, rgba(0,0,0,.1) 75%,transparent 0,transparent);
    }
  }
  .el-progress__text {
    color: #fff;
    font-size: vw(14) !important;
  }
}
// Image
.el-image {
  .image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .is-loading {
      color: $sd-screen-primary;
      font-size: 18px;
    }
  }
}
// Dropdown
.el-dropdown {
  .el-popper {
    @extend .popper;
  }
  .el-dropdown-menu {
    background-color: transparent;
    .el-dropdown-menu__item {
      color: #fff;
      font-family: none;
      margin: vw(8) 0;
    }
    .el-dropdown-menu__item:not(.is-disabled):hover,
    .el-dropdown-menu__item:not(.is-disabled):focus {
      background-color: transparent;
      color: $sd-screen-success !important;
    }
  }
}



// 使用 scss 的 math 函数，https://sass-lang.com/documentation/breaking-changes/slash-div
@use "sass:math" as math;

// 默认设计稿的宽度
$designWidth: 1920;
// 默认设计稿的高度
$designHeight: 1080;
 
// px 转为 vw 的函数
@function vw($px) {
  @return math.div($px, $designWidth) * 100vw;
}
 
// px 转为 vh 的函数
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}

$sd-screen-primary: #98F7D7; // 主要文字颜色
$sd-screen-success: #37FF9D;
$sd-screen-bg-color: #0A1822; // 主背景
$sd-screen-bg-color2: #21E3DD;
$sd-screen-border-color: rgba(73, 147, 122, 0.5);
$radio-color: #00ff93; // 单选按钮颜色

// 标准高度变量定义
$sd-module-title-height: vw(40); // 模块标题高度
$sd-module-title-line-height: vw(36); // 模块标题行高
$sd-module-margin: vw(12); // 模块间距（统一为中间值）
$sd-radio-group-height: vw(38); // Radio Group 高度（统一为中间值）
$sd-progress-bar-height: vw(16); // Progress Bar 高度
$sd-info-window-title-height: vw(39); // 信息窗口标题高度
$sd-info-window-content-height: vw(230); // 信息窗口内容高度
$sd-device-marker-height: vw(63); // 设备标记高度
$sd-device-icon-height: vw(21); // 设备图标高度（统一使用响应式单位）
$sd-rank-item-height: vw(26); // 排行榜项目高度
$sd-rank-title-height: vw(43); // 排行榜标题高度
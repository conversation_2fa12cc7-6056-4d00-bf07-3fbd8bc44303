import request, { CustomAxiosRequestConfig } from '/@/utils/request';

/**
 * 物种资源库相关接口
 * @method getDirectories 获取名录列表
 * @method getDirectoriesByRecResult 根据识别结果查询名录列表
 * @method getDirectotyDetailById 根据名录id查询物种详情
 * @method getDirectotyDetailByName 根据名录name查询物种详情
 * @method createDirectory 创建
 * @method updateDirectory 修改
 * @method deleteDirectory 删除
 * @method nameFilter 关联名录物种
 */

// type：0植物 1鸟 2昆虫 动物（3哺乳动物 4鱼类 5两栖类 6爬行类）
const urls: { [key: number]: string} = {
	0: 'directory-plants',
	1: 'directory-birds',
	2: 'directory-insects',
	3: 'directory-animals',
	4: 'directory-fish',
	5: 'directory-amphibians',
	6: 'directory-reptiles',
}
export function getDirectories(type: number, params: Object) {
	return request(<CustomAxiosRequestConfig>{
		url: `/${urls[type]}`,
		method: 'get',
		params,
    isTransformResponse: true,
	});
}

export function getDirectoriesByRecResult(params: Object) {
	return request({
		url: `/directory-plants/directory-list`,
		method: 'get',
		params,
    isTransformResponse: true,
	});
}

export function getDirectoryDetailById(type: number, id: number) {
	return request({
		url: `/${urls[type]}/${id}`,
		method: 'get',
	});
}

export function getDirectoryDetailByName(type: number, params: { name: string }) {
	const url = type === 0 ? `/${urls[type]}/${params.name}` : `/${urls[type]}/name`
	return request({
		url,
		method: 'get',
		params: type === 0 ? null : params,
	});
}

export function createDirectory(type: number, data: Object) {
	return request(<CustomAxiosRequestConfig>{
		url: `/${urls[type]}`,
		method: 'post',
		data,
		headers: {
			'content-type': 'multipart/form-data'
		},
    isTransformResponse: true,
	});
}

export function updateDirectory(type: number, id: number, data: Object) {
	return request(<CustomAxiosRequestConfig>{
		url: `/${urls[type]}/${id}`,
		method: 'put',
		data,
		headers: {
			'content-type': 'multipart/form-data'
		},
    isTransformResponse: true,
	});
}

export function deleteDirectory(type: number, id: number) {
	return request({
		url: `/${urls[type]}/${id}`,
		method: 'delete',
	});
}

export function nameFilter(type: number, params: object) {
	return request({
		url: `/${urls[type]}/name-filter`,
		method: 'get',
		params,
	});
}

// 纲，type 动物Animalia 植物Plantae
export function getClasses(params: { type: string }) {
	return request(<CustomAxiosRequestConfig>{
		url: `/directory-classes`,
		method: 'get',
		params,
    isTransformResponse: true,
	});
}

// 目，classId父级纲
export function getOrders(params: { classId: number }) {
	return request(<CustomAxiosRequestConfig>{
		url: `/directory-orders`,
		method: 'get',
		params,
    isTransformResponse: true,
	});
}

// 科，orderId父级目
export function getFamilies(params: { orderId: number }) {
	return request(<CustomAxiosRequestConfig>{
		url: `/directory-families`,
		method: 'get',
		params,
    isTransformResponse: true,
	});
}

// 目，familyId父级科
export function getGenus(params: { familyId: number }) {
	return request(<CustomAxiosRequestConfig>{
		url: `/directory-genera`,
		method: 'get',
		params,
    isTransformResponse: true,
	});
}

// # （特殊）鸟类列表 
// 获取物种列表
export function getAnimals(params: Object) {
	return request(<CustomAxiosRequestConfig>{
		url: `/animals`,
		method: 'get',
    params,
    isTransformResponse: true,
	});
}

// 获取当前物种详情
export function getAnimalByName(name: string) {
	return request({
		url: `/animals/${name}`,
		method: 'get',
	});
}

// 创建/更新物种
export function updateAnimal(data: any) {
	return request({
		url: `/animals`,
		method: 'post',
    data,
	});
}

// 移除物种标签
export function deleteAnimalTag(data: { name: string; tag: number}) {
	return request({
		url: `/animals/${data.name}/tag`,
		method: 'delete',
    params: {
      tag: data.tag,
    },
	});
}

// 移除物种居留类型
export function deleteAnimalResidencyType(data: { name: string; residencyType: number}) {
	return request({
		url: `/animals/${data.name}/residencyType`,
		method: 'delete',
    params: {
      tag: data.residencyType,
    },
	});
}
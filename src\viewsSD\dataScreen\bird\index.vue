<template>
	<div class="main-container" :class="state.mainType === 1 ? 'map' : ''">
		<LeftCon :timeType="state.dateRange"></LeftCon>
		<!-- # -->
		<div class="center-container">
			<div class="filter-container">
				<!-- date range -->
				<DateRange :dateRange="state.dateRange" @dateRangeChange="onDateRangeChange"></DateRange>
				<!-- toggle button -->
				<ToggleButton :mainType="state.mainType" @mainTypeChange="onMainTypeChange"></ToggleButton>
			</div>
			<Map v-show="state.mainType === 1" :timeType="state.dateRange"></Map>
			<Images v-show="state.mainType === 2" :timeType="state.dateRange"></Images>
		</div>
		<!-- # -->
		<RightCon :timeType="state.dateRange"></RightCon>
		<Notice v-show="state.mainType === 1"></Notice>
	</div>
</template>

<script setup lang="ts">
import { reactive } from 'vue';
// 子组件
import LeftCon from './leftCon.vue';
import RightCon from './rightCon.vue';
import DateRange from '../component/dateRange/index.vue';
import ToggleButton from '../component/toggleButton/index.vue';
import Map from '../component/map/index.vue';
// import Map from '../component/map/index2.vue';
import Images from '../component/images/index.vue';
import Notice from '../component/notice/index.vue';

const state = reactive({
	dateRange: 1 as number | null, // 时间类型 1.近一月 2.近三月 3.近一年 null.累计
	mainType: 1, // 1地图 2图集
});

// 日期范围改变
const onDateRangeChange = (value: number | null) => {
	state.dateRange = value;
};
// 切换
const onMainTypeChange = (value: number) => {
	state.mainType = value;
};
</script>

<template>
	<el-radio-group
		v-if="props.radios && props.radios.length > 0"
		class="data-screen-radio-group"
		v-model="radioValue"
		@change="handleChange"
	>
		<el-radio-button v-for="item in props.radios" :key="item.value" :label="item.value">
			{{ item.label }}
		</el-radio-button>
	</el-radio-group>
</template>

<script setup lang="ts" generic="T">
import { ref, watch } from 'vue';
import type { Radios } from '/@/viewsHLJ/type/index';

const props = defineProps<{
	modelValue: T;
	radios: Radios<T>[];
}>();

const emits = defineEmits<{
	(e: 'update:modelValue', value: T): void;
	(e: 'change', value: T): void;
}>();

const radioValue = ref<T>(props.modelValue);

// 监听外部 modelValue 变化
watch(
	() => props.modelValue,
	(newVal) => {
		radioValue.value = newVal;
	}
);

const handleChange = (val: T) => {
	emits('update:modelValue', val);
	emits('change', val);
};

// 监听内部 radioValue 变化
watch(radioValue, (newVal) => {
	emits('update:modelValue', newVal);
});
</script>

<style scoped>
.data-screen-radio-group {
	display: inline-block;
}
</style>

<template>
	<div class="rvc layout-pd">
		<div class="video-box">
			<div class="title">实时视频</div>
			<div class="video">
				<div ref="container"></div>
			</div>
			<!-- IOS系统谷歌浏览器，内置toolbar样式，尽量避免使用class="toolbar" -->
			<!-- 按钮禁用：未播放或识别中时 -->
			<div class="toolbar-btns">
				<div class="toolbar-left">
					<el-button
						class="recognition-button"
						round
						:disabled="toolbarBtnDisabled"
						@click="onRecord"
					>
						<img :src="recordState.img" alt="" /> {{ recordState.text }}
					</el-button>
				</div>
				<div class="toolbar-right">
					<el-button
						class="recognition-button"
						round
						:disabled="toolbarBtnDisabled"
						@click="onScreenshot"
					>
						<img src="/src/assets/sd/rvc/screenshot.png" alt="" /> 截图
					</el-button>
					<!-- 云台控制 -->
					<el-tooltip
						trigger="hover"
						effect="light"
						placement="top"
						popper-class="handle-tooltip"
						:disabled="!isPTZControl || toolbarBtnDisabled"
					>
						<el-button
							class="recognition-button"
							round
							:disabled="!isPTZControl || toolbarBtnDisabled"
						>
							<img src="/src/assets/sd/rvc/focus.png" alt="" /> 控制
						</el-button>
						<template #content>
							<div class="handle">
								<div class="handle-keys">
									<div class="key top" @click="onHandleKeyAction(21)">
										<img src="/src/assets/sd/rvc/handle-top.png" alt="" title="上仰" />
									</div>
									<div class="key bottom" @click="onHandleKeyAction(22)">
										<img src="/src/assets/sd/rvc/handle-bottom.png" alt="" title="下俯" />
									</div>
									<div class="key left" @click="onHandleKeyAction(23)">
										<img src="/src/assets/sd/rvc/handle-left.png" alt="" title="左转" />
									</div>
									<div class="key right" @click="onHandleKeyAction(24)">
										<img src="/src/assets/sd/rvc/handle-right.png" alt="" title="右转" />
									</div>
									<!-- <div class="key motion">
                    <img src="/src/assets/sd/rvc/handle-refresh.png" alt="">
                  </div> -->
									<div class="key add" @click="onHandleKeyAction(11)">
										<img src="/src/assets/sd/rvc/handle-add.png" alt="" title="焦距放大" />
									</div>
									<div class="key reduce" @click="onHandleKeyAction(12)">
										<img src="/src/assets/sd/rvc/handle-reduce.png" alt="" title="焦距缩小" />
									</div>
								</div>
							</div>
						</template>
					</el-tooltip>
				</div>
			</div>
			<div class="select card">
				<div>
					模型选择：<el-radio-group v-model="state.ruleForm.aiModelId">
						<el-radio v-for="item in aiModels.data" :key="item.id" :label="item.id">{{
							item.name
						}}</el-radio>
					</el-radio-group>
				</div>
				<div>
					截图张数：<el-radio-group v-model="screenshots">
						<el-radio :label="1" size="large">1张</el-radio>
						<el-radio :label="3" size="large">3张</el-radio>
						<el-radio :label="5" size="large">5张</el-radio>
					</el-radio-group>
				</div>
				<div>
					录制时长：<el-radio-group v-model="radio2" @change="onRecordRadioChange">
						<el-radio :label="10" size="large">10s</el-radio>
						<el-radio :label="20" size="large">20s</el-radio>
						<el-radio :label="30" size="large">30s</el-radio>
					</el-radio-group>
				</div>
			</div>
		</div>
		<div class="result">
			<div class="title">
				文件<span class="font14">{{
					state.screenshotPictures.length ? `（总截图${state.screenshotPictures.length}张）` : ''
				}}</span>
			</div>
			<div class="file-container card">
				<div class="file">
					<!-- 图片 -->
					<div class="shot-pictures">
						<div
							v-show="state.screenshotPictures.length > 0"
							class="result-pictures-item"
							v-for="(item, $index) in state.screenshotPictures"
							:key="item.localUrl"
						>
							<el-image :src="item.localUrl" fit="cover" />
							<div class="status" v-if="state.recogStatus !== 1">
								<el-icon style="cursor: pointer" @click="onDeletefile($index)">
									<ele-Delete />
								</el-icon>
							</div>
						</div>
					</div>
					<!-- 视频 -->
					<video v-show="videoURL" ref="videoRecRef" controls autoplay name="media"></video>
				</div>
				<div
					v-show="state.ruleForm.recType !== -1"
					class="recognition-button-box"
					:class="videoURL ? 'jc-sb' : 'jc-end'"
				>
					<el-button
						v-show="videoURL"
						class="recognition-button"
						type="danger"
						round
						@click="videoURL = ''"
					>
						删除
					</el-button>
					<el-button
						v-show="videoURL || state.screenshotPictures.length > 0"
						class="recognition-button"
						type="success"
						round
						:loading="state.recogStatus === 1"
						@click="onRecog"
					>
						识别
					</el-button>
				</div>
			</div>

			<div class="title" :class="state.recogStatus === 1 ? 'recoging' : ''">{{ recogTitle }}</div>
			<custom-scrollbar
				ref="scroll"
				contentClass="species"
				:style="{ height: '100%' }"
				wrapperClass="card recognition"
				:autoHide="false"
			>
				<div
					class="result-pictures-item"
					v-for="(item, $index) in state.resultList"
					:key="item.$index"
				>
					<!-- 
            isRecogFail：是否识别失败
            存在pictureUrl，即识别完成；否则正在识别中
            -->
					<el-image
						:src="item.pictureUrl || item.localUrl"
						fit="cover"
						:class="item.pictureUrl ? 'active' : ''"
						:preview-src-list="perviewPicList"
						:initial-index="$index"
					/>
					<div v-if="item.recResult" class="recResults-box">
						<template v-if="item.recResult.length === 0">未发现物种</template>
						<template v-else>
							<span
								v-for="rec in item.recResult"
								:key="rec.recResult"
								@click="onAssSpecies(rec.recResult)"
							>
								{{ rec.recResult }}（{{ rec.recResultCnt }}）
							</span>
						</template>
					</div>
					<div class="status">
						<el-icon class="success" v-if="item.pictureUrl"><ele-SuccessFilled /></el-icon>
						<el-icon class="loading" v-else-if="!item.isRecogFail && item.localUrl">
							<ele-Loading />
						</el-icon>
						<el-icon v-else><ele-Close /></el-icon>
					</div>
				</div>
			</custom-scrollbar>
		</div>
		<SpeciesDialog ref="speciesDialogRef"></SpeciesDialog>
	</div>
</template>

<script setup lang="ts">
import {
	ref,
	reactive,
	onMounted,
	onUnmounted,
	watch,
	computed,
	nextTick,
	defineAsyncComponent,
	onActivated,
	onDeactivated,
} from 'vue';
import { ElMessage } from 'element-plus';
import { generatePlayUrl, controlCamera } from '/@/api/devices';
import { useAiModels } from '/@/hooks/useAiModels';
import { uploadFile, readerFile } from '/@/api/upload';
import { doRecog, getRecogResult } from '/@/api/recognize';
import { useRoute } from 'vue-router';
import CustomScrollbar from 'custom-vue-scrollbar';
import 'custom-vue-scrollbar/dist/style.css';

const SpeciesDialog = defineAsyncComponent(
	() => import('/@/views/monitorEvents/speciesDialog.vue')
);

const route = useRoute();
const aiModels = reactive(useAiModels());
const state = reactive<any>({
	ruleForm: {
		deviceId: route.query.deviceId,
		aiModelId: '',
		recType: -1, // 0图片识别 1视频识别
	},
	screenshotPictures: [], // 截图图片集
	// recog
	recogStatus: 0, // 识别状态：0未识别/识别结束  1正在识别中
	fileIds: [], // 上传文件ids
	monitorId: '', // 本次识别记录id
	resultList: [], // 识别结果列表
	intervalTimer: null,
});
const recogTitle = computed(() => {
	if (state.recogStatus === 1) return '正在识别中...';
	return '识别结果';
});
// 视频未播放或正在识别时，禁用操作按钮
const toolbarBtnDisabled = computed(() => {
	return !playing.value || state.recogStatus === 1;
});

watch(
	() => aiModels.data,
	(val) => {
		state.ruleForm.aiModelId = val && val.length > 0 ? val[0].id : '';
	}
);

import startRecord from '/@/assets/sd/rvc/start-record.png';
import endRecord from '/@/assets/sd/rvc/end-record.png';
const radio2 = ref(10); // 录制时长
const recordState = reactive({
	state: false,
	text: '录制视频',
	img: startRecord,
});

let dataChunks: any[] = [];
let recorder: any;
const videoURL = ref('');
const videoRecRef = ref();
let seconds = 0;
let videoBlob: Blob;
let recordingElement: HTMLElement;
let timeElement: HTMLElement;

// 录制视频
const onRecord = () => {
	reRecordAndShot();
	if (jessibuca && !jessibuca.isPlaying()) return;

	if (!recorder) {
		const stream = jessibuca.$container.children[0].captureStream();
		recorder = new MediaRecorder(stream, { mimeType: 'video/webm' });
		recorder.ondataavailable = (event: any) => {
			console.log('录制中');
			if (radio2.value === seconds) {
				recorder?.stop();
				return;
			}
			let data = event.data;
			dataChunks.push(data);
			seconds += 1;
			if (document.getElementsByClassName('jessibuca-recording-time').length) {
				document.getElementsByClassName('jessibuca-recording-time')[0].innerHTML =
					secondsToHMS(seconds);
			}
		};
		recorder.onstart = () => {
			recordState.state = true;
			recordState.text = '结束录制';
			recordState.img = endRecord;
			const elements = document.getElementsByClassName(
				'jessibuca-recording'
			) as HTMLCollectionOf<HTMLElement>;
			if (elements.length > 0) {
				recordingElement = elements[0];
				recordingElement.style.display = 'flex';
				timeElement = document.getElementsByClassName('jessibuca-recording-time')[0] as HTMLElement;
				timeElement.innerHTML = secondsToHMS(seconds);
			}
		};
		recorder.onstop = () => {
			seconds = 0;
			recordingElement.style.display = 'none';
			videoBlob = new Blob(dataChunks, { type: 'video/webm' });
			dataChunks = [];
			videoURL.value = URL.createObjectURL(videoBlob);
			// 创建一个临时的下载链接 用来测试
			// const downloadLink = document.createElement('a');
			// downloadLink.href = URL.createObjectURL(videoBlob);
			// downloadLink.download = 'video.webm'; // 指定下载文件的名称

			// // 模拟点击这个链接以触发下载
			// document.body.appendChild(downloadLink);
			// downloadLink.click();

			// // 清理：移除链接并释放创建的URL对象
			// document.body.removeChild(downloadLink);
			// URL.revokeObjectURL(downloadLink.href);
			nextTick(() => {
				videoRecRef.value.src = videoURL.value;
				videoRecRef.value.load();
				state.ruleForm.recType = 1;
			});
			recorder = null;
			recordState.state = false;
			recordState.text = '录制视频';
			recordState.img = startRecord;
		};
		recorder.start(1000);
	} else {
		recorder.stop();
	}
};
const secondsToHMS = (d: number) => {
	d = Number(d);
	let h = Math.floor(d / 3600);
	let m = Math.floor((d % 3600) / 60);
	let s = Math.floor((d % 3600) % 60);

	return (h > 9 ? '' : '0') + h + ':' + (m > 9 ? '' : '0') + m + ':' + (s > 9 ? '' : '0') + s;
};
// 录制时长改变
const onRecordRadioChange = () => {
	if (jessibuca && jessibuca.isRecording()) {
		onRecord();
	}
};

// 截图
const screenshots = ref(3); // 截图张数
const onScreenshot = async () => {
	reRecordAndShot();
	if (!jessibuca && !jessibuca.isPlaying()) return;

	// 将截取的图片保存到 `state.screenshotPictures`
	state.screenshotPictures = [];
	for (let i = 0; i < screenshots.value; i++) {
		// filename设置无效，生成的文件名都是file
		const blob = jessibuca.screenshot(`test${i}`, 'jpg', 1, 'blob');
		const res = await readerFile(blob);
		state.screenshotPictures.push({
			localUrl: typeof res === 'string' ? res : '',
			fileBlob: blob,
		});
	}
	state.ruleForm.recType = 0;
};
// 再次点击录制或截图，重置数据
const reRecordAndShot = () => {
	state.screenshotPictures = [];
	videoURL.value = '';
	state.resultList = [];
	state.ruleForm.recType = -1;
};
// 删除文件
const onDeletefile = (index: number) => {
	state.screenshotPictures.splice(index, 1);
};

// 云台控制，按指令`commandType`控制设备
const onHandleKeyAction = async (commandType: number) => {
	await controlCamera({
		deviceRtsp: <string>route.query.rtspUrl,
		commandType,
		manufacturer: route.query.manufacturer ? Number(route.query.manufacturer) : null,
	});
};

// 点击识别按钮，开始识别
const onRecog = async () => {
	state.recogStatus = 1;
	state.resultList = [];

	// 上传文件（图片或视频）
	const formData = new FormData();
	if (state.ruleForm.recType === 0) {
		for (let index = 0; index < state.screenshotPictures.length; index++) {
			formData.append('file', state.screenshotPictures[index].fileBlob);
		}
	} else {
		formData.append('file', videoBlob);
	}
	const { payload } = await uploadFile(formData);
	const fileIds: string[] = []; // 已上传文件ids
	const temp: CustomResult[] = []; // 预览图片列表
	payload.forEach((item: any, $index: number) => {
		fileIds.push(item.id);
		temp.push({
			pictureId: item.id,
			localUrl: state.screenshotPictures[$index] ? state.screenshotPictures[$index].localUrl : '',
			isRecogFail: false, // 是否识别失败
		});
	});
	state.fileIds = fileIds;
	if (state.ruleForm.recType === 0) {
		state.resultList = temp;
	} else {
		state.resultList = [];
	}
	// 识别
	const data = {
		aiModelId: state.ruleForm.aiModelId,
		fileIds: fileIds,
		deviceId: state.ruleForm.deviceId,
		recType: state.ruleForm.recType,
	};
	const doRecogData = await doRecog(data);
	state.monitorId = doRecogData.payload.monitorId;
	onGetRecogResult();
	initIntervalTimer();
};

const initIntervalTimer = () => {
	clrearIntervalTimer();
	state.intervalTimer = setInterval(onGetRecogResult, 3000);
};
const clrearIntervalTimer = () => {
	if (state.intervalTimer) {
		clearInterval(state.intervalTimer);
		state.intervalTimer = null;
	}
};

// 获取识别结果
const onGetRecogResult = async () => {
	const data = {
		monitorId: state.monitorId,
		aiModelId: state.ruleForm.aiModelId,
		fileIds: state.fileIds,
		recType: state.ruleForm.recType,
	};
	const { payload } = await getRecogResult(data);
	// end为true，识别完成，清除定时器
	const results = payload.monitorEvents;
	if (payload.end && results.length === 0) {
		state.resultList.forEach((item: CustomResult) => (item.isRecogFail = true));
		ElMessage.error('文件识别失败，请检查模型后重新上传');
		state.recogStatus = 0;
		clrearIntervalTimer();
		return;
	}
	if (payload.end) {
		ElMessage.success('识别结束');
		state.recogStatus = 0;
		clrearIntervalTimer();
	}
	if (results.length === 0) return;
	// pictures中monitorEventDetails有数据时代表识别到物种
	if (state.ruleForm.recType === 0) {
		results.forEach((item: MResultItem) => {
			const index = state.resultList.findIndex(
				(f: CustomResult) => f.pictureId === item.oriPictureId
			);
			const { pictures } = item;
			state.resultList.splice(index, 1, {
				...state.resultList[index],
				pictureUrl:
					pictures[0].monitorEventDetails.length > 0
						? pictures[0].pictureUrl
						: pictures[0].oriPictureUrl,
				recResult: pictures[0].monitorEventDetails,
			});
		});
	} else {
		// 视频识别时，停止播放本地视频url，更新结果
		state.resultList = results[0].pictures.map((item: MPictureItem) => ({
			pictureUrl: item.monitorEventDetails.length > 0 ? item.pictureUrl : item.oriPictureUrl,
			recResult: item.monitorEventDetails,
			isRecogFail: false,
		}));
	}
};
// 点击查看大图
const perviewPicList = computed(() => {
	return state.resultList.map((item: CustomResult) => item.pictureUrl || item.localUrl);
});
// 识别结果链接物种百科
const speciesDialogRef = ref<InstanceType<typeof SpeciesDialog>>();
// @param name(识别结果名称)，根据识别结果查询关联物种百科数据
const onAssSpecies = async (name: string) => {
	speciesDialogRef.value?.getAssSpecies(name);
};

let jessibuca: any;
const container = ref(null);
const showOperateBtns = ref(true);
const forceNoOffscreen = ref(false);
const playing = ref(false);
let playUrl = '';
const isPTZControl = ref(false); // 是否支持云台控制
const initJessibuca = () => {
	jessibuca = new (window as any).Jessibuca({
		container: container.value,
		videoBuffer: 1, // 缓存时长
		isResize: true,
		isFullResize: false,
		isFlv: true,
		loadingText: '视频加载中，请稍候',
		heartTimeout: 3,
		loadingTimeout: 10,
		decoder: '/jessibuca/decoder.js',
		hasAudio: true,
		debug: false,
		operateBtns: {
			fullscreen: showOperateBtns.value,
			screenshot: false,
			play: showOperateBtns.value,
			audio: false,
			record: false,
		},
		forceNoOffscreen: forceNoOffscreen.value,
		isNotMute: true,
		useWebFullScreen: true,
		controlAutoHide: true,
	});

	jessibuca.on('recordEnd', function () {
		document.getElementsByClassName('jessibuca-recording-time')[0].innerHTML = '00:00:00'; // 重置播放器的录制时长
	});
	jessibuca.on('recordingTimestamp', function (timestamp: number) {
		// console.log('录制的时长 is', timestamp);
		if (timestamp >= radio2.value) {
			jessibuca.stopRecordAndSave();
			onRecord();
		}
	});

	jessibuca.on('pause', function () {
		// console.log('暂停');
		if (recorder) {
			recorder.stop();
		}
		playing.value = false;
	});
	jessibuca.on('play', function () {
		playing.value = true;
	});

	jessibuca.on('timeout', function (error: any) {
		console.log('当设定的超时时间内无数据返回,则回调:', error, jessibuca.ERROR);
	});

	jessibuca.on('error', function (error: any) {
		// playShow.value = true;
		// console.log('jessibuca-错误', error);
		jessibuca.play(playUrl);
	});

	document
		.getElementsByClassName('jessibuca-icon-recordStop')[0]
		.addEventListener('click', onRecord);
};
const play = () => {
	destoryJessibuca();
	initJessibuca();
	playUrl = generatePlayUrl(<string>route.query.deviceId);
	console.log('playUrl', playUrl);
	if (jessibuca.loaded) {
		jessibuca.play(playUrl);
	} else {
		jessibuca.on('load', function () {
			jessibuca.play(playUrl);
		});
	}
};
const destoryJessibuca = () => {
	jessibuca && jessibuca.destroy();
	jessibuca = null;
};

onActivated(() => {
	isPTZControl.value = route.query.ifControl === '1';
	play();
});
onDeactivated(() => {
	destoryJessibuca();
	clrearIntervalTimer();
});

onMounted(() => {
	isPTZControl.value = route.query.ifControl === '1';
	play();
});
onUnmounted(() => {
	destoryJessibuca();
	clrearIntervalTimer();
});
</script>

<style lang="scss" scoped>
/* @import url(); 引入css类 */
.rvc {
	display: flex;
	.video-box {
		flex: 1;
		// width: vw(1220);
		margin-right: vw(40);
		.video {
			position: relative;
			height: vh(686);
			background: #000;
			margin-bottom: vh(22);
			.record-state {
				position: absolute;
				top: vh(15);
				left: 50%;
				z-index: 9999999;
				transform: translateX(-50%);
				border-radius: vh(16);
				background-color: rgba(0, 0, 0, 0.3);
				width: vw(189);
				height: vh(32);
				display: flex;
				justify-content: center;
				align-items: center;
				cursor: pointer;
				img {
					width: vw(22);
					margin-right: vw(16);
				}
				.record-duration {
					color: rgba(255, 255, 255, 1);
					font-size: 18px;
				}
			}
		}
		.toolbar-btns {
			height: vh(35);
			display: flex;
			justify-content: space-between;
			margin-bottom: vh(22);
			.toolbar-left {
				height: 100%;
				:deep(.el-button) {
					border-color: #ff0000;
				}
			}
			.toolbar-right {
				display: flex;
				justify-content: flex-end;
				height: 100%;
				:deep(.el-button) {
					border-color: rgba(126, 211, 33, 1);
				}
			}
		}

		.select {
			display: flex;
			flex-direction: column;
			height: vh(144);

			&:deep(.el-radio) {
				height: vh(38) !important;
				.el-radio__inner {
					background-color: #fff;
					&::after {
						width: 8px;
						height: 8px;
						background-color: rgba(126, 211, 33, 1);
					}
					&:hover {
						border-color: rgba(126, 211, 33, 1);
					}
				}
				.el-radio__input.is-checked .el-radio__inner {
					border-color: rgba(126, 211, 33, 1);
				}
				.el-radio__input.is-checked + .el-radio__label {
					color: rgba(126, 211, 33, 1) !important;
				}
			}
		}
	}
	.result {
		width: vw(360);
		.file-container {
			height: vh(280);
			margin-bottom: vh(10);
			.file {
				height: calc(100% - vh(45));
				overflow-y: auto;
				&::-webkit-scrollbar-thumb {
					background-color: #999;
				}
				video {
					width: 100%;
					height: 100%;
					vertical-align: middle;
				}
			}
		}
		.recognition-button-box {
			display: flex;
			margin-top: vh(6);
		}
		.jc-sb {
			justify-content: space-between;
		}
		.jc-end {
			justify-content: end;
		}
		.recognition {
			height: vh(580);
			overflow: auto;
		}
	}

	.title {
		height: vh(25);
		line-height: vh(25);
		margin: vh(10) 0;
		color: #333333;
		font-size: vw(18);
		font-weight: 700;
		&::before {
			margin-right: 10px;
			display: inline-block;
			content: '';
			width: 6px;
			height: 16px;
			background: var(--el-color-primary);
			vertical-align: middle;
			border-radius: 4px;
			margin-right: 5px;
			position: relative;
			bottom: 1.5px;
		}
		&::before {
			margin-right: 10px;
		}
		&.recoging::before {
			animation: loading-rotate 3s linear infinite;
		}
	}
	.card {
		border-radius: 4px;
		background-color: rgba(238, 238, 238, 0.34);
		padding: vh(10);
	}
	.recognition-button {
		width: vw(129);
		letter-spacing: 1px;
		font-size: 16px;
		img {
			width: vw(20);
			margin-right: vw(8);
		}
	}
}

:deep(.jessibuca-container) {
	.jessibuca-recording {
		position: absolute;
		left: 50%;
		top: 5px;
		width: vw(189);
		height: vh(32);
		transform: translateX(-50%);
		justify-content: space-around;
		align-items: center;
		background: rgba(0, 0, 0, 0.3);
		border-radius: vh(16);
		z-index: 1;
	}
	.jessibuca-recording-red-point {
		width: 12px;
		height: 12px;
		background: #ff4747;
	}
	.jessibuca-recording-time {
		font-size: 16px;
		font-weight: 500;
		color: #fff;
	}
	.jessibuca-icon-recordStop {
		width: 21px;
		height: 21px;
		background: url('/src/assets/sd/rvc/end-record.png') no-repeat;
		background-size: cover;
	}

	.jessibuca-controls {
		background-color: rgba(22, 22, 22, 0.7);
	}
}

.result-pictures-item {
	overflow: hidden;
	margin-bottom: 10px;
	position: relative;
	& > .el-image {
		width: 100%;
		// padding-top: 75% !important;
		& > img {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			opacity: 0.5;
			transition: all 0.3s ease;
			cursor: pointer;
		}
		&:hover > img {
			transform: scale(1.05);
		}
		&.active > img {
			opacity: 1;
		}
	}
	.recResults-box {
		width: 100%;
		position: absolute;
		bottom: 0;
		left: 0;
		color: #fff;
		background-color: rgba(0, 0, 0, 0.6);
		padding: 5px;
		& > span:hover {
			cursor: pointer;
			color: var(--el-color-primary);
		}
	}
	.status {
		position: absolute;
		width: 50px;
		height: 50px;
		border-radius: 50%;
		right: -25px;
		top: -25px;
		background-color: rgba(0, 0, 0, 0.6);
		.el-icon {
			position: absolute;
			left: 7px;
			bottom: 7px;
			color: #fff;
			font-size: 16px;
		}
		.el-icon.loading {
			color: #fff;
			animation: loading-rotate 3s linear infinite;
		}
		.el-icon.success {
			color: var(--el-color-success-light-3);
		}
	}
	&:last-child {
		margin-bottom: 0;
	}
}

:deep() {
	.el-button.is-disabled,
	.el-button.is-disabled:focus,
	.el-button.is-disabled:hover {
		border-color: var(--el-button-disabled-border-color) !important;
	}
}
</style>

<style lang="scss">
.handle-tooltip {
	padding: 0;
	.handle {
		padding: 15px 20px;
		&-keys {
			margin: 0 auto;
			width: vw(120);
			height: vw(120);
			background: url('/src/assets/sd/rvc/handle-bg.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			position: relative;
			.key {
				position: absolute;
				cursor: pointer;
				img {
					width: 100%;
					height: 100%;
					vertical-align: middle;
					transform-origin: center;
				}
				&:hover img {
					transform: scale(1.05);
				}
			}
			.key.top {
				width: vw(28);
				top: vw(20);
				left: 50%;
				transform: translateX(-50%);
			}
			.key.bottom {
				width: vw(28);
				bottom: vw(20);
				left: 50%;
				transform: translateX(-50%);
			}
			.key.left {
				height: vw(28);
				left: vw(20);
				top: 50%;
				transform: translateY(-50%);
			}
			.key.right {
				height: vw(28);
				right: vw(20);
				top: 50%;
				transform: translateY(-50%);
			}
			.key.motion {
				width: vw(44);
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
			}
			.key.add {
				width: vw(24);
				right: 0;
				bottom: 0;
			}
			.key.reduce {
				width: vw(24);
				left: 0;
				bottom: 0;
			}
		}
	}
}
</style>

<template>
	<div class="layout-pd">
		<div class="mb15" style="text-align: right;">
      <el-tooltip effect="dark"	content="刷新" placement="top">
        <el-button circle @click="onRefresh">
          <template #icon>
            <el-icon><ele-Refresh /></el-icon>
          </template>
        </el-button>
      </el-tooltip>
    </div>

		<Table v-bind="tableOptions" ref="tableRef" @pageChange="onPageChange">
			<template #status="{ row }">
        <div class="custom-badge" :class=" row.success ? 'success' : 'danger'">
          {{ row.success ? '成功' : '失败' }}
        </div>
			</template>
		</Table>
	</div>
</template>

<script setup lang="ts" name="AuditLog">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { getAuditLogs } from '/@/api/auditLog';

// 引入异步组件
const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));

const tableRef = ref<InstanceType<typeof Table>>();
const tableOptions: GlobalTableOptions<UserRow> = reactive({
	data: [],
	header: [
		{
			title: '模块',
			key: 'module',
			isCheck: true,
		},
		{
			title: '操作',
			key: 'operation',
			isCheck: true,
		},
    {
      title: 'IP',
      key: 'ip',
      isCheck: true,
		},
		{
			title: '用户名',
			key: 'userName',
			isCheck: true,
		},
    {
			title: '状态',
			key: 'status',
			isCheck: true,
		},
		{
			title: '创建时间',
			key: 'timestamp',
			isCheck: true,
		},
	],
	config: {
		loading: true,
		isSelection: false,
		isSerialNo: true,
		isOperate: false, // 是否显示操作列
		// operateWidth: 150, // 操作列宽
		total: 0, // 总条数
	},
	pageParams: {
		page: 1,
		size: 10,
	},
});

onMounted(() => {
	initTableData();
});

const initTableData = async () => {
	tableOptions.config.loading = true;
	try {
		const query: any = {
			page: tableOptions.pageParams?.page && tableOptions.pageParams.page - 1,
			size: tableOptions.pageParams?.size,
		};
		const { payload } = await getAuditLogs(query);
		tableOptions.config.loading = false;
		tableOptions.data = payload.content;
		tableOptions.config.total = payload.totalElements;
	} catch (e) {
		tableOptions.config.loading = false;
		tableOptions.config.total = 0;
		tableOptions.data = [];
	}
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetFilter) { /* empty */ }
	if (resetPage) {
		tableRef.value?.pageReset();
	} else {
		initTableData();
	}
	tableRef.value?.clearSelection();
};

const onPageChange = async (page: { pageNum: number; pageSize: number }) => {
	if (tableOptions.pageParams) {
		tableOptions.pageParams.page = page.pageNum;
		tableOptions.pageParams.size = page.pageSize;
	}
	initTableData();
};
</script>

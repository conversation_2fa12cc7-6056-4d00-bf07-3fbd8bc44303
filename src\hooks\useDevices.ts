import { ref, onBeforeMount, onBeforeUnmount } from 'vue';
import { getDevices } from '/@/api/devices';
import mittBus from '/@/utils/mitt';
import { Session } from '/@/utils/storage';

export function useDevices() {
	const devices = ref<EmptyArrayType>([]);

	onBeforeMount(() => {
		initAllDevice();
		mittBus.on('devicesChange', () => {
			devices.value = Session.get('all_devices');
		});
	});

	onBeforeUnmount(() => {
		mittBus.off('devicesChange');
	});

	const initAllDevice = () => {
		if (Session.get('all_devices')) {
			const list = Session.get('all_devices');
			devices.value = list;
		} else {
			getDevices({ page: 0, size: 1000, sort: 'type,asc', typeFilter: 10 }).then(({ payload }) => {
				// 二级设备列表转一级
				let temp: DeviceRow[] = [];
				payload.content.forEach((item: DeviceRow) => {
					if (item.channels && item.channels.length > 0) {
						temp = temp.concat(item.channels);
					}
				});
				devices.value = temp;
				Session.set('all_devices', temp);
			});
		}
	};

	// 设备增删改时
	const onDevicesChange = () => {
		Session.remove('all_devices');
		initAllDevice();
		// 通知其他使用到设备列表的页面
		setTimeout(() => {
			mittBus.emit('devicesChange');
		}, 2000);
	};
	return {
		data: devices,
		onDevicesChange,
	};
}

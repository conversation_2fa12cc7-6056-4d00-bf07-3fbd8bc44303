<template>
	<div style="height: 100%">
		<div class="top-radio">
			<img class="back" @click="onToMore" src="/src/assets/sd/data-screen/more.png" alt="" />
			<el-radio-group v-model="animalTag" @change="onRadioChange">
				<el-radio label="" size="large">全部</el-radio>
				<el-radio :label="2" size="large">珍稀</el-radio>
				<el-radio :label="1" size="large">特定</el-radio>
				<el-radio v-if="IsCompanyNmg" :label="0" size="large">内蒙古</el-radio>
			</el-radio-group>
		</div>

		<custom-scrollbar
      v-loading="state.loading"
			ref="scroll"
			contentClass="species"
			:style="{ height: '100%' }"
			wrapperClass="wrapperClass"
			:autoHide="false"
		>
			<div
				class="species-item animate__animated"
				v-for="(item, $index) in state.tableData.data"
				:key="item.eventId"
			>
				<div class="picture">
					<el-image
						:src="item.pictureUrl || item.oriPictureThumbnailUrl || ''"
						fit="cover"
						:preview-src-list="previewSrcList"
						:initial-index="$index"
						:preview-teleported="true"
						title="查看大图"
					>
						<template #placeholder>
							<div class="image-placeholder">
								<el-icon class="is-loading">
									<ele-Loading />
								</el-icon>
							</div>
						</template>
						<template #error>
							<div class="load-error">
								<img
									class="small-img-error"
									src="/src/assets/fullScreen/small-load-error.png"
									title="加载失败"
									alt=""
								/>
							</div>
						</template>
					</el-image>
					<div class="desc">
						<div class="name">
							<span>{{ item.alias }}</span>
							<span>{{ item.discoveredTime }}</span>
						</div>
						<div>{{ item.deviceName }}</div>
					</div>
				</div>
			</div>
		</custom-scrollbar>

		<el-pagination
			v-model:current-page="state.tableData.pageParams.page"
			:page-sizes="[21, 42]"
			background
			:teleported="false"
			v-model:page-size="state.tableData.pageParams.size"
			layout="total, sizes, prev, pager, next, jumper"
			:total="state.tableData.total"
			@size-change="onHandleSizeChange"
			@current-change="onHandleCurrentChange"
		>
		</el-pagination>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue';
import { getRecentDiscovered } from '/@/api/sd/dataScreen/bird';
import CustomScrollbar from 'custom-vue-scrollbar';
import { useRouter } from 'vue-router';
import 'custom-vue-scrollbar/dist/style.css';
import { IsCompanyNmg } from '/@/hooks/useConfig';

const emits = defineEmits(['onActiveMenuChange']);
emits('onActiveMenuChange', 'device');

const router = useRouter();
const onToMore = () => {
	router.back();
};

const state = reactive<ViewBaseState<RecentMonitorEvent>>({
	tableData: {
		filter: {},
		data: [],
		loading: false,
		pageParams: {
			page: 1,
			size: 21,
		},
		total: 0,
	},
});
const animalTag = ref();
const previewSrcList = computed(() => {
	return state.tableData.data.map((item: RecentMonitorEvent) => {
		return item.pictureUrl || item.oriPictureUrl;
	});
});
const getTableData = async () => {
	state.tableData.data = [];
  state.loading = true;
	const query = {
		animalTag: animalTag.value,
		page: state.tableData.pageParams.page - 1,
		size: state.tableData.pageParams.size,
	};
	const { payload } = await getRecentDiscovered(query);
  state.loading = false;
	state.tableData.data = payload.content;
	state.tableData.total = payload.totalElements;
};

const onRadioChange = () => {
	(state.tableData.pageParams.page = 1), getTableData();
};

getTableData();

// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getTableData();
};
const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getTableData();
};
</script>

<style lang="scss" scoped>
/* @import url(); 引入css类 */

@import '/src/theme/mixins/index.scss';

// 修改滚动条样式。
:deep(.scrollbar__thumb) {
	background-color: $sd-screen-success;
}
.top-radio {
	margin: vh(20) 0 vh(20) vw(22);
	.back {
		width: vw(22);
		cursor: pointer;
		margin-right: 40px;
    transform: rotate(180deg);
	}
}
.wrapperClass {
	height: calc(100% - (vh(40) + 40px) - vh(110));
  :deep(.el-loading-mask) {
    background-color: transparent !important;
  }
}

:deep(.species) {
	padding: 0 vw(40);
	display: flex;
	flex-wrap: wrap;
	box-sizing: border-box;
	.species-item {
		width: vw(245);
		height: vh(245);
		margin-right: vw(20.8);
		margin-bottom: vw(20);
		border-radius: 5px;
		overflow: hidden;
		&:nth-child(7n) {
			margin-right: 0;
		}
		&:nth-last-child(-n + 7) {
			margin-bottom: 0;
		}
		display: flex;
		overflow: hidden;
		.picture {
			position: relative;
			width: 100%;
			height: 100%;
			.el-image {
				width: 100%;
				height: 100%;
				cursor: pointer;
				transition: transform 0.2s;
			}
			.el-image:hover {
				transform: scale(1.02);
			}
			.desc {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				position: absolute;
				bottom: 0;
				left: 0;
				z-index: 999;
				width: 100%;
				height: vh(66);
				background-color: rgba(0, 0, 0, 0.3);
				padding: vh(10) vw(5) vh(5);
				.name {
					height: vh(25);
					display: flex;
					justify-content: space-between;
					span {
						line-height: vh(25);
						&:first-child {
							font-size: vw(16);
						}
						&:last-child {
							font-size: vw(12);
						}
					}
				}
			}
		}
	}
}

:deep(.el-radio) {
	margin-right: 40px;
	.el-radio__label {
		color: rgba(255, 255, 255, 1);
		font-size: vw(18);
		font-family: 'AliMaMaShuHeiTi';
	}
	.el-radio__inner {
		width: 16px;
		height: 16px;
		background-color: transparent !important;
		&:hover {
			border-color: $radio-color;
		}
	}
	.is-checked {
		.el-radio__inner {
			width: 16px;
			height: 16px;
			border-color: #fff;
			border-width: vw(3);
			background-color: $radio-color !important;
		}
	}
	.el-radio__input.is-checked + .el-radio__label {
		color: $radio-color !important;
	}
	.el-radio__inner::after {
		display: none;
	}
}

:deep(.el-pagination) {
	margin-top: vh(30);
	justify-content: center;
	.el-popper.is-light .el-popper__arrow::before {
		border-top-color: transparent !important;
		border-left-color: transparent !important;
	}
	.el-input__wrapper {
		border: 1px solid rgb(22, 90, 67) !important;
		background-color: transparent !important;
		box-shadow: none !important;
		padding: 0 11px !important;
	}
	.el-input__inner {
		color: rgba(255, 255, 255, 1);
	}

	.is-active {
		background-color: transparent !important;
		color: #fff !important;
		&::before {
			opacity: 0.65 !important;
		}
	}
	.el-pagination__total,
	.el-pagination__jump {
		color: rgba(255, 255, 255, 0.65);
	}
	.btn-prev,
	.btn-next,
	.number,
	.more {
		position: relative;
		background: transparent;
		color: #fff !important;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-image: linear-gradient(to bottom, rgba(87, 242, 91, 0.3), rgba(87, 242, 91, 0.3));
			opacity: 0.2;
		}
	}
	.el-pager li {
		background-color: inherit !important;
	}

	.btn-next.is-disabled,
	.btn-next:disabled,
	.btn-prev.is-disabled,
	.btn-prev:disabled,
	.el-pager li.is-disabled,
	.el-pager li:disabled {
		background-color: inherit !important;
	}
	.el-popper {
		border: 1px solid $sd-screen-success !important;
		background-color: #0c1e29 !important;
		z-index: 2000;

		.el-select-dropdown__item {
			color: rgba(255, 255, 255, 1);
			padding: 0 10px !important;
		}

		.el-select-dropdown__item.hover,
		.el-select-dropdown__item:hover {
			background-color: transparent !important;
			color: $sd-screen-success;
		}

		.el-select-dropdown__item.selected {
			color: $sd-screen-success;
			font-weight: 700;
		}
	}

	.el-popper.is-light .el-popper__arrow::before {
		border: 1px solid $sd-screen-success !important;
		border-top-color: transparent !important;
		border-left-color: transparent !important;
		background: #010b07 !important;
	}
}
</style>

<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<el-form-item label="设备编号">
					<el-input v-model="state.tableData.filter.num" placeholder="请输入设备编号" clearable>
						<template #suffix>
							<el-icon><ele-Search /></el-icon>
						</template>
					</el-input>
				</el-form-item>
				<el-form-item label="设备名称">
					<el-input v-model="state.tableData.filter.name" placeholder="请输入设备名称" clearable>
						<template #suffix>
							<el-icon><ele-Search /></el-icon>
						</template>
					</el-input>
				</el-form-item>
			</template>

			<template #searchBtns>
				<div v-auths="['*:*:*', 'devices:*:*']">
					<el-button v-if="deviceType !== 4" type="primary" @click="onCreate">
						<template #icon>
							<el-icon><ele-Plus /></el-icon>
						</template>
						新增
					</el-button>

					<!-- <el-button v-if="deviceType!==4" type="success" :disabled="state.selectIds.length !== 1" @click="onUpdateRow(null)">
						<template #icon>
							<el-icon><ele-Edit /></el-icon>
						</template>
						修改
					</el-button> -->
					<!-- <el-button v-if="deviceType!==4" type="danger" :disabled="state.selectIds.length === 0" @click="onBatchDelete">
						<template #icon>
							<el-icon><ele-Delete /></el-icon>
						</template>
						删除
					</el-button> -->
					<el-button v-if="deviceType === 1" type="primary" @click="onCheckStatus">
						<template #icon>
							<el-icon><ele-Warning /></el-icon>
						</template>
						检查设备状态
					</el-button>
					<!-- 巡护设备操作按钮 -->
					<el-button v-if="deviceType === 4" type="primary" @click="onSync">同步巡护设备</el-button>
					<el-button v-if="deviceType === -100 || deviceType === 1" @click="onGoSplitScreen">
						<template #icon>
							<el-icon><ele-Grid /></el-icon>
						</template>
						分屏
					</el-button>
				</div>
			</template>
		</ViewSearch>

		<el-table
			:data="state.tableData.data"
			v-loading="state.tableData.loading"
			style="width: 100%"
			row-key="num"
			default-expand-all
			:indent="30"
			:tree-props="{ children: 'channels' }"
			@selection-change="onSelectionChange"
		>
			<!-- <el-table-column type="selection" width="50" align="center" /> -->
			<el-table-column prop="serialNum" label="序号" align="center" width="60" />
			<el-table-column
				prop="num"
				label="设备编号"
				align="center"
				show-overflow-tooltip
			></el-table-column>
			<el-table-column
				prop="name"
				label="设备名称"
				align="center"
				show-overflow-tooltip
			></el-table-column>
			<el-table-column
				v-if="deviceType === -100"
				prop="productId"
				label="设备IP"
				align="center"
				show-overflow-tooltip
			></el-table-column>
			<!-- 红外设备不存在通道-->
			<template v-if="deviceType !== 2">
				<el-table-column
					prop="channelNo"
					label="通道编号"
					align="center"
					show-overflow-tooltip
				></el-table-column>
				<el-table-column
					prop="channelName"
					label="通道名称"
					align="center"
					show-overflow-tooltip
				></el-table-column>
			</template>
			<!-- 监控设备/防火设备，可管理生产厂商 -->
			<el-table-column
				v-if="deviceType === 1 || deviceType === -100"
				prop="manufacturer"
				label="生产厂商"
				align="center"
				show-overflow-tooltip
			>
				<template #default="{ row }">
					{{
						!row.isDevChannel && typeof row.manufacturer === 'number'
							? Manufacturer[row.manufacturer]
							: ''
					}}
				</template>
			</el-table-column>
			<!-- 监控设备/防火设备，存在视频地址（可快捷复制），并标记视频地址的状态 -->
			<el-table-column
				v-if="deviceType === 1 || deviceType === -100"
				label="视频地址"
				align="center"
				show-overflow-tooltip
			>
				<template #default="{ row }">
					<template v-if="row.sourceType === 3 && row.previewUrl">
						<el-tag
							v-if="row.onlineStatus"
							class="el-tag-circle"
							type="success"
							effect="dark"
							round
						>
							<el-icon><ele-Check /></el-icon>
						</el-tag>
						<el-tag v-else class="el-tag-circle" type="danger" effect="dark" round>
							<el-icon><ele-Close /></el-icon>
						</el-tag>
						<span class="custom-link" @click="copyText(row.previewUrl)">
							{{ row.previewUrl }}
						</span>
					</template>
					<template v-else>
						<span class="custom-link" @click="copyText(row.previewUrl)">
							{{ row.previewUrl }}
						</span>
					</template>
				</template>
			</el-table-column>
			<el-table-column label="坐标" align="center" show-overflow-tooltip>
				<template #default="{ row }">
					{{ row.longitude != null ? row.longitude + ',' + row.latitude : '' }}
				</template>
			</el-table-column>
			<el-table-column label="坐标类型" align="center" show-overflow-tooltip>
				<template #default="{ row }">
					{{ typeof row.coordinateType === 'number' ? Coordinate_Type[row.coordinateType] : '' }}
				</template>
			</el-table-column>
			<!-- <el-table-column label="监测任务数" align="center" width="100">
				<template #default="{row}">
					<el-link type="primary" @click="onDeviceMonitorNumClick(row)">{{ row.sourceType }}</el-link>
				</template>
			</el-table-column> -->
			<el-table-column
				prop="createTime"
				label="创建时间"
				align="center"
				show-overflow-tooltip
			></el-table-column>
			<el-table-column v-auths="['*:*:*', 'devices:*:*']" label="操作" align="center" width="150">
				<template #default="{ row }">
					<template v-if="deviceType !== 4 && !row.isDevChannel">
						<el-button type="primary" text size="small" @click="onUpdateRow(row)">
							<template #icon>
								<el-icon><ele-EditPen /></el-icon>
							</template>
							修改
						</el-button>
						<!-- <el-button type="primary" text size="small" @click="onDelRow(row.id)">
							<template #icon>
								<el-icon><ele-Delete /></el-icon>
							</template>
							删除
						</el-button> -->
					</template>
					<el-button v-show="row.previewUrl" type="primary" text size="small" @click="onRVC(row)">
						<template #icon>
							<i class="iconfont icon-zhibo font15"></i>
						</template>
						实时视频
					</el-button>
					<!-- 巡护设备只有【设备概览】操作 -->
					<el-button
						v-show="deviceType === 4 && !row.isDevChannel"
						type="primary"
						text
						size="small"
						@click="onOverview(row)"
					>
						<template #icon>
							<el-icon><ele-Document /></el-icon>
						</template>
						设备概览
					</el-button>
				</template>
			</el-table-column>
		</el-table>
		<el-pagination
			@size-change="onHandleSizeChange"
			@current-change="onHandleCurrentChange"
			v-model:current-page="state.tableData.pageParams.page"
			v-model:page-size="state.tableData.pageParams.size"
			background
			layout="total, sizes, prev, pager, next, jumper"
			:total="state.tableData.total"
			:page-sizes="[10, 20, 30, 50]"
		>
		</el-pagination>

		<CreateDialog
			ref="createDialogRef"
			:deviceType="deviceType"
			@refresh="onRefresh"
			@resetDevices="onResetDevices"
			@onCreateMonitor="onCreateMonitor"
		/>
		<CreateFireDialog ref="createFireDialogRef" @refresh="onRefresh" />
		<OverviewDialog ref="overviewDialogRef"></OverviewDialog>

		<!-- <DeviceMonitorDialog
			ref="deviceMonitorDialog"
			@onCreateMonitor="onCreateMonitor"
			@onUpdateMonitor="onUpdateMonitor"
			@refresh="getTableData"
		></DeviceMonitorDialog> -->
		<CreateMonitorDialog
			ref="createMonitorDialog"
			:devices="devices.data.value"
			:aiModels="aiModels.data.value"
			@refresh="getTableData"
		></CreateMonitorDialog>
	</div>
</template>

<script setup lang="ts" name="Device">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getDevices, getFireDevices, checkStatus, syncPatrolRobots } from '/@/api/devices';
import { useDevices } from '/@/hooks/useDevices';
import { useAiModels } from '/@/hooks/useAiModels';
import commonFunction from '/@/utils/commonFunction';
import { Coordinate_Type, Manufacturer } from '/@/utils/constants';
const { copyText } = commonFunction();

// 引入组件
const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const CreateDialog = defineAsyncComponent(() => import('./component/dialog.vue'));
const CreateFireDialog = defineAsyncComponent(() => import('./component/fireDialog.vue'));
const OverviewDialog = defineAsyncComponent(() => import('./component/overview.vue'));
// const DeviceMonitorDialog = defineAsyncComponent(() => import('./component/monitorDialog.vue'));
const CreateMonitorDialog = defineAsyncComponent(() => import('../monitors/dialog.vue'));

// 定义变量内容
const props = defineProps<{
	// 设备类型（同查询接口参数typeFilter）：1监控设备/萤石云设备 2红外设备 4巡护设备 -100防火设备
	deviceType: number;
}>();
const router = useRouter();
const devices = useDevices();
const aiModels = useAiModels();
const state = reactive<ViewBaseState<DeviceRow | FireDeviceRow>>({
	tableData: {
		filter: {
			// typeFilter: '', // 1监控设备/萤石云设备 2红外设备 4巡护设备
			name: '',
			num: '',
			sort: 'num,asc',
		},
		data: [],
		total: 0,
		loading: false,
		pageParams: {
			page: 1,
			size: 10,
		},
	},
	selectIds: [],
});

onMounted(() => {
	getTableData();
});

const getTableData = () => {
	if (props.deviceType === -100) {
		getFireDeviceData();
	} else {
		getDeviceData();
	}
};
// 获取红外、监控、巡护设备列表
const getDeviceData = async () => {
	state.tableData.loading = true;
	const { page, size } = state.tableData.pageParams;
	const query = {
		typeFilter: props.deviceType,
		page: page - 1,
		size,
		...state.tableData.filter,
	};
	const { payload } = await getDevices(query);
	// 设备channels数大于1时显示设备-通道结构（两级）
	const temp: DeviceRow[] = [];
	payload.content.forEach((item: DeviceRow, $index: number) => {
		if (item.channels && item.channels.length === 1) {
			temp.push({
				...item.channels[0],
				serialNum: (page - 1) * size + $index + 1,
				isDevChannel: false,
			});
		} else {
			temp.push({
				id: `tree${$index}-${item.num}`, // id唯一化
				productId: item.channels[0].productId,
				num: item.num,
				name: item.name,
				manufacturer: item.channels[0].manufacturer,
				previewUrl: '',
				sourceType: item.channels[0].sourceType,
				coordinateType: item.channels[0].coordinateType,
				longitude: item.longitude,
				latitude: item.latitude,
				createTime: item.channels[0].createTime,
				channels: item.channels.map((channel) => {
					return {
						id: channel.id,
						channelNo: channel.channelNo,
						channelName: channel.channelName,
						isDevChannel: true,
					};
				}),
				serialNum: (page - 1) * size + $index + 1,
				isParentNode: true, // 是否为父节点，用于修改操作
				isDevChannel: false,
			});
		}
	});
	state.tableData.data = temp;
	state.tableData.total = payload.totalElements;
	state.tableData.loading = false;
};
// 获取防火设备列表
const getFireDeviceData = async () => {
	state.tableData.loading = true;
	const { page, size } = state.tableData.pageParams;
	const query = {
		page: page - 1,
		size,
		...state.tableData.filter,
	};
	const { payload } = await getFireDevices(query);
	// 每个防火设备都对应热成像和可见光两个通道
	payload.content.forEach((item: FireDeviceRow, $index: number) => {
		item.id = item.num;
		item.serialNum = (page - 1) * size + $index + 1;
		item.channels = [
			{
				id: item.infraredId, // 实时视频的deviceId
				channelNo: 'channel1',
				channelName: '热成像',
				previewUrl: item.infraredVideoUrl,
				manufacturer: item.manufacturer,
				ifControl: item.ifControl,
				isDevChannel: true,
			},
			{
				id: item.visibleLightId, // 实时视频的deviceId
				channelNo: 'channel2',
				channelName: '可见光',
				previewUrl: item.visibleLightVideoUrl,
				manufacturer: item.manufacturer,
				ifControl: item.ifControl,
				isDevChannel: true,
			},
		];
	});
	state.tableData.data = payload.content;
	state.tableData.total = payload.totalElements;
	state.tableData.loading = false;
};
const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetPage) state.tableData.pageParams.page = 1;
	if (resetFilter) {
		state.tableData.filter.name = '';
		state.tableData.filter.num = '';
	}
	getTableData();
};
const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getTableData();
	resetScroll();
};
const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getTableData();
	resetScroll();
};
// 重置滚动条
const resetScroll = () => {
	// .layout-backtop-header-fixed .el-scrollbar__wrap
	const scrollEle = document.querySelector('.layout-backtop .el-scrollbar__wrap');
	(<HTMLDivElement>scrollEle).scrollTo({
		top: 0,
		behavior: 'smooth',
	});
};
// 设备更新时（增/改/删）
const onResetDevices = () => {
	devices.onDevicesChange();
};
// 触发表格多选
const onSelectionChange = (selectRows: (DeviceRow | FireDeviceRow)[]) => {
	state.selectIds = selectRows.map((item: DeviceRow | FireDeviceRow) => item.id);
};

const createDialogRef = ref<InstanceType<typeof CreateDialog>>();
const createFireDialogRef = ref<InstanceType<typeof CreateFireDialog>>();
const onCreate = () => {
	if (props.deviceType === -100) {
		createFireDialogRef.value?.openDialog();
	} else {
		createDialogRef.value?.openDialog();
	}
};
const onUpdateRow = (row: DeviceRow | FireDeviceRow | null) => {
	// if (!row) {
	// 	row = <DeviceRow | FireDeviceRow>state.tableData.data.find((item) => item.id === state.selectIds[0]);
	// }
	if (props.deviceType === -100) {
		createFireDialogRef.value?.openDialog(<FireDeviceRow>row);
	} else {
		createDialogRef.value?.openDialog(<DeviceRow>row);
	}
};
// const onBatchDelete = () => {
// 	ElMessageBox({
// 		title: '提示',
// 		message: '此操作将永久删除，是否继续?',
// 		type: 'warning',
// 		showCancelButton: true,
// 	}).then(async () => {
// 		await batchDeleteDevice(state.selectIds);
// 		ElMessage.success('删除成功');
// 		onRefresh();
// 		onResetDevices();
// 	})
// 	.catch(() => {})
// }
// const onDelRow = (rowId: string) => {
// 	ElMessageBox({
// 		title: '提示',
// 		message: '此操作将永久删除，是否继续?',
// 		type: 'warning',
// 		showCancelButton: true,
// 	}).then(async () => {
//     if (props.deviceType === -100) {
//       await deleteFireDevice({ num: rowId });
//     } else {
//       await deleteDevice(rowId);
//     }
// 		ElMessage.success('删除成功');
// 		onRefresh();
// 		onResetDevices();
// 	})
// 	.catch(() => {})
// };

// 监控设备 - 实时获取设备状态
const onCheckStatus = async () => {
	ElMessage.warning('开始检查');
	await checkStatus();
	ElMessage.success('检查完成，状态已更新');
	onRefresh();
};

// 监控设备/防护设备 - 实时视频
const onRVC = async (row: DeviceRow | FireDeviceChannel) => {
	router.push({
		path: '/device/rvc',
		query: {
			deviceId: row.id,
			rtspUrl: row.previewUrl,
			manufacturer: row.manufacturer,
			ifControl: row.ifControl || 0,
		},
	});
};

// 巡护设备 - 同步
const onSync = async () => {
	await syncPatrolRobots();
	ElMessage.success('巡护设备同步成功');
	onRefresh(true, true);
};
// 巡护设备 - 设备概览
const overviewDialogRef = ref<InstanceType<typeof OverviewDialog>>();
const onOverview = async (row: DeviceRow) => {
	overviewDialogRef.value?.openDialog(row.productId);
};

// 关联监测任务
// const deviceMonitorDialog = ref<InstanceType<typeof DeviceMonitorDialog>>();
// const onDeviceMonitorNumClick = (row: DeviceRow) => {
// 	deviceMonitorDialog.value?.openDialog(<string>row.id, row.name);
// };
// const onUpdateMonitor = (deviceId: string, data: MonitorRow) => {
// 	createMonitorDialog.value?.openDialog('edit', data, deviceId);
// };
const createMonitorDialog = ref<InstanceType<typeof CreateMonitorDialog>>();
const onCreateMonitor = (newDevice: { id: string; sourceType: number }) => {
	createMonitorDialog.value?.openDialog('add', undefined, newDevice);
};

// 跳转到分屏页面
const onGoSplitScreen = async () => {
	router.push({
		path: '/device/fire/splitScreen',
		query: {
			deviceType: props.deviceType.toString(),
		},
	});
};
</script>

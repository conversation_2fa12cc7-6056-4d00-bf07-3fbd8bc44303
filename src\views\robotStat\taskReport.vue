<template>
	<div class="report-container">
		<div class="report-container-top">
			<div class="top-title">
				<!-- <img src="../../assets/fullScreen/robotStat/taskReport/title.png" alt="" /> -->
			</div>

			<!-- Table Search -->
			<div class="top-tSearch">
				<div class="tab">
					<div
						v-for="item in state.reportTypes"
						:key="item.eventType"
						class="tab-item"
						@click="onReportTypeChange(item.eventType)"
					>
						<img
							class="tab-item-img"
							:width="item.width"
							:src="
								state.tableData.filter.eventType === item.eventType
									? item.activePicture
									: item.defaultPicture
							"
							alt=""
						/>
						<div
							class="a-line"
							:style="{ width: item.width }"
							:class="{ 'a-line-bg': state.tableData.filter.eventType === item.eventType }"
						></div>
					</div>
				</div>
				<div style="width: 450px; height: 32px; display: flex; align-items: center">
					选择时间：
					<el-date-picker
						v-if="state.tableData.filter.eventType === 4"
						class="el-date-editor_ld"
						popper-class="el-popper_ld"
						v-model="date"
						type="daterange"
						range-separator="-"
						start-placeholder="点击选择开始时间"
						end-placeholder="点击选择结束时间"
						:teleported="false"
						format="YYYY-MM-DD"
						:clearable="false"
						value-format="YYYY-MM-DD"
						@change="handleDateChange"
					/>
					<el-date-picker
						v-else
						class="el-date-editor_largeScreen"
						popper-class="el-popper_largeScreen"
						v-model="state.tableData.filter.date"
						type="date"
						:clearable="false"
						placeholder="点击选择日期"
						:teleported="false"
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD"
						:disabled-date="disabledDate"
						@change="handleDateChange"
					/>
				</div>
			</div>

			<!-- Table -->
			<div class="top-table">
				<div class="table-tips">
					<span class="round-dot success">正常</span>
					<span class="round-dot error">异常</span>
					<span class="round-dot warning">漏检</span>
				</div>
				<el-table
					class="el-table_largeScreen"
					:data="state.tableData.data"
					v-loading="state.tableData.loading"
					style="width: 100%; height: 100%"
					highlight-current-row
					:header-cell-style="{
						color: '#fff',
						background: 'transparent',
						borderBottom: '1px solid rgba(255,255,255, 0.3)',
						backgroundImage: 'linear-gradient(to bottom, #000, rgba(65, 169, 127, 0.35))',
					}"
					:cell-style="{
						color: '#fff',
						borderBottom: '1px solid rgba(255,255,255, 0.3)',
					}"
					:row-style="{
						color: '#fff',
						borderBottom: '1px solid rgba(255,255,255, 0.3)',
					}"
				>
					<el-table-column
						type="index"
						label="序号"
						:index="tableIndexMethod"
						align="center"
						width="94"
					/>
					<el-table-column prop="name" label="抓拍点" align="center" width="435">
						<template v-slot="{ row }">
							<div class="capture-point">
								<span>{{ row.name }}</span>
								<img src="../../assets/fullScreen/robotStat/taskReport/capture.png" alt="" />
							</div>
						</template>
					</el-table-column>
					<el-table-column
						v-if="state.tableData.filter.eventType === 4"
						v-for="(col, index) in columns"
						min-width="100"
						prop="time1"
						type="index"
						:label="col"
						align="center"
					>
						<template v-slot="{ row }">
							<template v-if="row[col] === 1">
								<span class="round-dot success"></span>
							</template>
							<template v-else-if="row[col] === 2">
								<span class="round-dot error"></span>
							</template>
							<template v-else>
								<span class="round-dot warning"></span>
							</template>
						</template>
					</el-table-column>

					<el-table-column
						v-else
						v-for="(value, key) in dateList"
						min-width="100"
						prop="time1"
						type="index"
						:label="key"
						align="center"
					>
						<template v-slot="{ row }">
							<template v-if="row[key] === 1">
								<span class="round-dot success"></span>
							</template>
							<template v-else-if="row[key] === 2">
								<span class="round-dot error"></span>
							</template>
							<template v-else>
								<span class="round-dot warning"></span>
							</template>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</div>
		<el-pagination
			class="el-pagination_largeScreen"
			v-model:current-page="state.tableData.pageParams.page"
			:page-sizes="[10, 30, 50]"
			background
			v-model:page-size="state.tableData.pageParams.size"
			layout="total, sizes, prev, pager, next, jumper"
			:total="state.tableData.total"
			@size-change="onHandleSizeChange"
			@current-change="onHandleCurrentChange"
			:teleported="false"
		>
		</el-pagination>
	</div>
</template>

<script lang="ts" setup>
import { reactive, onMounted, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { getRobotPointsTaskStat } from '/@/api/robotStat';
import { formatDate } from '/@/utils/formatTime';

import other from '/@/utils/other';
const date = ref([formatDate(new Date(), 'YYYY-mm-dd'), formatDate(new Date(), 'YYYY-mm-dd')]);

const dateList = ref<any>({});

for (let hour = 8; hour <= 19; hour++) {
	const time = `${String(hour).padStart(2, '0')}:${'00'}`;
	dateList.value[time] = null;
}

console.log('dateList', dateList.value);
const disabledDate = (time: Date) => {
	return time.getTime() > Date.now();
};
const columns = ref<string[]>();
const handleDateChange = () => {
	console.log('1', date.value);
	if (state.tableData.filter.eventType === 4) {
		// 路灯
		state.tableData.filter.startDate = date.value[0];
		state.tableData.filter.endDate = date.value[1];
		columns.value = getDatesBetween(date.value[0], date.value[1]);
	}

	resetFilter();
};

const getDatesBetween = (start: string, end: string) => {
	// 将字符串转换为Date对象
	const startDate = new Date(start);
	const endDate = new Date(end);

	// 初始化日期数组
	const dates = [];

	// 当开始日期小于结束日期时继续循环
	while (startDate <= endDate) {
		// 将开始日期添加到数组
		dates.push(formatDate(new Date(startDate), 'YYYY-mm-dd'));
		// 将开始日期增加一天
		startDate.setDate(startDate.getDate() + 1);
	}

	return dates;
};
const state = reactive<ViewBaseState<statReportRow>>({
	// 类型
	reportTypes: [
		{
			defaultPicture: other.getStaticImag('fullScreen/robotStat/taskReport/ljt.png'),
			activePicture: other.getStaticImag('fullScreen/robotStat/taskReport/a-ljt.png'),
			eventType: 17,
			width: 50,
		},
		{
			defaultPicture: other.getStaticImag('fullScreen/robotStat/taskReport/ld.png'),
			activePicture: other.getStaticImag('fullScreen/robotStat/taskReport/a-ld.png'),
			eventType: 4,
			width: 34,
		},
		{
			defaultPicture: other.getStaticImag('fullScreen/robotStat/taskReport/bch.png'),
			activePicture: other.getStaticImag('fullScreen/robotStat/taskReport/a-bch.png'),
			eventType: 3,
			width: 50,
		},
	],
	tableData: {
		filter: {
			eventType: 17,
			startDate: '',
			endDate: '',
			date: formatDate(new Date(), 'YYYY-mm-dd'),
		},
		data: [],
		loading: false,
		pageParams: {
			page: 1,
			size: 10,
		},
		total: 0,
	},
});
const getTableData = async () => {
	state.tableData.loading = true;
	// # 以下代码会使分页有问题
	// state.tableData.total = 0;
	state.tableData.data = [];
	const query = {
		eventType: 17,
		startDate: date.value[0],
		endDate: date.value[1],
		date: '',
		page: state.tableData.pageParams.page - 1,
		size: state.tableData.pageParams.size,
		...state.tableData.filter,
	};

	if (state.tableData.filter.eventType !== 4) {
		query.startDate = '';
		query.endDate = '';
	} else {
		query.date = '';
	}

	const { payload } = await getRobotPointsTaskStat(query);

	state.tableData.data = payload.content.map((item: any) => {
		let pointPatrol = {} as any;
		if (item?.robotPointPatrolOverviews?.length) {
			item.robotPointPatrolOverviews.forEach((subItem: any) => {
				pointPatrol[subItem.date] = subItem.status;
			});
		}
		return {
			name: item.pointName,
			...pointPatrol,
		};
	});
	// console.log('arr', arr);
	state.tableData.total = payload.totalElements;
	state.tableData.loading = false;
};

const onReportTypeChange = (eventType: number) => {
	if (state.tableData.filter.eventType === eventType) return;
	state.tableData.filter.eventType = eventType;
	if (eventType === 4) {
		date.value = [state.tableData.filter.date, state.tableData.filter.date];
	} else {
		state.tableData.filter.date = date.value[0];
	}
	resetFilter();
};
// 自定义表格序号
const tableIndexMethod = (index: number) => {
	const { page, size } = state.tableData.pageParams;
	return (page - 1) * size + (index + 1);
};

const resetFilter = () => {
	state.tableData.pageParams.page = 1;
	state.tableData.pageParams.size = 10;

	if (state.tableData.filter.eventType === 4) {
		// 路灯
		state.tableData.filter.startDate = date.value[0];
		state.tableData.filter.endDate = date.value[1];
		columns.value = getDatesBetween(date.value[0], date.value[1]);
	}
	state.tableData.total = 0;
	getTableData();
};
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getTableData();
};
const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getTableData();
};
onMounted(() => {
	getTableData();
});
</script>

<style lang="scss" scoped>
.report-container {
	height: 100%;

	&-top {
		// border: 1px solid rgba(104, 207, 173, 0.5);
		padding: 0 vw(20) vh(30);
		height: calc(100% - 52px);
		display: flex;
		flex-direction: column;
		background: url('/src/assets/fullScreen/robotStat/taskReport/bg.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;

		.top-title {
			height: vh(45);
			display: flex;
			align-items: center;
			// background: url('../../assets/fullScreen/robotStat/title-bg.png');
			// background-repeat: no-repeat;
			// background-size: 26% 100%;
			// background-position: 0 48%;
			img {
				width: vw(105);
				height: vh(29);
				object-fit: cover;
			}
		}

		.el-date-editor_ld {
			// :deep(.el-popper__arrow::before) {
			// 	border-bottom-color: none !important;
			// }
		}
		.top-tSearch {
			margin: vh(20) 0 vh(24);
			:deep(.el-popper.is-pure) {
				padding: 1px;
			}
			:deep(.el-date-editor.el-input__wrapper) {
				border-radius: 50px !important;
				border: 1px solid rgba(104, 207, 173, 1) !important;
				border: none;
				background-color: transparent !important;
				box-shadow: none !important;
				padding: 0 11px !important;
			}
			:deep(.el-date-range-picker__content.is-left) {
				background-color: #010b07;
				overflow: hidden;
				border-right: 1px solid rgba(104, 207, 173, 1) !important;
			}
			:deep(.el-date-range-picker__content.is-right) {
				background-color: #010b07;
			}
		}
		.top-table {
			height: calc(100% - vh(125 + 20 + 24));
			position: relative;
			.table-tips {
				position: absolute;
				right: 0;
				top: vh(-36);
				span {
					margin: 0 12px;
					color: rgba(255, 255, 255, 1);
					font-size: 14px;
				}
			}

			:deep(.el-table_largeScreen.el-table) {
				--el-table-current-row-bg-color: linear-gradient(to bottom, #000, rgba(65, 169, 127, 0.35));
				border: 1px solid rgba(255, 255, 255, 0.3);
				background-color: transparent !important;
				--el-table-border-color: none;
			}
			:deep(.capture-point) {
				display: flex;
				justify-content: space-between;
				align-items: center;
				img {
					width: 18px;
					height: 18px;
					object-fit: contain;
					cursor: pointer;
				}
			}
			.round-dot {
				&::before {
					content: '';
					display: inline-block;
					width: 16px;
					height: 16px;
					border-radius: 50%;
					margin-right: 5px;
					border: 1px solid transparent;
					box-sizing: border-box;
					position: relative;
					top: 3px;
				}
				&.success::before {
					border-color: #68cfad;
				}
				&.error::before {
					background: #f74a4a;
					border-color: #f74a4a;
				}
				&.warning::before {
					background: #f5d623;
					border-color: #f5d623;
				}
			}
		}
	}
	:deep(.el-popper_ld) {
		.el-popper__arrow::before {
			border: 1px solid rgb(104, 207, 173) !important;
			background: #010b07 !important;
		}
	}
	:deep(.el-popper_largeScreen) {
		.el-popper__arrow::before {
			border: 1px solid rgba(104, 207, 173, 1) !important;
			border-bottom-color: transparent !important;
			border-right-color: transparent !important;
			background: #010b07 !important;
		}
	}

	:deep(.el-date-table td.start-date .el-date-table-cell__text) {
		border: 1px solid #22d69f;
	}
	:deep(.el-date-table td.end-date .el-date-table-cell__text) {
		border: 1px solid #22d69f;
	}
	:deep(.el-date-table td.in-range .el-date-table-cell:hover) {
		background-color: transparent;
		color: #22d69f;
	}
	:deep(.el-date-table td.current:not(.disabled) .el-date-table-cell__text) {
		background-color: #000;
		border: 1px solid #22d69f;
	}
	:deep(.el-date-range-picker) {
		--el-datepicker-inrange-bg-color: rgba(14, 255, 204, 0.3);
		--el-datepicker-active-color: #000;
	}

	:deep(.el-date-editor .el-range-input) {
		color: #fff;
	}
	:deep(.el-range-separator) {
		color: #fff;
	}
	:deep(.el-input__inner) {
		color: #fff;
		text-align: center;
	}

	:deep(.el-date-table td.disabled .el-date-table-cell) {
		background-color: transparent;
		// background-color: var(--el-text-color-placeholder);

		.el-date-table-cell__text {
			color: var(--el-text-color-regular);
		}
	}
}
</style>

<template>
	<div class="notice-container" v-if="recentMonitorEvent">
		<div class="notice">
			<img class="icon" src="/src/assets/sd/data-screen/notice.png" alt="" />
			<span>{{ recentMonitorEvent }}</span>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, inject, watch, Ref, onUnmounted } from 'vue';

const sseData = inject<Ref<SSEData>>('sseData');
const recentMonitorEvent = ref('');

// 用于存储计时器的引用
let timer: NodeJS.Timeout;

watch(
	sseData!,
	(newValue) => {
		if (newValue) {
			recentMonitorEvent.value = `${newValue.createTime} ${newValue.text}`;
			clearTimeout(timer);
			timer = setTimeout(() => {
				recentMonitorEvent.value = '';
			}, 30 * 1000); // 30秒后过期
		}
	},
	{ immediate: true }
);

onUnmounted(() => {
	timer && clearTimeout(timer);
});
</script>

<style lang="scss" scoped>
.notice-container {
	width: 100%;
	position: absolute;
	left: 0;
	bottom: vw(24);
	z-index: 9;
	.notice {
		width: vw(980);
		height: vw(34);
		display: flex;
		align-items: center;
		margin: 0 auto;
		background: linear-gradient(
			to bottom,
			rgba(0, 0, 0, 0.01),
			rgba(0, 0, 0, 0.1) 40%,
			rgba(55, 255, 157, 0.4)
		);
		position: relative;
		border-radius: 1px;
		font-size: vw(15);
		&::before {
			content: '';
			display: block;
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 1px;
			background: linear-gradient(to right, transparent, rgba(55, 255, 157, 0.5), transparent);
		}
		&::after {
			content: '';
			display: block;
			position: absolute;
			left: 0;
			bottom: 0;
			width: 100%;
			height: 1px;
			background: linear-gradient(to right, transparent, rgba(55, 255, 157, 0.5), transparent);
		}
		.icon {
			width: vw(20);
			margin: 0 vw(12);
		}
	}
}
</style>

<template>
	<SideContainer direction="left" v-model="isLeftCollapsed">
		<div class="module stat">
			<div class="module-title">
				<span
					><img
						class="species"
						src="/src/assets/sd/data-screen/species-title.png"
						alt=""
					/>物种统计</span
				>
				<img @click="onToMore" src="/src/assets/sd/data-screen/more.png" alt="" />
			</div>
			<div class="module-cont stat-cont">
				<div :key="eventType" class="stat-left"></div>
				<div class="stat-right">
					<div class="stat-item" v-for="item in statistics" :key="item.key">
						<span class="label"
							>{{ item.label }} <span class="unit">({{ item.unit }})</span></span
						>
						<span class="count" :class="getWidthClass"
							><countTo :startVal="0" :endVal="item.count" :duration="2000"></countTo
						></span>
					</div>
				</div>
			</div>
		</div>

		<div class="module monitor-trend">
			<div class="module-title">
				<span
					><img class="trend" src="/src/assets/sd/data-screen/trend.png" alt="" />物种趋势
				</span>
			</div>
			<div class="module-cont monitor-trend-cont">
				<Echarts
					id="monitorTrendChart"
					ref="monitorTrendChartRef"
					v-loading="monitorTrendLoading"
					element-loading-custom-class="chart-loading-class"
				></Echarts>
			</div>
		</div>
		<!-- monitor trend -->
		<div class="module monitor-trend">
			<div class="module-title">
				<span
					><img class="trend" src="/src/assets/sd/data-screen/trend.png" alt="" />种群栖息趋势
				</span>
			</div>

			<div class="module-cont monitor-trend-cont">
				<Echarts
					id="populationTrendChart"
					ref="populationTrendChartRef"
					v-loading="chartLoading"
					element-loading-custom-class="chart-loading-class"
				></Echarts>
			</div>
		</div>
	</SideContainer>
</template>

<script setup lang="ts">
import { ref, inject, Ref, watch, computed } from 'vue';
import SideContainer from './component/sideContainer/index.vue';
import {
	getSpeciesStat,
	getSpeciesTrend,
	getSpeciesPopulationTrend,
} from '/@/api/sd/dataScreen/bird';
import {
	mergeLineOptions,
	ECOption,
	chartLinearColors,
	transDate,
} from '/@/components/echarts/mergeOptions';
import Echarts from '/@/components/echarts/echarts.vue';
import { useRouter } from 'vue-router';
import { createQuery } from './utils/query';

type Chart = InstanceType<typeof Echarts>;
const router = useRouter();
const isLeftCollapsed = ref(false);

const timeType = inject('timeType') as Ref<TimeType>;
const eventType = inject('eventType') as Ref<string>;
const getWidthClass = computed(() => {
	// 检查是否有任何一个数字长度超过3
	const hasLongNumber = statistics.value.some((item) => String(item.count).length > 3);
	// 如果有长数字，返回窄宽度类名
	return hasLongNumber ? 'count-length4' : '';
});
// 物种统计
const statistics = ref([
	{
		key: 'speciesType',
		label: '发现种类',
		unit: '种',
		count: 0,
		ratiokey: 'selectedRatiosOfSpeciesType',
		ratio: 0,
	},
	{
		key: 'speciesFrequency',
		label: '发现频次',
		unit: '次',
		count: 0,
		ratiokey: 'selectedRatiosOfSpeciesFrequency',
		ratio: 0,
	},
	{
		key: 'speciesNum',
		label: '发现数量',
		unit: '只',
		count: 0,
		ratiokey: 'selectedRatiosOfSpeciesNum',
		ratio: 0,
	},
]);
const getStats = async () => {
	const query = createQuery({
		timeType: timeType.value,
		eventType: eventType.value,
	});
	const { payload } = await getSpeciesStat(query);
	statistics.value.forEach((item) => {
		item.count = payload[item.key] || 0;
	});
};

// 最近发现
const onToMore = () => {
	router.push({
		path: '/screen/rankDetail',
		query: {
			pagePath: 'species',
			timeType: timeType.value,
			eventType: eventType.value,
		},
	});
};

// 物种趋势
const monitorTrendChartRef = ref<Chart>();
const monitorTrendLoading = ref(false);
const getMonitorTrend = async () => {
	monitorTrendLoading.value = true;
	try {
		const query = createQuery({
			timeType: timeType.value,
			eventType: eventType.value,
		});
		const { payload } = await getSpeciesTrend(query);
		const chartData = payload.map((et: SpeciesEvent) => {
			const date = transDate(timeType.value, et.date);
			return [date, et.speciesNum, et.speciesFrequency];
		});
		const options: ECOption = {
			dataset: {
				source: chartData,
			},
			color: chartLinearColors.slice(0, 2),
			series: [
				{
					type: 'line',
					name: '发现物种频次',
					symbol: 'circle',
					symbolSize: 6,
					showSymbol: false,
					areaStyle: {
						color: chartLinearColors[3],
					},
				},
				{
					type: 'line',
					name: '发现物种数量',
					symbol: 'circle',
					symbolSize: 6,
					showSymbol: false,
					areaStyle: {
						color: chartLinearColors[2],
					},
				},
			],
		};
		monitorTrendChartRef.value?.resetOption(mergeLineOptions(options));
	} catch (error) {
		console.error('获取物种趋势数据失败:', error);
	} finally {
		monitorTrendLoading.value = false;
	}
};

// 种群栖息趋势
const populationTrendChartRef = ref<InstanceType<typeof Echarts>>();
const chartLoading = ref(false);
const getPopulationMonitorTrend = async () => {
	chartLoading.value = true;
	try {
		const query = createQuery({
			timeType: timeType.value,
			eventType: eventType.value,
		});
		const { payload } = await getSpeciesPopulationTrend(query);
		const chartData = payload.map((et: SpeciesEvent) => {
			const date = transDate(timeType.value, et.date);
			return [date, et.speciesNum];
		});
		const options: ECOption = {
			dataset: {
				source: chartData,
			},
			color: [chartLinearColors[0]],
			series: [
				{
					type: 'line',
					name: '发现种群数量',
					symbol: 'circle',
					symbolSize: 6,
					showSymbol: false,
					areaStyle: {
						color: chartLinearColors[2],
					},
				},
			],
		};
		populationTrendChartRef.value?.resetOption(mergeLineOptions(options));
	} catch (error) {
		console.error('获取趋势数据失败:', error);
	} finally {
		chartLoading.value = false;
	}
};

watch(
	[timeType, eventType],
	() => {
		getStats();
		getMonitorTrend();
		getPopulationMonitorTrend();
	},
	{ immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.stat {
	.stat-cont {
		display: flex;
		align-items: center;
		height: vw(139);
		margin-bottom: vw(20);
		.stat-left {
			width: vw(206);
			height: vw(109);
			animation: float-effect 8s ease-in-out infinite;
			transform-origin: center center;
			background: url('/@/assets/sd/data-screen/species-stat-all.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
		}
		.stat-right {
			flex: 1;
			padding-left: vw(8);
			.stat-item {
				height: vw(37);
				display: flex;
				align-items: center;

				&:nth-child(2) {
					margin-top: vw(9);
					margin-bottom: vw(9);
				}
				.label {
					line-height: vw(37);
					color: rgba(152, 247, 215, 1);
					font-size: vw(16);
					.unit {
						font-size: vw(14);
					}
				}
				.count {
					flex: 1;
					line-height: vw(37);
					font-size: vw(29);
					font-family: 'ALiMaMaShuHeiTi';
					text-align: right;
				}
				.count-length4 {
					font-size: vw(20);
				}
			}
		}
	}
}
.monitor-trend .monitor-trend-cont {
	width: 100%;
	height: vh(280);
}

/* 硬件加速的滑动效果 */
.flip-enter-active,
.flip-leave-active {
	transition: all 0.3s ease;
	transform: translateZ(0); /* 启用硬件加速 */
}

.flip-enter-from {
	opacity: 0;
	transform: translateX(-20px) translateZ(0);
}

.flip-leave-to {
	opacity: 0;
	transform: translateX(20px) translateZ(0);
}
</style>

<template>
	<div class="data-screen-date-picker">
		<el-date-picker
			v-model="startDate"
			type="datetime"
			popper-class="date-picker-popper"
			:placeholder="startPlaceholder"
			:disabled-date="disabledStartDate"
			:teleported="false"
			@change="handleStartDateChange"
			value-format="YYYY-MM-DD HH:mm:ss"
			format="YYYY-MM-DD HH:mm:ss"
		/>
	</div>
	<div class="to mr10 ml10">至</div>
	<div class="data-screen-date-picker">
		<el-date-picker
			v-model="endDate"
			type="datetime"
			popper-class="date-picker-popper"
			:placeholder="endPlaceholder"
			:disabled-date="disabledEndDate"
			:default-time="endDefaultTime"
			:teleported="false"
			@change="handleEndDateChange"
			value-format="YYYY-MM-DD HH:mm:ss"
			format="YYYY-MM-DD HH:mm:ss"
		/>
	</div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import dayjs from 'dayjs';

interface Props {
	startTime?: string;
	endTime?: string;
	startPlaceholder?: string;
	endPlaceholder?: string;
	disableAfterToday?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
	startTime: '',
	endTime: '',
	disableAfterToday: true,
	startPlaceholder: '选择开始时间',
	endPlaceholder: '选择结束时间',
});

const emit = defineEmits(['update:startTime', 'update:endTime', 'change']);

const startDate = ref<string>(props.startTime);
const endDate = ref<string>(props.endTime);

// 设置结束日期的默认时间为 23:59:59
const endDefaultTime = ref(new Date(2000, 1, 1, 23, 59, 59));

// 禁用开始日期
const disabledStartDate = (time: Date) => {
	// 如果设置了禁用今天之后的日期
	if (props.disableAfterToday) {
		if (dayjs(time).isAfter(dayjs(), 'day')) {
			return true;
		}
	}

	// 如果已选择结束日期，则禁用结束日期之后的日期
	if (endDate.value) {
		return dayjs(time).isAfter(dayjs(endDate.value));
	}

	return false;
};

// 禁用结束日期
const disabledEndDate = (time: Date) => {
	// 如果设置了禁用今天之后的日期
	// if (props.disableAfterToday) {
	// 	if (dayjs(time).isAfter(dayjs(), 'day')) {
	// 		return true;
	// 	}
	// }

	// 如果已选择开始日期，则禁用开始日期之前的日期
	if (startDate.value) {
		return dayjs(time).isBefore(dayjs(startDate.value));
	}

	return false;
};

// 监听外部值变化
watch(
	() => props.startTime,
	(newVal) => {
		startDate.value = newVal;
	}
);

watch(
	() => props.endTime,
	(newVal) => {
		endDate.value = newVal;
	}
);

// 处理开始日期变化
const handleStartDateChange = (val: string | null) => {
	const newStartTime = val || '';
	emit('update:startTime', newStartTime);
	emit('change', { startTime: newStartTime, endTime: endDate.value });

	// 如果选择的开始时间晚于结束时间，清空结束时间
	if (val && endDate.value && dayjs(val).isAfter(dayjs(endDate.value))) {
		endDate.value = '';
		emit('update:endTime', '');
		emit('change', { startTime: newStartTime, endTime: '' });
	}
};

// 处理结束日期变化
const handleEndDateChange = (val: string | null) => {
	const newEndTime = val || '';
	emit('update:endTime', newEndTime);
	emit('change', { startTime: startDate.value, endTime: newEndTime });

	// 如果选择的结束时间早于开始时间，清空开始时间
	if (val && startDate.value && dayjs(val).isBefore(dayjs(startDate.value))) {
		startDate.value = '';
		emit('update:startTime', '');
		emit('change', { startTime: '', endTime: newEndTime });
	}
};
</script>

<style lang="scss">
.to {
	color: #fff;
}
.data-screen-date-picker {
	position: relative;
	width: vw(253);
	display: flex;
	padding: vw(5) 0;
	height: vw(40);
	background: linear-gradient(
		to right,
		rgba(22, 226, 231, 0),
		rgba(33, 227, 221, 0.08) 20%,
		rgba(33, 227, 221, 0.15) 30%,
		rgba(14, 55, 57, 0.15) 70%,
		rgba(136, 232, 130, 0.08) 80%,
		rgba(136, 232, 130, 0) 100%
	);

	// 添加上下边框渐变效果
	&::before,
	&::after {
		content: '';
		position: absolute;
		left: 0;
		right: 0;
		height: 1px;
		background: linear-gradient(
			to right,
			rgba(14, 55, 57, 1),
			rgba(14, 55, 57, 1),
			rgba(14, 55, 57, 0.3),
			transparent,
			rgba(14, 55, 57, 0.3),
			rgba(14, 55, 57, 1),
			rgba(14, 55, 57, 1)
		);
	}

	&::before {
		top: 0;
	}

	&::after {
		bottom: 0;
	}

	// 添加左右边框
	box-shadow: 1px 0 0 0 rgba(14, 55, 57, 1), -1px 0 0 0 rgba(14, 55, 57, 1);
	// 日期选择器
	.el-date-editor {
		height: 100%;
		width: 100%;
		background: linear-gradient(
			to right,
			rgba(22, 226, 231, 0),
			rgba(33, 227, 221, 0.1) 20%,
			rgba(79, 229, 181, 0.3) 50%,
			rgba(118, 231, 146, 0.1) 80%,
			rgba(136, 232, 130, 0)
		);
		.el-range-separator {
			color: #fff !important;
		}
	}
	// 日期选择器
	.el-picker-panel {
		background: rgba(4, 31, 39, 1);
	}
	// 今天
	.el-date-table td.today .el-date-table-cell__text {
		color: $sd-screen-success;
	}
	// 日期选择器头部标签
	.el-date-picker__header-label {
		&:hover {
			color: $sd-screen-success;
		}
	}
	// 不可选
	.el-date-table td.disabled .el-date-table-cell {
		background-color: #1a3642;
		color: #a8a8a8;
	}

	.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
		color: #333;
		background-color: $sd-screen-success;
	}
	.el-date-table td.available:hover {
		color: $sd-screen-success;
	}
	// 此刻
	.el-button.is-text {
		color: rgba(255, 255, 255, 0.7);
	}
	.el-button.is-text:not(.is-disabled):focus,
	.el-button.is-text:not(.is-disabled):hover {
		background-color: $sd-screen-success;
		color: #fff;
		outline: none;
	}

	.el-button {
		color: rgba(255, 255, 255, 0.7);
		border-color: rgba(255, 255, 255, 0.7);
		background-color: transparent;
	}

	.el-button:focus,
	.el-button:hover {
		color: $sd-screen-success;
		border-color: transparent;
		box-shadow: 0 0 0 1px $sd-screen-success inset !important;
	}
	.el-button.is-disabled,
	.el-button.is-disabled:focus,
	.el-button.is-disabled:hover {
		color: rgba(255, 255, 255, 0.7);
		background-image: none;
		background-color: transparent;
		border-color: rgba(255, 255, 255, 0.7);
	}

	// 时间选择器
	.el-time-spinner__item:hover:not(.is-disabled):not(.is-active) {
		background: $sd-screen-success;
		color: #333;
	}

	.el-input {
		.el-input__inner {
			color: #fff;
			background: transparent;
			&::placeholder {
				color: rgba(255, 255, 255, 0.7);
			}
		}
		.el-input__icon {
			color: rgba(255, 255, 255, 0.7);
		}
	}
	// 日期选择器箭头
	.el-popper.is-light .el-popper__arrow {
		display: none;
	}
	// 日期选择器输入框
	.el-input__wrapper {
		border-radius: 0;
		box-shadow: none;
		background-color: transparent !important;
		box-shadow: 0 0 0 1px var(--el-datepicker-inner-border-color) inset !important;

		&:hover {
			box-shadow: 0 0 0 1px $sd-screen-success inset !important;
		}
	}
	.el-input__wrapper.is-focus {
		box-shadow: 0 0 0 1px $sd-screen-success inset !important;
	}

	// 日期选择器弹窗
	.el-popper {
		background: transparent !important;
		border-radius: 0;

		.el-date-picker__header,
		.el-date-picker__content,
		.el-date-range-picker__content {
			background-color: transparent;
		}
		.el-date-picker__header {
			.el-date-picker__header-label {
				color: #fff;
			}

			.el-picker-panel__icon-btn {
				color: #fff;
				&:hover {
					color: $sd-screen-success;
				}
			}
		}

		.el-date-table td {
			background-color: transparent;
		}

		.el-date-table th {
			background-color: transparent;
			color: #fff;
		}

		.el-picker-panel__content {
			background-color: transparent;
		}

		.el-date-range-picker__time-header {
			background-color: transparent;
		}
	}

	// 日期选择器下拉框
	.el-select-dropdown__item {
		background-color: transparent !important;
	}
}

// 日期选择器弹窗
.date-picker-popper {
	background: transparent !important;
	border: none !important;

	.el-picker-panel__sidebar,
	.el-date-range-picker__time-header,
	.el-picker-panel__footer {
		background-color: transparent !important;
		border: none !important;
	}

	.el-time-panel {
		background-color: rgba(4, 31, 39, 1);
		border: solid 1px rgba(33, 227, 221, 0.1);
		color: #fff;
	}
	.el-input__inner {
		color: #fff;
	}
	.is-active {
		&:not(.is-disabled) {
			color: #fff;
		}
	}

	.el-time-panel__btn.cancel {
		color: #fff;
	}
	.el-time-panel__btn.confirm {
		color: $sd-screen-success;
	}
}
</style>

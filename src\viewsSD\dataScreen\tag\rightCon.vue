<template>
	<div class="right-container">
		<!-- monitor-stat -->
		<div class="module monitor-stat">
			<div class="module-title">
				<span
					><img class="species" src="/src/assets/sd/data-screen/species.png" alt="" />物种占比</span
				>
			</div>
			<div class="module-cont monitor-stat-cont">
				<div v-for="item in monitorStat" :key="item.label">
					<div class="chart-title">{{ item.label }}</div>
					<div class="chart">
						<Echarts :id="item.chartId" ref="monitorRadioChartRef"></Echarts>
					</div>
				</div>
			</div>
		</div>
		<!-- monitor trend -->
		<div class="module monitor-trend">
			<div class="module-title">
				<span
					><img
						src="/src/assets/sd/data-screen/jiance.png"
						alt=""
						width="33"
						height="23"
					/>监测趋势</span
				>
			</div>
			<div class="module-cont monitor-trend-cont">
				<Echarts id="monitorTrendChart" ref="monitorTrendChartRef"></Echarts>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import {
	mergePieOptions,
	mergeLineOptions,
	ECOption,
	chartLinearColors,
	transDate,
} from '/@/components/echarts/mergeOptions';
import {
	getSpeciesFrequencyRatio,
	getSpeciesNumRatio,
	getSpeciesTrend,
} from '/@/api/sd/dataScreen/bird';

import Echarts from '/@/components/echarts/echarts.vue';
type Chart = InstanceType<typeof Echarts>;

const props = defineProps<{
	animalTag: number;
	animalNameList: string[];
	timeType: number | null;
}>();

// 物种占比
const monitorStat = ref([
	{
		label: '发现频次',
		key: 'speciesFrequency',
		api: getSpeciesFrequencyRatio,
		chartId: 'frequencyRadioChart',
	},
	{
		label: '发现数量',
		key: 'speciesNum',
		api: getSpeciesNumRatio,
		chartId: 'numRadioChart',
	},
]);
const monitorRadioChartRef = ref<Chart[]>();
const getMonitorRatio = () => {
	const query = {
		animalNameList: props.animalNameList,
		animalTag: props.animalTag,
		timeType: props.timeType,
	};
	monitorStat.value?.forEach((item, $index) => {
		item.api(query).then(({ payload }) => {
			// 返回的物种大于5条时，展示前五种 + 其他
			let chartData = payload.slice(0, 5).map((et: SpeciesEvent) => ({
				name: et.speciesAlias,
				value: et[item['key']],
			}));
			if (payload.length > 5) {
				const other = payload.slice(5).reduce(
					(prev: any, et: SpeciesEvent) => {
						prev.value = prev.value + et[item['key']];
						return prev;
					},
					{ name: '其他', value: 0 }
				);
				chartData.push(other);
			}
			const options = {
				legend: {
					formatter: (name: string) => {
						const index = chartData.findIndex(
							(item: { name: string; value: number }) => item.name === name
						);
						return `{icon${index}|}{cname|${name}}{cnum|${chartData[index]['value']}}\n{line|}{square|}`;
					},
				},
				dataset: {
					source: chartData,
				},
			};
			(monitorRadioChartRef.value as Chart[])[$index].resetOption(mergePieOptions(options));
		});
	});
};

// 监测趋势
const monitorTrendChartRef = ref<Chart>();
const getMonitorTrend = async () => {
	const query = {
		animalNameList: props.animalNameList,
		animalTag: props.animalTag,
		timeType: props.timeType,
	};
	const { payload } = await getSpeciesTrend(query);
	const chartData = payload.map((et: SpeciesEvent) => {
		const date = transDate(props.timeType, et.date);
		return [date, et.speciesNum, et.speciesFrequency];
	});
	const options: ECOption = {
		dataset: {
			source: chartData,
		},
		color: chartLinearColors.slice(0, 2),
		series: [
			{
				type: 'line',
				name: '发现物种频次',
				symbol: 'circle',
				symbolSize: 6,
				showSymbol: false,
				areaStyle: {
					color: chartLinearColors[3],
				},
			},
			{
				type: 'line',
				name: '发现物种数量',
				symbol: 'circle',
				symbolSize: 6,
				showSymbol: false,
				areaStyle: {
					color: chartLinearColors[2],
				},
			},
		],
	};
	monitorTrendChartRef.value?.resetOption(mergeLineOptions(options));
};

watch(
	[() => props.animalNameList, () => props.timeType],
	() => {
		getMonitorRatio();
		getMonitorTrend();
	},
	{ immediate: true }
);
</script>

<style lang="scss" scoped>
.monitor-stat .monitor-stat-cont {
	.chart-title {
		padding: vw(10) 0 vw(6) vw(48);
		font-family: 'ALiMaMaShuHeiTi';
		font-size: vw(16);
	}
	.chart {
		width: 100%;
		height: vh(220);
	}
}
.monitor-trend .monitor-trend-cont {
	width: 100%;
	height: vh(300);
}
</style>

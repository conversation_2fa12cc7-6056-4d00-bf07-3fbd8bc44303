
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import { Species_Key } from '/@/utils/constants';
import type { SpeciesKey } from '/@/views/species/type';

export function useSpeciesType () {
  const route = useRoute();
  const currentPath = ref(route.path);

  // <number>type：0植物 1鸟 2昆虫 动物（3哺乳动物 4鱼类 5两栖类 6爬行类）
  const speciesType = computed(() => {
    let type: number = 0;
    const deep = (arr: SpeciesKey[]) => {
      arr.forEach((item) => {
        if (currentPath.value.includes(item.key)) {
          if (currentPath.value.endsWith(item.key) || currentPath.value.endsWith(`/detail`)) {
            type = item.value;
          }
        }
      })
    }
    deep(Species_Key);
    return type;
  });
  
  return {
    currentPath,
    speciesType,
  };
}

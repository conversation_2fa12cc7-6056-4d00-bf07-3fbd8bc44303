import { ref, onBeforeMount, onBeforeUnmount } from 'vue';
import mittBus from '/@/utils/mitt';
import { getAiModels } from '/@/api/aiModels';
import { Session } from '/@/utils/storage';

interface ModelType {
  name: string;
  type: number;
}

export function useAiModels() {
  const aiModels = ref<EmptyArrayType>([]);

  onBeforeMount(() => {
    initAllModels();
    mittBus.on('aiModelsChange', () => {
      aiModels.value = Session.get('all_aiModels');
    });
  });

  onBeforeUnmount(() => {
    mittBus.off('aiModelsChange');
  });

  const initAllModels = () => {
    if (Session.get('all_aiModels')) {
      const list = Session.get('all_aiModels');
      aiModels.value = list;
    } else {
      getAiModels({ page: 0, size: 1000, sort: 'type,asc' }).then(({ payload }) => {
        aiModels.value = payload.content;
        Session.set('all_aiModels', payload.content);
      })
    }
  };

  // 模型增删改时
  const onAiModelChange = () => {
    Session.remove('all_aiModels');
    initAllModels();
    // 通知其他使用到模型列表的页面
    setTimeout(() => {
      mittBus.emit('aiModelsChange');
    }, 2000)
  };

  return {
    data: aiModels,
    onAiModelChange,
  };
}

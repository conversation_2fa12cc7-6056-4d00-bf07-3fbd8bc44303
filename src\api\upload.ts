import request from '/@/utils/request';

/**
 * @method uploadFile 上传文件
 * @method readerFile 读取本地文件
 */

export function uploadFile(data: FormData, repo: string = 'irc') {
	return request({
		url: `/files/u/${repo}`,
		method: 'post',
		data,
		headers: {
			'content-type': 'multipart/form-data',
		},
		timeout: 5 * 60 * 1000,
	});
}

export function readerFile(file: File) {
	return new Promise((resolve) => {
		const reader = new FileReader();
		reader.onload = (event) => {
			resolve(event.target?.result);
		};
		reader.readAsDataURL(file);
	})
}
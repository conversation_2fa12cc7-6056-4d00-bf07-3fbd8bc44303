<template>
	<div class="login-container flex">
		<div class="login-main">
			<img
				src="/src/assets/login-title.png"
				alt=""
				width="580"
				height="63"
				style="display: block; object-fit: contain; margin: 45px auto 47px"
			/>
			<div class="login-center">
				<Account />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts" name="loginIndex">
import { defineAsyncComponent, onMounted } from 'vue';
import { NextLoading } from '/@/utils/loading';

// 引入组件
const Account = defineAsyncComponent(() => import('/@/views/login/component/account.vue'));

// 页面加载时
onMounted(() => {
	NextLoading.done();
});
</script>

<style scoped lang="scss">
.login-container {
	height: 100%;
	background-image: url('/src/assets/loginBg.jpg');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	.login-main {
		width: 640px;
		height: 560px;
		margin: 18.52vh auto 0;
		background-color: rgba(0, 0, 0, 0.5);
		.login-center {
			margin: 0 auto;
			width: 65%;
			height: 100%;
		}
	}
}
</style>

<template>
	<div class="home-container layout-pd">
		<el-row :gutter="15" class="home-card-one mb15">
			<el-col
				:xs="24"
				:sm="12"
				:md="12"
				:lg="8"
				:xl="8"
				v-for="(v, k) in state.stat"
				:key="k"
				:class="{ 'home-media home-media-lg': k > 1, 'home-media-sm': k === 1 }"
			>
				<div class="home-card-item flex">
					<div class="flex-margin flex w100" :class="`home-one-animation${k}`">
						<div class="flex-auto">
							<span class="font32">{{ v.count }}</span>
							<!-- <span class="ml5 font16" :style="{ color: v.color1 }">{{ v.num2 }}%</span> -->
							<div class="mt10">{{ v.label }}</div>
						</div>
						<div class="home-card-item-icon flex" :style="{ background: `var(${v.color2})` }">
							<i
								class="flex-margin font32"
								:class="v.icon"
								:style="{ color: `var(${v.color3})` }"
							></i>
						</div>
					</div>
				</div>
			</el-col>
		</el-row>

    <div class="home-card-two">
      <div class="home-card-item">
        <div class="home-card-item-title">物种资源库</div>
        <el-row class="home-card-item-main">
          <el-col class="main-item" :xs="24" :sm="12" :md="6" :lg="6" :xl="6" v-for="(v, k) in state.specieStat" :key="k">
            <div class="main-item-box" :class="`home-animation${k}`">
              <div class="total">
                <i class="font32" :class="v.icon" :style="{ color: v.iconColor }"></i>
                <div>
                  <div class="font30">{{ v.total }}</div>
                  <div>{{ v.label }}</div>
                </div>
              </div>
              <div class="classCount" v-if="k !== state.specieStat.length - 1">
                <div>
                  <div class="font24">{{ v.firstGrade }}</div>
                  <div>一级</div>
                </div>
                <div>
                  <div class="font24">{{ v.secondGrade }}</div>
                  <div>二级</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

	</div>
</template>

<script setup lang="ts" name="home">
import { reactive, onMounted } from 'vue';
import { deviceCountStat, monitorCountStat, speciesStat, getProtectionLevel } from '/@/api/home';

// 定义变量内容
const state = reactive({
	stat: [
		{
			count: 0,
			label: '监测设备数',
			icon: 'iconfont icon-shexiangtou-copy',
			color1: '#FF6462',
			color2: '--next-color-primary-lighter',
			color3: '--el-color-primary',
		},
		{
      count: 0,
			label: '监测任务数',
			icon: 'iconfont icon-renwuguanli',
			color1: '#6690F9',
			color2: '--next-color-success-lighter',
			color3: '--el-color-success',
		},
		// {
		// 	num1: '4,324',
		// 	num2: '+17.32',
		// 	num3: '识别图片数',
		// 	num4: 'iconfont icon-tupian-copy',
		// 	color1: '#6690F9',
		// 	color2: '--next-color-warning-lighter',
		// 	color3: '--el-color-warning',
		// },
		// {
		// 	num1: '500',
		// 	num2: '-10.01',
		// 	num3: '上报图片数',
		// 	num4: 'iconfont icon-tupian-copy',
		// 	color1: '#FF6462',
		// 	color2: '--next-color-danger-lighter',
		// 	color3: '--el-color-danger',
		// },
	],
	specieStat: [
		{
			label: '植物',
			total: 0,
			firstGrade: 0,
			secondGrade: 0,
			otherGrade: 0,
			icon: 'iconfont icon-plant-copy',
			iconColor: '#88D565',
		},
		{
			icon: 'iconfont icon-animal',
			label: '动物',
			total: 0,
			firstGrade: 0,
			secondGrade: 0,
			otherGrade: 0,
			iconColor: '#91BFF8',
		},
		{
			label: '鸟类',
			total: 0,
			firstGrade: 0,
			secondGrade: 0,
			otherGrade: 0,
			icon: 'iconfont icon-bird',
			iconColor: '#F72B3F',
		},
		{
			label: '昆虫',
			total: 0,
			firstGrade: 0,
			secondGrade: 0,
			otherGrade: 0,
			icon: 'iconfont icon-insect',
			iconColor: '#FBD4A0',
		},
	],
});

const getDeviceCount = async () => {
  const { payload } = await deviceCountStat();
  state.stat[0].count = payload;
};

const getMonitorCount = async () => {
  const { payload } = await monitorCountStat();
  state.stat[1].count = payload;
};

// 物种资源库统计
const getSpecieStat = async () => {
	const { payload } = await speciesStat();
	state.specieStat[0].total = payload.allPlantCount;
	state.specieStat[1].total = payload.allAnimalCount;
	state.specieStat[2].total = payload.allBirdCount;
	state.specieStat[3].total = payload.allInsectCount;
	// 保护等级统计
	for (var i = 0; i < 3; i++) {
		const { payload } = await getProtectionLevel(i);
		state.specieStat[i] = Object.assign(state.specieStat[i], payload);
	}
};

// 页面加载时
onMounted(() => {
  getDeviceCount();
  getMonitorCount();
	getSpecieStat();
});
</script>

<style scoped lang="scss">
$homeNavLengh: 8;

.home-container {
	overflow: hidden;
	.home-card-one .home-card-item,
	.home-card-two .home-card-item {
		width: 100%;
		height: 140px;
		border-radius: 4px;
		padding: 20px 30px;
		overflow: hidden;
		background: var(--el-color-white);
		color: var(--el-text-color-primary);
		border: 1px solid var(--next-border-color-light);
		transition: all ease 0.3s;
		&:hover {
			box-shadow: 0 2px 12px var(--next-color-dark-hover);
			transition: all ease 0.3s;
		}
		&-icon {
			width: 70px;
			height: 70px;
			border-radius: 100%;
			flex-shrink: 1;
			i {
				color: var(--el-text-color-placeholder);
			}
		}
		&-title {
			font-size: 15px;
			font-weight: bold;
			height: 30px;
		}
	}
	.home-card-one {
		@for $i from 0 through 3 {
			.home-one-animation#{$i} {
				opacity: 0;
				animation-name: error-num;
				animation-duration: 0.5s;
				animation-fill-mode: forwards;
				animation-delay: calc($i/4) + s;
			}
		}
	}

	.home-card-two .home-card-item {
		width: 100%;
    height: max-content;
		&-main {
			.main-item {
        padding: 10px;
				.main-item-box {
          height: 200px;
					color: var(--el-text-color-primary);
					padding: 28px 10px 0;
					border-radius: 5px;
					background: var(--next-bg-color);
					cursor: pointer;
				}
				@for $i from 0 through $homeNavLengh {
					.home-animation#{$i} {
						opacity: 0;
						animation-name: error-num;
						animation-duration: 0.5s;
						animation-fill-mode: forwards;
						animation-delay: calc($i/10) + s;
					}
				}
				.main-item-box > .total {
					display: flex;
					justify-content: center;
					align-items: center;
					text-align: center;
					.iconfont {
						width: 60px;
						height: 60px;
						border-radius: 10px;
						margin-right: 20px;
						line-height: 60px;
						background-color: rgba(255, 255, 255, 0.8);
					}
				}
				.main-item-box > .classCount {
					display: flex;
					align-items: center;
					text-align: center;
					margin-top: 30px;
					& > div {
						width: 50%;
					}
					& > div:first-child {
						border-right: 1px solid var(--next-border-color-light);
					}
				}
			}
		}
	}
}
</style>

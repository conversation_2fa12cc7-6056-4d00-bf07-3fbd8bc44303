import request from '/@/utils/request';

/**
 * @method getSpeciesFrequency 获取设备点位物种频次
 */

//
export function getSpeciesFrequency(params?: {
  timeType?: TimeType,
  deviceList?: string[],
}) {
  return request({
    url: '/dashboard/devicePoint/species/frequency',
    method: 'get',
    params
  });
}


export function getDeviceRank(params: any) {
  return request({
    url: '/dashboard/device/rank/page',
    method: 'get',
    params
  });
}

export function getSpeciesRank(params: any) {
  return request({
    url: '/dashboard/species/rank/page',
    method: 'get',
    params
  });
}

export function getSpecies(params: { name?: string, eventType?: number, eventTypes?: [] }) {
  return request({
    url: `/monitor-events/directory/species`,
    method: 'get',
    params
  });
}

export function getExistSpecies(params?: any) {
  return request({
    url: `/monitor-events/exist/directory/species`,
    method: 'get',
    params
  });
}
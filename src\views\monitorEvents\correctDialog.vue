<template>
	<!-- <el-dialog
		width="30%"
		top="5vh"
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		class="correct-dialog"
	>
		<el-switch
			v-model="state.abnormal"
			class="ml-2"
			inline-prompt
			style="--el-switch-on-color: #ff4949; --el-switch-off-color: #13ce66"
			active-text="有病虫害"
			inactive-text="无病虫害"
		/>
	</el-dialog> -->

	<el-popconfirm
		width="220"
		confirm-button-text="OK"
		cancel-button-text="No, Thanks"
		icon-color="#626AEF"
		title="Are you sure to delete this?"
	>
		<template #reference>
			<el-button>Delete</el-button>
		</template>
	</el-popconfirm>
</template>

<script setup lang="ts" name="MonitorEventEdit">
import { ref, reactive, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { updateMonitorEvent, MonitorEventDetail } from '/@/api/monitorEvents';

const state = reactive({
	dialog: {
		isShowDialog: false,
		title: '订正识别结果',
	},
	abnormal: false,
});

const openDialog = (abnormal: boolean) => {
	state.dialog.isShowDialog = true;
	state.abnormal = abnormal;
};

defineExpose({
	openDialog,
});
</script>

<style lang="scss" scoped></style>

import request from '/@/utils/request';

/**
 * @method getMonitorEvents 获取所有上报事件
 * @method getMonitorEventMenuTypes 获取识别结果一览上报事件组（子菜单）
 * @method getMonitorEventTypes 获取上报事件识别类型
 * @method updateMonitorEvent 修改上报事件
 * @method deleteMonitorEvent 删除上报事件
 * @method batchDeleteMonitorEvent 批量删除上报事件
 * @method exportMonitorFiles 导出
 */

export function getMonitorEvents(params: object) {
  return request({
    url: '/monitor-event-files',
    method: 'get',
    params,
  });
}

export function getMonitorEventMenuTypes() {
  return request({
    url: '/monitor-events/group_event_types',
    method: 'get',
  });
}

export function getMonitorEventTypes(params?: object) {
  return request({
    url: '/monitor-events/event_types',
    method: 'get',
    params,
  });
}


export interface MonitorEventDetail {
  recResult: string;
  recResultCnt: number;
  eventType?: number;
}
interface UpdateData {
  id: string;
  oriPictureId: string;
  rawResult: string;
  monitorEventDetails: MonitorEventDetail[];
  eventType: number;
}
export function updateMonitorEvent(data: UpdateData) {
  return request({
    url: `/monitor-event-files/${data.id}`,
    method: 'put',
    params: {
      eventType: data.eventType,
    },
    data,
  });
}

export function deleteMonitorEvent(id: string) {
  return request({
    url: `/monitor-event-files/${id}`,
    method: 'delete',
  });
}

export function batchDeleteMonitorEvent(ids: string[]) {
  return request({
    url: '/monitor-event-files/del',
    method: 'delete',
    data: ids,
  });
}

export function getMonitorFilesEvents(params: object) {
  return request({
    url: '/monitor-event-files/events',
    method: 'get',
    params,
  });
}

/**
 * 订正事件结果
 * @param 
 * @returns 
 */
export function revisedEvent({
  id,
  eventType,
  abnormal,
  result // 默认为null，如果不需要默认值可以去掉
}: {
  id: string;
  eventType: number;
  abnormal: boolean;
  result?: string;
}) {
  return request({
    url: `/monitor-event-files/${id}`,
    method: 'put',
    params: {
      eventType: eventType,
    },
    data: {
      id: id,
      abnormal: abnormal,
      result: result
    }
  });
}

export function exportMonitorFiles(params: object) {
  return request({
    url: `/monitor-event-files/export`,
    method: 'get',
    responseType: 'arraybuffer',
    params,
  })
}

/**
 * 获取物种类型
 * @param params 
 * @returns 
 */
export function getUnknownSpecies(params: object) {
  return request({
    url: '/monitor-events/directory/unknown/species',
    method: 'get',
    params,
  });
}


/**
 * 获取物种类型
 * @param params 物种事件类型 speciesEventType 0.有动物 1.有鸟类 21.有水生 24.有人 100.有明确识别，不包含人 101.无明确识别，不包含人 102.有物种，不包含人 103.无物种" 
 * @returns 
 */
export function getEventTypes(params: object) {
  return request({
    url: '/monitor-event-files/event',
    method: 'get',
    params,
  });
}



// export function getUnknownSpecies(params: object) {
//   return request({
//     url: '/directory/unknown/species',
//     method: 'get',
//     params,
//   });
// }
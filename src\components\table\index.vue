<template>
	<div class="table-container flex-auto">
		<el-table
			:data="data"
			v-bind="$attrs"
			ref="elTableRef"
			row-key="id"
			style="width: 100%"
			v-loading="config.loading"
			:row-class-name="tableRowClassName"
			:treeProps="config.treeProps"
			@selection-change="onSelectionChange"
		>
			<el-table-column v-if="config.treeProps?.children" type="expand">
				<template v-slot="scope">
					<slot name="treeProps" v-bind="scope"></slot>
				</template>
			</el-table-column>
			<el-table-column
				type="selection"
				:reserve-selection="true"
				width="50"
				align="center"
				v-if="config.isSelection"
			/>
			<el-table-column
				v-if="config.isSerialNo"
				type="index"
				label="序号"
				width="60"
				align="center"
				show-overflow-tooltip
			/>
			<el-table-column
				v-for="(item, index) in setHeader"
				:key="index"
				:prop="item.key"
				:width="item.colWidth"
				:label="item.title"
				:fixed="item.fixed"
				:showOverflowTooltip="!item.noTooltip"
				align="center"
			>
				<template v-slot="scope">
					<slot :name="item.key" v-bind="scope">
						{{
							item.isDate
								? formatDate(new Date(item.key), item.format as string)
								: scope.row[item.key] || '-'
						}}
					</slot>
				</template>
			</el-table-column>

			<el-table-column
				v-if="config.isOperate"
				label="操作"
				:width="config.operateWidth"
				:fixed="config.fixedOperate"
				align="center"
				class-name="option-column"
			>
				<template v-slot="scope">
					<slot name="operate" v-bind="scope"></slot>
				</template>
			</el-table-column>
		</el-table>

		<div class="table-footer" v-if="isPagination">
			<el-pagination
				:current-page="state.page.pageNum"
				:page-size="state.page.pageSize"
				:page-sizes="[10, 20, 30, 50]"
				:total="config.total"
				background
				layout="total, sizes, prev, pager, next, jumper"
				@size-change="onHandleSizeChange"
				@current-change="onHandleCurrentChange"
			>
			</el-pagination>
		</div>
	</div>
</template>

<script setup lang="ts" name="Table">
import { ref, reactive, computed } from 'vue';
import { formatDate } from '/@/utils/formatTime';
// 定义tableRowClassName函数的类型
type TableRowClassNameFunction = (params: { row: any; rowIndex: number }) => string | undefined;

const props = withDefaults(
	defineProps<{
		data: Array<EmptyObjectType>;
		header: TableHeader[];
		config: TableConfig;
		isPagination?: boolean;
		tableRowClassName?: TableRowClassNameFunction;
		pageParams?: {
			page: number;
			size: number;
		};
	}>(),
	{
		data: () => [],
		header: () => [],
		config: () => ({
			loading: false,
			isSelection: false,
			isSerialNo: false,
			isOperate: true,
			operateWidth: 100,
			fixedOperate: false,
			total: 0,
		}),
		tableRowClassName: () => '',
		isPagination: true,
		pageParams: () => ({
			page: 1,
			size: 10,
		}),
	}
);

const emit = defineEmits(['pageChange', 'selectionChange']);

const state = reactive({
	page: {
		pageNum: 1,
		pageSize: 10,
	},
});
const elTableRef = ref();
const selectRows = ref<EmptyObjectType[]>([]);

// header
const setHeader = computed(() => {
	return props.header.filter((v) => v.isCheck);
});
// 表格多选改变时
const onSelectionChange = (val: EmptyObjectType[]) => {
	selectRows.value = val;
	emit('selectionChange', val);
};
// 清空多选
const clearSelection = () => {
	elTableRef.value.clearSelection();
};
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.page.pageSize = val;
	emit('pageChange', state.page);
};
const onHandleCurrentChange = (val: number) => {
	state.page.pageNum = val;
	emit('pageChange', state.page);
};
// 搜索时，分页还原成默认
const pageReset = () => {
	state.page.pageNum = 1;
	emit('pageChange', state.page);
};

defineExpose({
	pageReset,
	selectRows,
	clearSelection,
});
</script>

<style scoped lang="scss">
.table-container {
	display: flex;
	flex-direction: column;
	.el-table {
		flex: 1;
	}
	:deep(.el-table__empty-text .el-empty .el-empty__description) {
		margin-top: 0;
	}
	:deep(.option-column) {
		.el-button {
			padding: 0;
		}
	}
	.table-footer {
		display: flex;
		justify-content: flex-end;
	}
}
</style>

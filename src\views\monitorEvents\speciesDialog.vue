<template>
  <!-- 根据识别结果查询到的多条百科数据 -->
  <el-dialog v-model="dialogVisible" title="关联物种资源" width="1000">
    <Table v-bind="dialogTableOptions" ref="tableRef">
      <template #imageUrl="{ row }">
        <el-image style="width: 120px; height: 90px;" :src="row.imageUrl" fit="cover"></el-image>
      </template>
      <template #protectLevel="{ row }">
        {{ row.protectLevel || '普通' }}
      </template>
      <template #operate="{row}">
        <el-button size="small" text type="primary" @click="toSpeciesDetail(row)">查看详情</el-button>
      </template>
    </Table>
  </el-dialog>
</template>

<script setup lang="ts" name="">
import { ref, reactive, defineAsyncComponent } from 'vue';
import { useRouter } from 'vue-router';
import { getDirectoriesByRecResult } from '/@/api/species';
import { Session } from '/@/utils/storage';

const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));

const dialogVisible = ref(false);
const dialogTableOptions: GlobalTableOptions<any> = reactive({
  data: [],
  header: [
    {
      title: '封面图',
      key: 'imageUrl',
      isCheck: true,
			noTooltip: true,
			colWidth: 120,
    },
    {
      title: '名称',
      key: 'name',
      isCheck: true,
    },
    {
      title: '拉丁名',
      key: 'latinName',
      isCheck: true,
    },
    {
      title: '纲',
      key: 'className',
      isCheck: true,
    },
    {
      title: '目',
      key: 'order',
      isCheck: true,
    },
		{
      title: '科',
      key: 'family',
      isCheck: true,
    },
		{
      title: '属',
      key: 'genus',
      isCheck: true,
    },
		{
      title: '种',
      key: 'specie',
      isCheck: true,
    },
		{
      title: '保护等级',
      key: 'protectLevel',
      isCheck: true,
    },
  ],
  config: {
    loading: false,
    isSelection: false,
    isSerialNo: true,
    isOperate: true,
    operateWidth: 100,
    total: 0, // 总条数
  },
	isPagination: false,
});
const router = useRouter();

/**
 * 识别结果链接百科信息，查看物种信息
 * 		①若查到多条，展示查询到的数据，让用户去选择要查看的物种。
 * 		②若查询结果为单条，直接跳转物种详情页面。
 */
const getAssSpecies = async (name: string) => {
	const data = { name };
	const { payload } = await getDirectoriesByRecResult(data);
	let temp: any[] = [];
	if (!payload) return;
	for(let key in payload) {
		if (!payload[key]) continue;
		payload[key] = payload[key].map((item: any) => ({
			...item,
			speciesType: key,
		}))
		temp = temp.concat(payload[key]);
	}
	if (temp.length === 1) {
		toSpeciesDetail(temp[0]);
	} else {
		dialogTableOptions.data = temp;
    dialogVisible.value = true;
	}
};
const toSpeciesDetail = ({ id, speciesType } : { id: number, speciesType: string}) => {
	Session.set('speciesDetailQuery', JSON.stringify({ id }));
	router.push({
		path: `/species/${speciesType}/detail`,
	})
};
defineExpose({
  getAssSpecies,
})
</script>
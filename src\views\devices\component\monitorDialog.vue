<template>
  <el-dialog
    :title="state.dialog.title"
    v-model="state.dialog.isShowDialog"
    width="1200px"
    align-center
  >
    <Table v-bind="tableOptions" ref="tableRef" @pageChange="onPageChange">
      <template #deviceId="{ row }">
        {{ (row.deviceModel && row.deviceModel.num + '-' + (row.deviceModel.channelName || row.deviceModel.name)) || '-' }}
      </template>

      <template #aiModelId="{ row }">
        {{ (row.aiModel && row.aiModel.name) || '-' }}
      </template>

      <template #monitorRunTime="{ row }">
        {{
          row.monitorRunTime ?
          row.monitorRunStatus === 0 ?
            formatDuration(row.monitorRunTime, row.monitorStopTime)
            : formatDuration(row.monitorRunTime)
          : '-'
        }}
      </template>

      <template #monitorStopTime="{ row }">
        {{ (row.monitorRunStatus === 0 && row.monitorStopTime) ? row.monitorStopTime : '-' }}
      </template>

      <template #monitorStatus="{ row }">
        <div v-if="row.monitorRunStatus === 1" class="custom-badge"
          :class="row.monitorStatus === 0 ? 'success' : 'danger'">
          {{ row.monitorStatus === 0 ? '运行中' : row.monitorStatus === 1 ? '模型异常' : '设备异常' }}
        </div>
        <div v-else class="custom-badge info">未启动</div>
      </template>

      <template #operate="{ row, $index }">
        <el-button size="small" text type="primary" @click="onUpdateRow(row)">
          <el-icon><ele-EditPen /></el-icon>
          修改
        </el-button>
        <el-button size="small" text type="primary" @click="onDelRow(row.id, $index)">
          <el-icon><ele-Delete /></el-icon>
          删除
        </el-button>
      </template>
    </Table>

    <template #footer>
			<span class="dialog-footer">
				<el-button type="primary" @click="onCreate">
          <template #icon>
            <el-icon><ele-Plus /></el-icon>
          </template>
          新增监测任务
        </el-button>
			</span>
		</template>

  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, defineAsyncComponent } from 'vue';
import { getMonitors, deleteMonitor } from '/@/api/monitors';
import { ElMessage, ElMessageBox } from 'element-plus';
import { formatDuration } from '/@/utils/formatTime';

const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));
const emits = defineEmits<{
  (e: 'onCreateMonitor', deviceId: string): void,
  (e: 'onUpdateMonitor', deviceId: string, data: MonitorRow): void,
  (e: 'refresh'): void,
}>();

const state = reactive({
  dialog: {
    isShowDialog: false,
    title: '',
  },
  deviceId: '',
});
const tableOptions: GlobalTableOptions<MonitorRow> = reactive({
  data: [],
  header: [
    {
      title: '监测任务名称',
      key: 'name',
      isCheck: true,
    },
    {
      title: '关联设备',
      key: 'deviceId',
      isCheck: true,
    },
    {
      title: '关联模型',
      key: 'aiModelId',
      isCheck: true,
    },
    {
      title: '监测运行时长',
      key: 'monitorRunTime',
      isCheck: true,
      colWidth: 160,
    },
    {
      title: '最近停止时间',
      key: 'monitorStopTime',
      isCheck: true,
      colWidth: 160,
      // isDate: true,
      // format: 'YYYY-mm-dd HH:MM:SS',
    },
    {
      title: '状态',
      key: 'monitorStatus',
      isCheck: true,
      colWidth: 100,
    },
  ],
  config: {
    loading: true,
    isSelection: false,
    isSerialNo: true,
    isOperate: true, // 是否显示操作列
    operateWidth: 150, // 操作列宽
    total: 0, // 总条数
  },
  pageParams: {
    page: 1,
    size: 10,
  },
});

const openDialog = (deviceId: string, deviceName: string) => {
  state.dialog.isShowDialog = true;
  state.dialog.title = deviceName + ' - 监测任务'
  state.deviceId = deviceId;
  initTableData();
};
const closeDialog = () => {
  state.dialog.isShowDialog = false;
};
const initTableData = async () => {
  tableOptions.config.loading = true;
  try {
    const query: any = {
      page: tableOptions.pageParams?.page && tableOptions.pageParams.page - 1,
      size: tableOptions.pageParams?.size,
      deviceId: state.deviceId,
    };
    const { payload } = await getMonitors(query)
    tableOptions.config.loading = false;
    tableOptions.data = payload.content;
    tableOptions.config.total = payload.totalElements;
  } catch (e) {
    tableOptions.config.loading = false;
    tableOptions.config.total = 0;
    tableOptions.data = [];
  }
};
const onPageChange = async (page: { pageNum: number; pageSize: number; }) => {
  if (tableOptions.pageParams) {
    tableOptions.pageParams.page = page.pageNum;
    tableOptions.pageParams.size = page.pageSize;
  }
  initTableData();
};
const onCreate = () => {
  closeDialog();
  emits('onCreateMonitor', state.deviceId);
};
const onUpdateRow = (data: MonitorRow) => {
  closeDialog();
  emits('onUpdateMonitor', state.deviceId, data);
};
const onDelRow = (rowId: string, $index: number) => {
  ElMessageBox({
    title: '提示',
    message: '此操作将永久删除，是否继续?',
    type: 'warning',
    showCancelButton: true,
  }).then(async () => {
    await deleteMonitor(rowId);
    ElMessage.success('删除成功');
    tableOptions.data.splice($index, 1);
    tableOptions.config.total -= 1;
    emits('refresh');
  }).catch(() => { })
};

defineExpose({
  openDialog,
});
</script>

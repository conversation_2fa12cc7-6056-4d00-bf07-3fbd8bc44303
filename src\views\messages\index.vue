<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<el-form-item label="消息类型:">
					<el-select
						v-model="searchOptions.filter.targetType"
						placeholder="请选择消息类型"
						clearable
					>
						<el-option
							v-for="item in targetTypes.data"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="时间范围:">
					<el-date-picker
						v-model="dateRange"
						type="datetimerange"
						range-separator="至"
						start-placeholder="开始日期时间"
						end-placeholder="结束日期时间"
						value-format="YYYY-MM-DD HH:mm:ss"
						@change="handleDateRangeChange"
					/>
				</el-form-item>
			</template>
		</ViewSearch>

		<el-button
			type="danger"
			style="margin-bottom: 15px"
			:disabled="tableRef?.selectRows.length === 0"
			@click="onBatchRead"
		>
			<template #icon>
				<i class="iconfont icon-yiduxinxi"></i>
			</template>
			已读
		</el-button>

		<Table
			v-bind="tableOptions"
			:tableRowClassName="tableRowClassName"
			ref="tableRef"
			@pageChange="onPageChange"
		>
			<template #targetType="{ row }">
				{{ targetTypes.getLabel(row.targetType) }}
			</template>

			<template #operate="{ row }">
				<el-button size="small" text type="primary" @click="onDetail(row.id)">
					<el-icon><ele-Pointer /></el-icon>
					详情
				</el-button>
			</template>
		</Table>
		<DetailDialog ref="detailDialogRef" @refresh="onRefresh"></DetailDialog>
	</div>
</template>

<script setup lang="ts" name="Messages">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getMessages, getMessagesDetail, batchRead } from '/@/api/message';
import { targetTypesFun } from './hooks/targetTypes';
const targetTypes = targetTypesFun();
// 引入异步组件
const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));
const DetailDialog = defineAsyncComponent(() => import('./detailDialog.vue'));

const tableRef = ref<InstanceType<typeof Table>>();
const searchOptions = reactive({
	filter: {
		targetId: null,
		targetType: null,
		text: '',
		sort: 'createTime,desc',
		startDateTime: '',
		endDateTime: '',
	},
	monitorStatusOptions: [
		{ label: '未启动', value: 3 },
		{ label: '运行中', value: 0 },
		{ label: '模型异常', value: 1 },
		{ label: '设备异常', value: 2 },
	] as SelectOptions[],
});

const tableRowClassName = ({ row }: { row: any }) => {
	if (row.messageUserModel.viewed === 1) {
		return 'read-row';
	}
};
const tableOptions: GlobalTableOptions<any> = reactive({
	data: [],
	header: [
		{
			title: '消息内容',
			key: 'text',
			isCheck: true,
		},
		// {
		// 	title: '设备',
		// 	key: 'targetId',
		// 	isCheck: true,
		// 	colWidth: 160,
		// },
		{
			title: '消息类型',
			key: 'targetType',
			isCheck: true,
			colWidth: 160,
		},
		{
			title: '创建时间',
			key: 'createTime',
			isCheck: true,
			colWidth: 160,
			// isDate: true,
			// format: 'YYYY-mm-dd HH:MM:SS',
		},
	],
	config: {
		loading: true,
		isSelection: true,
		isSerialNo: true,
		isOperate: true, // 是否显示操作列
		operateWidth: 150, // 操作列宽
		total: 0, // 总条数
	},
	pageParams: {
		page: 1,
		size: 10,
	},
});

const dateRange = ref<string[]>([]);

const handleDateRangeChange = (val: string[]) => {
	if (val) {
		searchOptions.filter.startDateTime = val[0];
		searchOptions.filter.endDateTime = val[1];
	} else {
		searchOptions.filter.startDateTime = '';
		searchOptions.filter.endDateTime = '';
	}
};

onMounted(() => {
	initTableData();
});

const initTableData = async () => {
	tableOptions.config.loading = true;
	try {
		const query: any = {
			page: tableOptions.pageParams?.page && tableOptions.pageParams.page - 1,
			size: tableOptions.pageParams?.size,
			...searchOptions.filter,
		};

		const { payload } = await getMessages(query);
		tableOptions.config.loading = false;
		tableOptions.data = payload.content;
		tableOptions.config.total = payload.totalElements;
	} catch (e) {
		tableOptions.config.loading = false;
		tableOptions.config.total = 0;
		tableOptions.data = [];
	}
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetFilter) {
		searchOptions.filter.targetId = null;
		searchOptions.filter.targetType = null;
		searchOptions.filter.text = '';
		searchOptions.filter.startDateTime = '';
		searchOptions.filter.endDateTime = '';
		dateRange.value = [];
	}
	if (resetPage) {
		tableRef.value?.pageReset();
	} else {
		initTableData();
	}
	tableRef.value?.clearSelection();
};

const onPageChange = async (page: { pageNum: number; pageSize: number }) => {
	if (tableOptions.pageParams) {
		tableOptions.pageParams.page = page.pageNum;
		tableOptions.pageParams.size = page.pageSize;
	}
	initTableData();
};

const onBatchRead = () => {
	ElMessageBox({
		title: '提示',
		message: '此操作将批量已读，是否继续?',
		type: 'warning',
		showCancelButton: true,
	})
		.then(async () => {
			const ids = tableRef?.value?.selectRows.map((item) => item.id) as string[];
			await batchRead(ids);
			ElMessage.success('已读成功');
			onRefresh();
		})
		.catch(() => {});
};
const detailDialogRef = ref<InstanceType<typeof DetailDialog>>();
const onDetail = (rowId: string) => {
	getMessagesDetail(rowId).then((res) => {
		detailDialogRef.value?.openDialog(res.payload);
	});
};
</script>

<style lang="scss" scoped>
:deep(.el-table) {
	.read-row {
		--el-table-tr-bg-color: var(--el-fill-color-light);
	}
}
</style>

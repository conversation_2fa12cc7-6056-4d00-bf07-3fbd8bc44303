import request from '/@/utils/request';

/**
 * @method getUsers 获取用户列表
 * @method createUser 新增
 * @method updateUser 修改
 * @method resetPassword  管理员重置密码
 * @method changePassword 修改密码（仅限当前用户）
 */

export function getUsers(params?: Object) {
	return request({
		url: `/users`,
		method: 'get',
		params,
	});
}

export function createUser(params: Object) {
	return request({
		url: `/users`,
		method: 'post',
		data: params,
	});
}

export function updateUser(params: EmptyObjectType) {
	return request({
		url: `/users/${params.id}`,
		method: 'put',
		data: params,
	});
}

export function resetPassword(data: Object) {
	return request({
		url: `/users/password`,
		method: 'post',
		data,
	});
}

export function changePassword(data: EmptyObjectType) {
	return request({
		url: `/users/${data.userId}/reset-password`,
		method: 'post',
		data,
	});
}


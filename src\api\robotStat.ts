// import { number } from 'echarts';
import request from '/@/utils/request';


type Trend = {
  type: StatType,
  deviceId?: number,
  eventType?: number
}

/**
 * 机器人统计
 * @param params 
 * @returns 
 */
export function getOverviewStat(params: {
  type: StatType
}) {
  return request({
    url: `/statistics/robots_patrol_overview`,
    method: 'get',
    params,
  });
}

/**
 * 单设备趋势统计
 * @param params 
 * @returns 
 */
export function getTrendStat(params: Trend) {
  return request({
    url: `/statistics/robot_patrol_trend`,
    method: 'get',
    params,
  });
}

/**
 * 任务统计概览
 * @param params 
 * @returns 
 */
export function getTaskOverviewStat(params: Trend) {
  return request({
    url: `/statistics/robots_task_overview`,
    method: 'get',
    params,
  });
}

/**
 * 任务统计趋势
 * @param params 
 * @returns 
 */
export function getTaskTrendStat(params: Trend) {
  return request({
    url: `statistics/robots_task_trend`,
    method: 'get',
    params,
  });
}



type PointsTask = {
  eventType: number,
  startDate?: string,
  endDate?: string,
  page: number,
  size: number,
}

/**
 * 任务统计趋势
 * @param params 
 * @returns 
 */
export function getRobotPointsTaskStat(params: PointsTask) {
  return request({
    url: `statistics/robots_points_task`,
    method: 'get',
    params,
  });
}

/**
 * 事件统计-左侧事件类型菜单接口
 * @returns 
 */
export function getGroupSubEvents() {
  return request({
    url: `monitor-events/group_sub_event_types`,
    method: 'get',
  });
}


/**
 * 事件统计-右侧事件
 * @returns 
 */
export function getRobotsEventsStat(params: { eventType?: number, eventGroupType?: number }) {
  return request({
    url: `statistics/robots_events_statistics`,
    method: 'get',
    params
  });
}


type ROverview = {
  date?: string,
  startDate?: string,
  endDate?: string,
  eventType?: number,
  groupEventType?: number
}


/**
 * 事件统计-中间事件列表
 * @returns 
 */
export function getRobotsEvents(params: ROverview) {
  return request({
    url: `/statistics/robots_events`,
    method: 'get',
    params
  });
}

/**
 * 事件统计-事件类型概览
 * @returns 
 */
export function getRobotsEventsOverview(params: ROverview) {
  return request({
    url: `/statistics/robots_events_overview`,
    method: 'get',
    params
  });
}

/**
 * 数据概览
 * @returns 
 */
export function getOverview() {
  return request({
    url: `statistics/overview`,
    method: 'get',
  });
}


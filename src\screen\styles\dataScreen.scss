@import '/src/theme/mixins/index.scss';
@import '/src/theme/utils.scss';

// 湿地大屏公共样式
.data-screen {
	width: 100%;
	height: 100%;
	background: $sd-screen-bg-color;
	display: flex;
	flex-direction: column;
	color: #fff;
	font-size: vw(14);
	user-select: none;
	&-header {
		height: vw(86);
	}
	&-main {
		flex: 1;
		overflow: hidden;
	}

	// 主内容区
	.main-container {
		width: 100%;
		height: 100%;
		display: flex;
		position: relative;
		.center-container {
			flex: 1;
			position: relative;
		}
	}
	.main-container.map {
		.center-container {
			&::before {
				content: '';
				display: block;
				position: absolute;
				width: 100%;
				height: vw(80);
				background: linear-gradient(
					to bottom,
					$sd-screen-bg-color,
					rgba(12, 30, 41, 0.2) 70%,
					transparent
				);
				z-index: 5;
			}
		}
	}
}

// 地图公共样式
.amap-container {
	background-color: transparent !important;
	.amap-maps {
		background-color: $sd-screen-bg-color !important;
	}
	.amap-logo,
	.amap-copyright {
		display: none !important;
	}
	// .amap-marker {
	//   pointer-events: none !important;
	// }
}

// 地图设备标记样式
.device-marker {
	width: vw(99);
	height: vw(63);
	position: relative;
	cursor: pointer;
	pointer-events: auto;
	.device-icon {
		width: 100%;
		height: 100%;
		cursor: pointer;
		user-select: none;
		pointer-events: none;
	}
	.device-info {
		position: absolute;
		top: vw(6);
		left: vw(25);
		color: #fff;
		font-size: vw(12);
		width: calc(100% - vw(33)); // 因为图片有阴影和边框，所以需要减去33px
		text-align: center;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: normal;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		line-height: 1.2;
		max-height: vw(32);
		word-wrap: break-word;
		word-break: break-all;
		pointer-events: none;

		&.single-line {
			line-height: 2.4;
		}
	}
}

// 各区域模块样式，比如物种统计、最近发现、物种占比等等
.module {
	.module-title {
		width: 98%;
		height: $sd-module-title-height;
		background: url('/@/assets/sd/data-screen/module-title-bg.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		font-size: vw(18);
		line-height: $sd-module-title-line-height;
		padding-left: 8.5%;
		padding-right: vw(20);
		font-family: 'AliMaMaShuHeiTi';
		display: flex;
		justify-content: space-between;
		align-items: center;
		span {
			display: flex;
			align-items: center;
			img {
				width: vw(30);
				height: vh(26);
				margin-right: vw(5);
			}
		}
		img {
			width: vw(22);
			height: vh(16);
			cursor: pointer;
		}
		.trend {
			width: vw(25);
		}
		.species {
			width: vw(26);
		}
		.paiming {
			width: vw(25);
		}
		.device {
			width: vw(26);
			height: $sd-device-icon-height;
		}
		.juliu {
			width: vw(24);
		}

		.patrol {
			width: vw(20);
			height: $sd-device-icon-height;
		}
	}
	.module-cont {
		margin: $sd-module-margin 0;
	}
}

// 文字阴影
.text-sd {
	text-shadow: 2px 0px 4px $sd-screen-bg-color2;
}

// map infowindow
.marker-window-modal {
	user-select: none;
	background-color: rgba(0, 0, 0, 0.2);
}
.marker-window-modal .marker-window.el-dialog {
	.el-dialog__header {
		display: none;
	}
	.el-dialog__body {
		padding: 0 !important;
		overflow-y: hidden;
		overflow: visible;
	}
}
.map-custom-info-window {
	width: 100%;
	background: linear-gradient(to right, rgba(12, 29, 40, 0.98), rgba(12, 30, 41, 1));
	box-shadow: 1px 1px 20px 6px rgba(12, 29, 40, 0.9);
	position: relative;
	animation-duration: 0.4s !important;
	color: #fff;
	.module-title {
		width: 104%;
		margin-left: -2%;
		font-size: vw(16);
	}
	.icon-close {
		position: absolute;
		top: vw(10);
		right: 6%;
		cursor: pointer;
		img {
			width: vw(14);
			opacity: 0.8;
			&:hover {
				opacity: 1;
			}
		}
	}
	.info-window-cont {
		height: $sd-info-window-content-height;
	}
	.ranks {
		padding: vw(15);
    @include sd-scrollBar;
		.rank-item {
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: vw(10);
			.serial-num {
				min-width: vw(60);
				font-family: 'ALiMaMaShuHeiTi';
				&.threeBefore {
					color: #ffe700;
				}
			}
			.info {
				flex: 1;
				height: $sd-rank-item-height;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 vw(10) 0;
				background: url('/@/assets/sd/data-screen/box-bg2.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
				.count {
					font-family: 'ALiMaMaShuHeiTi';
				}
			}
      &:first-child {
        margin-top: 0;
      }
		}
    .el-loading-mask {
		  background-color: transparent;
	  }
	}
}

// # reset element ui
.popper {
	background-color: $sd-screen-bg-color;
	border-color: $sd-screen-border-color;
	.el-popper__arrow::before {
		background-color: $sd-screen-bg-color !important;
		border-top-color: $sd-screen-border-color !important;
		border-left-color: $sd-screen-border-color !important;
	}
}
.bg {
	background: linear-gradient(
		to right,
		rgba(22, 226, 231, 0),
		rgba(33, 227, 221, 0.1) 20%,
		rgba(79, 229, 181, 0.3) 50%,
		rgba(118, 231, 146, 0.1) 80%,
		rgba(136, 232, 130, 0)
	);
}

// Select
.data-screen-select.el-select {
	width: vw(253);
	height: vw(40);
	background-color: transparent;
	border: none;
	padding: vw(5) 0;
	box-shadow: 0 0 0 1px rgba(33, 227, 221, 0.1) inset;
	--el-select-input-focus-border-color: $sd-screen-bg-color !important;

	&:hover:not(.el-select--disabled) .el-input__wrapper {
		box-shadow: 0 0 0 1px $sd-screen-success inset;
	}
	.el-input__wrapper {
		color: #fff;
		padding: 0 vw(20);
		box-shadow: none;
		border-radius: 0;
		@extend .bg;
	}

	.el-select__input {
		color: #fff;
	}
	.el-select-tags-wrapper .el-tag {
		background-color: transparent;
		color: #fff;
		.el-tag__close {
			color: #ccc;
			&:hover {
				background-color: transparent;
				color: #fff;
			}
		}
	}
	.el-select__suffix .el-icon {
		color: #fff;
	}

	.el-popper {
		border: none;
		background-color: rgba(4, 31, 39, 1);
		border-radius: 0;
		.el-popper__arrow {
			display: none;
		}
		.el-select-dropdown__item {
			padding: 0 vw(10) 0 vw(20);
			margin-bottom: vw(5);
			// background: linear-gradient(to right, rgba(22, 226, 231, 0), rgba(33, 227, 221, 0.3), rgba(79, 229, 181, 0.2), rgba(136, 232, 130, 0));
			.custom-option {
				display: flex;
				justify-content: space-between;
				color: #fff;
				.el-radio .el-radio__label {
					display: none;
				}
			}
		}
		.el-select-dropdown__item.selected {
			@extend .bg;
			&::after {
				display: none;
			}
		}
	}
}
// Radio
.data-screen-radio.el-radio {
	.el-radio__inner {
		width: vw(16);
		height: vw(16);
		background-color: transparent;
		&:hover {
			border-color: $sd-screen-success;
		}
	}
	.el-radio__input.is-checked .el-radio__inner {
		background-color: transparent !important;
		border-color: #fff !important;
		&::after {
			width: vw(6);
			height: vw(6);
			background-color: $sd-screen-success;
		}
	}
}
// Radio Group
.data-screen-radio-group.el-radio-group {
	width: auto;
	height: vw(40) !important;
	padding: vw(6) 0;
	background: url('/@/assets/sd/data-screen/box-bg2.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;
	.el-radio-button {
		height: 100%;
		.el-radio-button__inner {
			width: vw(75);
			height: 100%;
			line-height: vw(26);
			padding: 0;
			background-color: transparent;
			border-color: transparent;
			color: #9acfff;
		}
	}
	.el-radio-button.is-active
		.el-radio-button__original-radio:not(:disabled)
		+ .el-radio-button__inner {
		color: #fff;
		box-shadow: -1px 0 0 0 $sd-screen-border-color;
		border-color: $sd-screen-border-color;
		@extend .bg;
	}
}
// Progress
.data-screen-progress.el-progress {
	.el-progress-bar {
		min-width: vw(100);
		.el-progress-bar__outer {
			// height: vw(20) !important;
			background: linear-gradient(
				to right,
				rgba(22, 226, 231, 0),
				rgba(33, 227, 221, 0.3),
				rgba(79, 229, 181, 0.2),
				rgba(136, 232, 130, 0)
			);
			border: vw(2) solid rgba(151, 151, 151, 0.2);
		}
		.el-progress-bar__inner--striped {
			background-image: linear-gradient(
				-45deg,
				rgba(0, 0, 0, 0.1) 25%,
				transparent 0,
				transparent 50%,
				rgba(0, 0, 0, 0.1) 0,
				rgba(0, 0, 0, 0.1) 75%,
				transparent 0,
				transparent
			);
		}
	}
	.el-progress__text {
		color: #fff;
		font-size: vw(14) !important;
	}
	.el-progress-bar__outer {
		height: vw(16) !important;
	}
}
// Image
.el-image {
	.image-placeholder {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		.is-loading {
			color: $sd-screen-primary;
			font-size: 18px;
		}
	}
}
// Dropdown
.el-dropdown {
	.el-popper {
		@extend .popper;
	}
	.el-dropdown-menu {
		background-color: transparent;
		.el-dropdown-menu__item {
			color: #fff;
			font-family: none;
			margin: vw(8) 0;
		}
		.el-dropdown-menu__item:not(.is-disabled):hover,
		.el-dropdown-menu__item:not(.is-disabled):focus {
			background-color: transparent;
			color: $sd-screen-success !important;
		}
	}
}

.el-select .el-input.is-focus .el-input__wrapper {
	box-shadow: 0 0 0 1px $sd-screen-success inset !important;
}

.title {
	font-size: vw(16);
	font-family: 'ALiMaMaShuHeiTi';
}

.species-frequency {
	flex: 1;
	.species-frequency-cont {
		height: 100%;
		overflow: visible;
		.rank-list {
			padding: 0 vw(36);
			height: 100%;
			.legend {
				height: 100%;

				display: flex;
				justify-content: flex-end;
				align-items: center !important;

				span {
					font-size: vw(12);
					margin-right: vw(30);
				}
			}
			i {
				display: inline-block;
				font-style: normal;
				width: 10px;
				height: 10px;
				border-radius: 50%;
				margin-right: vw(5);
			}
			.legend-specific {
				background-color: #ffda00;
			}
			.legend-precious {
				background-color: #fc74d9;
			}
			.legend-nmg {
				background-color: #5af6e8;
			}
			.rank-title {
				height: vw(43);
				font-size: vw(14) !important;
				// background: url('/@/assets/sd/data-screen/list-title.png');
				background-repeat: no-repeat;
				background-size: 88% 100%;
				background-position: center;
				padding-bottom: 0 !important;
			}
			width: calc(100% + vw(40));
			position: relative;
			left: vw(-20);
			min-height: vw(245);
			max-height: 100%;
			background-repeat: no-repeat;
			background-size: 100% 100%;
			.rank-item-bg {
				height: vw(37);
				background: url('/@/assets/sd/data-screen/rank-list-bg.png') no-repeat;
				background-size: 100%;
				margin-bottom: vw(10);
			}

			.rank-item {
				display: flex;
				justify-content: center;
				align-items: center;
				text-align: center;

				.rank {
					width: vw(30);
				}

				.name {
					flex: 1;
					padding: 0 vw(5);
					.text-ellipsis {
						display: inline-block;
						max-width: 100%;
						overflow: hidden;
						white-space: nowrap;
					}
					.tag-tooltips {
						display: inline-block;
						width: vw(16);
						height: vw(16);
						vertical-align: text-top;
						margin-left: vw(4);
						color: $sd-screen-primary;
						cursor: pointer;
					}
				}
				.species {
					width: vw(70);
				}
				.frequency {
					width: vw(45);
				}
				.count {
					width: vw(45);
				}
				.shoot {
					width: vw(58);
				}

				&.loading,
				&.no-data {
					justify-content: center;
					height: vw(100);
					font-size: vw(14);
					text-align: center;
				}
				.no-data {
					color: var(--el-text-color-secondary);
					text-align: center;
				}
				&.loading {
					display: flex;
					align-items: center;
					gap: vw(10);
					color: $sd-screen-primary;
					.is-loading {
						width: vw(16);
						animation: rotating 2s linear infinite;
					}
				}
			}
		}
	}
}
@keyframes rotating {
	0% {
		transform: rotate(0);
	}
	100% {
		transform: rotate(360deg);
	}
}

.no-data {
	color: var(--el-text-color-secondary);
	text-align: center;
}

// 空状态
.el-empty {
	--el-empty-padding: 0;
}

.filter-container {
	position: absolute;
	top: 0;
	left: 50%;
	transform: translateX(-50%);
	padding: vw(24) 0 0;
	z-index: 10;
	display: flex;
	align-items: center;
	justify-content: center;
	.data-screen-select {
		margin-right: vw(15);
	}
}

.device-img {
	position: relative;
	width: vw(39);
	cursor: pointer;
	user-select: none;
	.device-img-icon {
		width: 100%;
		height: 100%;
	}
}

// 地图信息窗口
.map-custom-info-window {
	width: vw(750);
	background: #0a1822;
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: vw(4);
	animation: infoWindowFade 0.3s ease-in-out;

	.info-window-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: vw(39);
		padding: 0 vw(17);
		font-family: 'AliMaMaShuHeiTi';
		background: linear-gradient(
			to right,
			rgba(5, 45, 46, 1),
			rgba(42, 227, 213, 0.43),
			rgba(79, 229, 181, 1),
			rgba(33, 227, 221, 0.3),
			rgba(3, 37, 55, 1)
		);

		.info-window-title-left {
			color: #fff;
			font-size: vw(16);
		}

		.info-window-title-close {
			width: vw(14);
			height: vw(14);
			background: url('/@/assets/sd/data-screen/close.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			cursor: pointer;
		}
	}
	.info-window-container {
		height: auto;

		.video-container {
			width: 100%;
			height: 100%;
			background: #000;
			aspect-ratio: 16/9;
			.no-video {
				height: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				color: #999;
				font-size: vw(14);
			}
		}
		.video-switch {
			margin: vw(5) 0 vw(5) vw(15);
		}

		.el-radio {
			margin-right: 40px;
			.el-radio__label {
				color: rgba(255, 255, 255, 1);
				font-size: vw(14);
			}
			.el-radio__inner {
				width: vw(16);
				height: vw(16);
				background-color: transparent !important;
				&:hover {
					border-color: $radio-color;
				}
			}
			.is-checked {
				.el-radio__inner {
					width: vw(16);
					height: vw(16);
					border-color: #fff !important;
					border-width: vw(3);
					background-color: $radio-color !important;
				}
			}
			.el-radio__input.is-checked + .el-radio__label {
				color: $radio-color !important;
			}
			.el-radio__inner::after {
				display: none;
			}
		}
	}
}

// 添加动画关键帧
@keyframes infoWindowFade {
	from {
		opacity: 0;
		transform: translateY(-10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

// 黑龙江大屏按钮
.hlj-btn {
	position: relative;
	width: vw(90);
	height: vw(36);
	font-size: vw(14);
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	margin-left: vw(20);
	// 图案颜色
	--pattern-color: rgba(33, 227, 221, 0.065);

	background: linear-gradient(
			to right,
			rgba(22, 226, 231, 0),
			rgba(33, 227, 221, 0.1) 20%,
			rgba(79, 229, 181, 0.3) 50%,
			rgba(118, 231, 146, 0.1) 80%,
			rgba(136, 232, 130, 0)
		),
		linear-gradient(var(--pattern-color) 1px, transparent 1px),
		linear-gradient(to right, var(--pattern-color) 1px, transparent 1px);
	background-size: cover, vw(15) vw(15), vw(15) vw(15);
	background-position: center center, center center, center center;
	transition: all 0.2s ease-in-out;

	// 保持原有的边框效果
	&::before,
	&::after {
		content: '';
		position: absolute;
		left: 0;
		right: 0;
		height: 1px;
		background: linear-gradient(
			to right,
			rgba(14, 55, 57, 1),
			rgba(14, 55, 57, 1),
			rgba(14, 55, 57, 0.3),
			transparent 50%,
			rgba(14, 55, 57, 0.3),
			rgba(14, 55, 57, 1),
			rgba(14, 55, 57, 1)
		);
	}

	&::before {
		top: 0;
	}

	&::after {
		bottom: 0;
	}

	box-shadow: 1px 0 0 0 rgba(14, 55, 57, 1), -1px 0 0 0 rgba(14, 55, 57, 1);

	// hover效果
	&:hover {
		background-size: cover, vw(10) vw(10), vw(10) vw(10);
	}
}

.chart-loading-class {
	background-color: transparent;
	.el-loading-spinner {
		.path {
			stroke: #31edb5;
		}
	}
}

@keyframes float-effect {
	0% {
		transform: translateY(0) rotate(0deg) scale(1);
	}
	25% {
		transform: translateY(vw(-4)) rotate(1deg) scale(1.02);
	}
	50% {
		transform: translateY(0) rotate(0deg) scale(1);
	}
	75% {
		transform: translateY(vw(2)) rotate(-1deg) scale(0.98);
	}
	100% {
		transform: translateY(0) rotate(0deg) scale(1);
	}
}

.gradient-bg {
	background: linear-gradient(
		to right,
		rgba(79, 229, 181, 0.2),
		rgba(33, 227, 221, 0.06),
		rgba(22, 226, 231, 0),
		rgba(33, 227, 221, 0.06),
		rgba(79, 229, 181, 0.2)
	);
}

<template>
  <el-dialog
    v-model="state.dialogVisible"
    :title="state.dialogType === 1 ? '新增物种' : '修改物种'"
    width="1000px"
    align-center
  >
    <el-form ref="formRef" :model="state.formData" :rules="formRules" label-width="80px" size="large">
      <el-row :gutter="10">
        <template v-for="item in renderTags">

          <el-col :key="item.prop" v-if="item.visible && item.type !== 'custom'" :span="item.colSpan">
            <el-form-item :label="item.label" :prop="item.prop">
              <!-- 下拉选择的表单 -->
              <el-select
                v-if="item.type === 'select'"
                v-model="state.formData[item.prop]"
                :placeholder="'请选择' + item.label"
                style="width: 100%;"
                clearable
                @change="(val: string) => { onSelectPropChange(item.prop, val) }"
              >
                <el-option
                  v-for="opt in state[`${item.prop}Options`]"
                  :key="opt.id"
                  :label="opt.label"
                  :value="opt.label"
                />
              </el-select>
              <!-- 支持远程搜索的表单 -->
              <el-select
                v-else-if="item.type === 'select-filterable'"
                :model-value="state.formData[item.prop]"
                :placeholder="`请输入${item.label}`"
                filterable
                style="width: 100%;"
                clearable
                remote
                :maxlength="10"
                :loading="state.remoteLoading"
                loading-text="您可能想要查找这些数据，Loading..."
                popper-class="create-species-select-filterable"
                :remote-method="(query: string) => onRemoteMethod(item.prop, query)"
                @change="(value: string) => onRemoteSelect(item.prop, value)"
              >
                <el-option v-for="ropt in state.remoteOptions" :key="ropt.id" :value="ropt[item.prop]">
                  <div class="o-label">
                    <span>{{ ropt[item.prop] }}</span>
                    <span>（{{ ropt.status === 1 && ropt.scopeStatus === 1 ? '已开放' : '暂未开放' }}）</span>
                  </div>
                </el-option>
              </el-select>
              <!-- 单选表单，如保护等级、居留类型 -->
              <el-radio-group v-else-if="item.type === 'radio'" v-model="state.formData[item.prop]">
                <el-radio v-for="r in item.radioOptions" :label="r.value" :key="r.value">
                  {{ r.label }}
                </el-radio>
              </el-radio-group>
              <!-- 物种标签 -->
              <div class="bird-tags" v-else-if="item.type === 'tag'">
                <el-tag
                  v-for="tag in item.tagOptions"
                  :key="tag.value"
                  :type="state.selectedTags.includes(tag.value) ? undefined: 'info'"
                  size="default"
                  :effect="state.selectedTags.includes(tag.value) ? 'dark': 'plain'"
                  round
                  @click="onSelectTag(tag.value)"
                >
                  {{ tag.label }}
                </el-tag>
              </div>
              <!-- 上传图片表单，如代表图 -->
              <el-upload
                v-else-if="item.type === 'images'"
                v-model:file-list="state.formData[item.prop]"
                action="#"
                :multiple="true"
                accept="image/jpg,image/png,image/jpeg"
                :auto-upload="false"
                list-type="picture-card"
                :on-change="onFileChange"
                :on-remove="onFileRemove"
              >
              <el-icon><ele-Plus /></el-icon>
                <template #tip>
                  <div class="el-upload__tip mt0">
                    图片格式仅支持jpg/jpeg/png，且大小不能超过50M。
                  </div>
                </template>
              </el-upload>
              <!-- 多行文本输入的表单 -->
              <el-input
                v-else-if="item.type === 'textarea'"
                v-model.trim="state.formData[item.prop]"
                type="textarea"
                :rows="3"
                clearable
                :placeholder="`请输入${item.label}`"
              />
              <!-- 单行文本输入的表单 -->
              <el-input
                v-else
                v-model.trim="state.formData[item.prop]"
                :maxlength="60"
                clearable
                :placeholder="`请输入${item.label}`"
              />
            </el-form-item>
          </el-col>

          <!-- 鱼类自定义属性与属性值 -->
          <el-col :key="item.colSpan" v-if="item.visible && item.type === 'custom'" :span="item.colSpan" class="el-col-custom">
            <el-form-item>
              <el-button type="primary" size="default" @click="onAddCustomField(item.prop)">
                <template #icon>
                  <el-icon><ele-Plus /></el-icon>
                </template>
                自定义
              </el-button>
            </el-form-item>
            <div class="el-col-custom-form" v-if="state.formData[item.prop]">
              <div v-for="(cus, $index) in state.formData[item.prop]" :key="$index">
                <el-form-item :label="'特征' + ($index + 1)">
                  <el-input v-model.trim="cus.key" placeholder="请输入（例：形态）" />
                </el-form-item>
                <el-form-item label="描述">
                  <el-input type="textarea" v-model.trim="cus.value" placeholder="请输入（例：体较高，侧扁。鳃耙9。侧线鳞52—54...）" />
                </el-form-item>
                <el-button class="ml10" circle @click="onDelCustomField(item.prop, $index)">
                  <template #icon>
                    <el-icon :size="18"><ele-Delete /></el-icon>
                  </template>
                </el-button>
              </div>
            </div>
          </el-col>

        </template>
      </el-row>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="state.dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="state.dialogType === 1 ? onCreate() : onUpdate()">
          {{ state.dialogType === 1 ? '新 增' : '修 改' }}
        </el-button>
      </span>
    </template>

  </el-dialog>
</template>

<script setup lang='ts'>
import { ref, reactive, computed, nextTick } from 'vue';
import type { FormInstance, UploadFiles, UploadFile } from 'element-plus';
import { ElMessage } from 'element-plus';
import {
  createDirectory, updateDirectory, nameFilter,
  getClasses, getOrders, getFamilies, getGenus,
  getAnimalByName, updateAnimal,
} from '/@/api/species';
import type { Species, RuleObj, FormFields, Tags, Detail, HistoryImage, Animal } from '../type';
import { Bird_Tag, protectLevelOptions, residencyTypeOptions } from '/@/utils/constants';
import { AUTHS } from '/@/directive/authDirective';

const props = defineProps<{
  speciesType: number,
}>();

const emits = defineEmits<{
  (e: 'refreshData', resetPage: boolean, resetFilter: boolean): void;
}>();

// 是否拥有管理动物标签的权限，目前仅鸟类拥有物种标签
const hasAnimalsAuth = computed(() => {
  return (props.speciesType === 1 && AUTHS(['*:*:*', 'animals:*:*']));
});


/**
 * 根据数据库中定义的各物种参数字段，生成植物、鸟、昆虫、动物（哺乳动物/鱼类/两栖类/爬行类）的form表单项
 * 注意：【鱼类】支持自定义属性与属性值
 */
const commonTag: Tags[] = [
  { visible: true, label: '名称', prop: 'name', required: true, type: 'select-filterable', colSpan: 12},
  { visible: true, label: '拉丁名', prop: 'latinName', required: true, type: 'select-filterable', colSpan: 12},
  { visible: true, label: '纲', prop: 'className', required: true, type: 'select', colSpan: 12 },
  { visible: true, label: '目', prop: 'order', required: true, type: 'select', colSpan: 12 },
  { visible: true, label: '科', prop: 'family', required: true, type: 'select', colSpan: 12 },
  { visible: true, label: '属', prop: 'genus', required: true, type: 'select', colSpan: 12 },
];
const speciesTypeTags: Tags[][] = [
  [
    ...commonTag,
    { visible: true, label: '保护等级', prop: 'protectLevel', required: true, type: 'radio', colSpan: 24, radioOptions: protectLevelOptions },
    { visible: true, label: '形态特征', prop: 'describe', required: true, type: 'textarea', colSpan: 24 },
    { visible: true, label: '代表图', prop: 'imageList', required: true, type: 'images', colSpan: 24 },
  ],
  [
    ...commonTag,
    { visible: true, label: '俗名', prop: 'commonName', required: false, type: 'input', colSpan: 12 },
    { visible: true, label: '虹膜', prop: 'iris', required: false, type: 'input', colSpan: 12 },
    { visible: true, label: '叫声', prop: 'sound', required: false, type: 'input', colSpan: 12 },
    { visible: true, label: '保护等级', prop: 'protectLevel', required: true, type: 'radio', colSpan: 24, radioOptions: protectLevelOptions },
    { visible: hasAnimalsAuth.value, label: '标签', prop: 'tag', required: false, type: 'tag', colSpan: 24, tagOptions: Bird_Tag},
    { visible: hasAnimalsAuth.value, label: '居留类型', prop: 'residencyType', required: false, type: 'radio', colSpan: 24, radioOptions: residencyTypeOptions },
    { visible: true, label: '形态特征', prop: 'describe', required: true, type: 'textarea', colSpan: 24 },
    { visible: true, label: '分布地区', prop: 'distributionRange', required: false, type: 'textarea', colSpan: 24 },
    { visible: true, label: '分布情况', prop: 'distribution', required: false, type: 'textarea', colSpan: 24 },
    { visible: true, label: '习性', prop: 'habit', required: false, type: 'textarea', colSpan: 24 },
    { visible: true, label: '代表图', prop: 'imageList', required: true, type: 'images', colSpan: 24 },
  ],
  [
    ...commonTag,
    { visible: true, label: '保护等级', prop: 'protectLevel', required: true, type: 'radio', colSpan: 24, radioOptions: protectLevelOptions },
    { visible: true, label: '形态特征', prop: 'describe', required: true, type: 'textarea', colSpan: 24 },
    { visible: true, label: '代表图', prop: 'imageList', required: true, type: 'images', colSpan: 24 },
  ],
  [
    ...commonTag,
    { visible: true, label: '保护等级', prop: 'protectLevel', required: true, type: 'radio', colSpan: 24, radioOptions: protectLevelOptions },
    { visible: true, label: '形态特征', prop: 'morphologicalDescription', required: true, type: 'textarea', colSpan: 24 },
    { visible: true, label: '鉴别特征', prop: 'differentiateCharacteristic', required: false, type: 'textarea', colSpan: 24 },
    { visible: true, label: '分布地区', prop: 'distribution', required: false, type: 'textarea', colSpan: 24 },
    { visible: true, label: '代表图', prop: 'imageList', required: true, type: 'images', colSpan: 24 },
  ],
  // 鱼类
  [
    ...commonTag,
    { visible: true, label: '别名', prop: 'alias', required: false, type: 'input', colSpan: 12 },
    { visible: true, label: '保护等级', prop: 'protectLevel', required: true, type: 'radio', colSpan: 24, radioOptions: protectLevelOptions  },
    { visible: true, label: '自定义', prop: 'detail', required: false, type: 'custom', colSpan: 24 },
    { visible: true, label: '代表图', prop: 'imageList', required: true, type: 'images', colSpan: 24 },
  ],
];
const renderTags = computed(() => {
  return props.speciesType === 4 ? speciesTypeTags[4] :
          props.speciesType > 2 ? speciesTypeTags[3] : speciesTypeTags[Number(props.speciesType)];
});

const formRef = ref<FormInstance>();
const formRules = computed(() => {
  const ruleObj: RuleObj = {};
  renderTags.value
    .filter((item) => item.required)
    .forEach((item) => {
      ruleObj[item.prop] = {
        required: true, message: `${item.label}不能为空`, trigger: 'blur',
      };
    })
  return ruleObj;
})
const state: EmptyObjectType = reactive({
  dialogVisible: false,
  dialogType: 0, // 1增加 2修改
  formData: <FormFields>{},
  selectedTags: [], // 物种选中的标签
  
  specieId: 0, // 要修改的物种id
  remoteLoading: false, // 是否从远程查询关联数据（仅支持可远程搜索的表单，比如名称、拉丁名）
  remoteOptions: <string[]>[], // 远程查询关联数据
  classNameOptions: [], // 纲
  orderOptions: [], // 目
  familyOptions: [], // 科
  genusOptions: [], // 属
})

const show = async (item?: Species) => {
  state.dialogType = item?.id ? 2 : 1;
  state.dialogVisible = true;
  nextTick(() => {
    formRef.value?.resetFields();
    // 重置滚动条
    const dialogBodyEle = document.querySelector('.el-dialog .el-dialog__body');
    if(dialogBodyEle) dialogBodyEle.scrollTop = 0;
    // 修改时回显数据
    if (item?.id) {
      state.specieId = item.id;
      const propArr = renderTags.value.map((f) => f.prop);
      propArr.forEach((prop: string) => {
        if (prop === 'detail') {
          state.formData[prop] = detailObj2Arr(<string>item[prop]);
          return;
        }
        state.formData[prop] = item[prop];
      })
      getClassNameOptions();
      if (!state.formData['protectLevel']) state.formData['protectLevel'] = '普通';
      // 代表图预览
      state.formData['imageList'] = item.historyImages ?
        item.historyImages.map((hisImage: EmptyObjectType) => ({
          name: hisImage.imageId,
          url: hisImage.imageUrl,
          imageId: hisImage.id,
        })) : [];
      // 修改时，若拥有管理动物标签的权限，则先获取当前物种标签详情，回显数据
      if (hasAnimalsAuth.value) {
        getAnimal();
      }
      return;
    }
    // 新增时重置数据
    if (!state.formData['protectLevel']) state.formData['protectLevel'] = '普通';
    state.formData['detail'] = [];
    getClassNameOptions();
    state.orderOptions = [];
    state.familyOptions = [];
    state.genusOptions = [];
    state.selectedTags = [];
  })
}
// 根据名称或拉丁名，查询关联物种列表
const onRemoteMethod = async (prop: string, query: string) => {
  if (!query) return;
  state.formData[prop] = query;
  state.remoteLoading = true;
  const data = {
    name: query,
    type: prop === 'name' ? 0 : 1, //0中文名 1拉丁名
  };
  const { payload } = await nameFilter(props.speciesType, data);
  state.remoteOptions = payload;
  state.remoteLoading = false;
};
// 若从远程关联到的下拉物种列表中选择，则代表要修改当前物种（回显当前物种数据）
const onRemoteSelect = (prop: string, value: string) => {
  if (!value) {
    state.formData[prop] = '';
    return;
  }
  const selected = state.remoteOptions.filter((item: any) => item[prop] === value);
  show(selected[0]);
};

// 获取纲
const getClassNameOptions = async () => {
  const data = {
    type: props.speciesType === 0 ? 'Plantae' : 'Animalia',
  };
  const { payload } = await getClasses(data);
  state.classNameOptions = payload.map((item: any) => ({ id: item.id, label: item.classC }));
  state.formData['className'] && getOrderOptions(state.formData.className);   
};
/**
 * 获取父级纲下的目
 * @param className 父级纲
 */
const getOrderOptions = async (className: string) => {
  const { id } = state.classNameOptions.find((item:any) => item.label === className);
  const { payload } = await getOrders({ classId: id });
  state.orderOptions = payload.map((item: any) => ({ id: item.id, label: item.orderC }));
  state.formData['order'] && getFamilyOptions(state.formData.order);
};
/**
 * 获取父级目下的科
 * @param order 父级目
 */
const getFamilyOptions = async (order: string) => {
  const { id } = state.orderOptions.find((item:any) => item.label === order)
  const { payload } = await getFamilies({ orderId: id });
  state.familyOptions = payload.map((item: any) => ({ id: item.id, label: item.familyC }));
  state.formData['family'] && getGenusOptions(state.formData.family);
};
/**
 * 获取父级科下的属
 * @param family 父级科
 */
const getGenusOptions = async (family: string) => {
  const { id } = state.familyOptions.find((item:any) => item.label === family)
  const { payload } = await getGenus({ familyId: id });
  state.genusOptions = payload.map((item: any) => ({ id: item.id, label: item.genusC }));
};
/**
 * 针对纲目科属变化的情况，重新获取下拉项
 * @param prop 纲/目/科/属对应的字段
 * @param value 纲/目/科/属对应的值
 */
const onSelectPropChange = (prop: string, value?: string) => {
  if (prop === 'className') {
    formRef.value?.resetFields(['order', 'family', 'genus']);
    if (!value) {
      state.orderOptions = [];
      state.familyOptions = [];
      state.genusOptions = [];
      return
    }
    getOrderOptions(value);
    return
  }
  if (prop === 'order') {
    formRef.value?.resetFields(['family', 'genus']);
    if (!value) {
      state.familyOptions = [];
      state.genusOptions = [];
      return
    }
    getFamilyOptions(value);
    return
  }
  if (prop === 'family') {
    formRef.value?.resetFields(['genus']);
    if (!value) {
      state.genusOptions = [];
      return
    }
    getGenusOptions(value);
  }
}

// # 鱼类自定义属性
const onAddCustomField = (prop: string) => {
  state.formData[prop] ? state.formData[prop].push({}) : state.formData[prop] = [{}];
};
const onDelCustomField = (prop: string, $index: number) => {
  state.formData[prop].splice($index, 1);
};
const valiDetail = () => {
  if (state.formData['detail']) {
    const completed = state.formData['detail'].every((item: { key: string; value: string; }) => item.key);
    if (!completed) ElMessage.error('自定义特征名不能为空');
    return completed;
  }
  return true;
};
// 解析鱼类自定义属性，字符串对象转数组
const detailObj2Arr = (str: string) : Detail[] => {
  const arr: Detail[] = [];
  if (str && typeof str == 'string') {
    const obj = JSON.parse(str);
    if (obj !== '{}') {
      return Object.keys(obj).map((item) => ({
        key: item,
        value: obj[item],
      }))
    }
    return [];
  }
  return arr;
};
// 解析鱼类自定义属性，数组转字符串对象
const detailArr2Obj = (arr: Detail[]) : string => {
  const obj: { [key: string] : string } = {};
  arr.forEach((item: Detail) => {
    obj[item.key] = item.value;
  })
  return JSON.stringify(obj);
};

// # 上传代表图
const onFileChange = (uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  state.formData['imageList'] = uploadFiles;
}
const onFileRemove = (uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  state.formData['imageList'] = uploadFiles;
}

// # 管理动物标签，可设定物种标签`state.selectedTags` 和 居留类型`state.formData['residencyType']`
const oldName = ref(''); // `updateAnimal`接口参数oldName：原始物种名称，新增时为name；修改时为修改前的name
// 获取当前物种标签详情
const getAnimal = async () => {
  state.selectedTags = [];
  oldName.value = state.formData['name'];
  const { payload } = await getAnimalByName(oldName.value);
  if (<Animal>payload) {
    state.selectedTags = payload.tags;
    state.formData['residencyType'] = payload.residencyType;
  }
};
// 选择标签
const onSelectTag = (tag: number) => {
  if (state.selectedTags.includes(tag)) {
    state.selectedTags = state.selectedTags.filter((t: number) => t !== tag);
  } else {
    state.selectedTags.push(tag);
  }
};
// 修改物种标签详情
const onUpdateAnimal = (newData: Species) => {
  const data = {
    oldName: oldName.value,
    name: newData.specie || newData.name,
    imageUrl: newData.imageUrl,
    tags: state.selectedTags.length > 0 ? state.selectedTags : null, // 物种标签
    residencyType: Number(state.formData['residencyType']), // 居留类型
  };
  updateAnimal(data);
};

const onCreate = () => {
  formRef.value?.validate(async (vaild) => {
    if (vaild) {
      if (!valiDetail()) return;
      // 动物标签相关参数通过`updateAnimal`接口设置，其他通过`createDirectory`接口设置
      const formData = new FormData();
      for(let key in state.formData) {
        if (key === 'imageList') {
          state.formData['imageList'] && state.formData['imageList'].forEach((item: UploadFile) => {
            formData.append(key, item.raw as File);
          })
        } else if (key === 'protectLevel') {
          // 保护等级protectLevel为普通时不传
          state.formData[key] !== '普通' && formData.append(key, state.formData[key] as string);
        } else if (key === 'detail') {
          formData.append(key, detailArr2Obj(state.formData[key]));
        } else if (key !== 'residencyType') {
          state.formData[key] && formData.append(key, state.formData[key] as string);
        }
      }
      const { payload } = await createDirectory(props.speciesType, formData);
      ElMessage.success('创建成功');
      // 若拥有管理动物标签的权限，则需调用修改接口`updateAnimal`
      if (hasAnimalsAuth.value) {
        oldName.value = state.formData['name'];
        onUpdateAnimal(payload);
      }
      state.dialogVisible = false;
      emits('refreshData', true, true);
    }
  })
}
const onUpdate = () => {
  formRef.value?.validate(async (vaild) => {
    if (vaild) {
      if (!valiDetail()) return;
      const formData = new FormData();
      for(let key in state.formData) {
        if (key === 'imageList') {
          /**
           * 修改时，imageList中可能包含已上传图片，和新增加图片
           * 若是已上传图片，将其传入historyImages字段，格式为[imageId, imageId...]
           * 否则，传入imageList字段
           */
          state.formData['imageList'] && state.formData['imageList'].forEach((item: UploadFile | HistoryImage) => {
            (<UploadFile>item).status === 'success' ?
              formData.append('historyImages', (<HistoryImage>item).imageId as string) :
              formData.append(key, (<UploadFile>item).raw as File);
          })
        } else if (key === 'protectLevel') {
          // 保护等级protectLevel为普通时不传
          state.formData[key] !== '普通' && formData.append(key, state.formData[key] as string);
        } else if (key === 'detail') {
          formData.append(key, detailArr2Obj(state.formData[key]));
        } else if (key !== 'residencyType')  { // 居留类型不由`updateDirectory`接口设置
          state.formData[key] && formData.append(key, state.formData[key] as string);
        }
      }
      const { payload } = await updateDirectory(props.speciesType, state.specieId, formData);
      ElMessage.success('修改成功');
      // 若拥有管理动物标签的权限，则需调用修改接口`updateAnimal`
      if (hasAnimalsAuth.value) {
        onUpdateAnimal(payload);
      }
      state.dialogVisible = false;
      emits('refreshData', false, false);
    }
  })
}

defineExpose({
  show
})
</script>

<style lang="scss" scoped>
  :deep(.el-row .el-col-custom) {
    .el-col-custom-form div {
      display: flex;
      .el-form-item:first-child {
        width: 300px;
      }
      .el-form-item:not(:first-child) {
        flex: 1;
      }
      .el-form-item .el-form-item__label {
        height: 52px;
        line-height: 52px;
      }
      .el-button.is-circle {
        margin-top: 6px;
      }
    }
  }
  :deep(.bird-tags) {
    .el-tag {
      margin-right: 10px;
      cursor: pointer;
    }
  }
</style>
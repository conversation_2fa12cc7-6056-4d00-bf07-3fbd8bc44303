import request from '/@/utils/request';

/**
 * （不建议写成 request.post(xxx)，因为这样 post 时，无法 params 与 data 同时传参）
 * 巡护管理api接口集合
 * @method initPatrol 同步任务：(同步今日、同步历史: 同步今日就是startDate=endDate=今日, 同步历史就让选择日期区间 )
 * @method addMapShow 大屏展示 添加
 * @method removeMapShow 大屏展示 移除
 * @method batchDel 批量删除
 * @method initPatrolPlans  同步计划
 * @method initPatrolLines  同步线路
 */
export function initPatrolTasks(params: object) {
  return request({
    url: 'robots/init_patrol_tasks',
    method: 'post',
    params,
  });
}

export function addMapShow(id: number | string) {
  return request({
    url: `robot-patrol-tasks/${id}/map_show`,
    method: 'post',
  });
}

export function removeMapShow(id: number | string) {
  return request({
    url: `robot-patrol-tasks/${id}/map_show`,
    method: 'delete',
  });
}

export function delTask(id: string) {
  return request({
    url: `robot-patrol-tasks/${id}`,
    method: 'delete',
  });
}
export function batchDelTasks(data: string[]) {
  return request({
    url: `robot-patrol-tasks/del`,
    method: 'delete',
    data
  });
}

export function initPatrolPlans() {
  return request({
    url: `robots/init_patrol_plans`,
    method: 'post',
  });
}
export function initPatrolLines() {
  return request({
    url: `robots/init_patrol_lines`,
    method: 'post',
  });
}


import type { App } from 'vue';
import { useUserInfo } from '/@/stores/userInfo';
import { judementSameArr } from '/@/utils/arrayOperation';

/**
 * 用户权限指令
 * @directive 单个权限验证（v-auth="xxx"）
 * @directive 多个权限验证，满足一个则显示（v-auths="[xxx,xxx]"）
 * @directive 多个权限验证，全部满足则显示（v-auth-all="[xxx,xxx]"）
 */
export function authDirective(app: App) {
	// 单个权限验证（v-auth="xxx"）
	app.directive('auth', {
		mounted(el, binding) {
			const stores = useUserInfo();
			if (!stores.userInfos.permissions.some((v: string) => v === binding.value)) el.parentNode.removeChild(el);
		},
	});
	// 多个权限验证，满足一个则显示（v-auths="[xxx,xxx]"）
	app.directive('auths', {
		mounted(el, binding) {
			let flag = false;
			const stores = useUserInfo();
			stores.userInfos.permissions.map((val: string) => {
				binding.value.map((v: string) => {
					if (val === v) flag = true;
				});
			});
			if (!flag) el.parentNode.removeChild(el);
		},
	});
	// 多个权限验证，全部满足则显示（v-auth-all="[xxx,xxx]"）
	app.directive('auth-all', {
		mounted(el, binding) {
			const stores = useUserInfo();
			const flag = judementSameArr(binding.value, stores.userInfos.permissions);
			if (!flag) el.parentNode.removeChild(el);
		},
	});
}

/**
 * 用户权限方法
 * @method auth 单个权限验证
 * @method auths 多个权限验证，满足一个则返回true
 * @method authAll 多个权限验证，全部满足则返回true
 */
// 单个权限验证
export function AUTH(perm: string) {
	const stores = useUserInfo();
	return stores.userInfos.permissions.includes(perm);
}
// 多个权限验证
export function AUTHS(perms: string[]) {
	const stores = useUserInfo();
	return stores.userInfos.permissions.some((item) => perms.includes(item));
}
// 多个权限验证，全部满足
export function AUTH_ALL(perms: string[]) {
	const stores = useUserInfo();
	return judementSameArr(stores.userInfos.permissions, perms);
}
<template>
	<div class="right-container">
		<!-- other stat -->
		<div class="module stat">
			<div class="module-title">
				<span
					><img
						src="/src/assets/sd/data-screen/jiance.png"
						alt=""
						width="33"
						height="23"
						species
					/>监测数据统计
				</span>
			</div>
			<div class="module-cont">
				<div class="num-stat">
					<div class="num-stat-cont num-stat-cont-top">
						<div class="num-stat-item" v-for="item in stat2" :key="item.key">
							<img :src="item.img" alt="" />
							<div>
								<div class="text">{{ item.label }}</div>
								<div class="count">{{ item.count }}</div>
							</div>
						</div>
					</div>
				</div>

				<!-- monitor trend -->
				<div class="monitor-trend">
					<div class="module-title">
						<span
							><img class="device" src="/src/assets/sd/data-screen/device.png" alt="" />发现种类趋势
						</span>
					</div>
					<div class="monitor-trend-cont">
						<Echarts id="monitorTrendChart" ref="monitorTrendChartRef"></Echarts>
					</div>
				</div>
			</div>
		</div>
		<!-- material stat -->
		<div class="module material-stat">
			<div class="module-title">
				<span
					><img class="trend" src="/src/assets/sd/data-screen/trend.png" alt="" />素材数量统计
				</span>
			</div>
			<div class="module-cont">
				<div class="num-stat">
					<div class="num-stat-cont">
						<div class="num-stat-item num-stat-item-3" v-for="item in materialStat" :key="item.key">
							<div class="text">{{ item.label }}</div>
							<div class="count">
								{{ item.key === 'videoDuration' ? item.count + 'h' : item.count }}
							</div>
						</div>
					</div>
				</div>
				<!-- trend -->
				<div class="monitor-trend">
					<div class="module-title">
						<span
							><img class="trend" src="/src/assets/sd/data-screen/trend.png" alt="" />趋势统计
						</span>
					</div>
					<div class="monitor-trend-cont">
						<Echarts id="materialTrendChart" ref="materialTrendChartRef"></Echarts>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import {
	mergeLineOptions,
	ECOption,
	chartLinearColors,
	transDate,
} from '/@/components/echarts/mergeOptions';
import { getMaterialQuantity, getMaterialQuantityTrend } from '/@/api/sd/dataScreen/device';
import { getSpeciesStat, getSpeciesTrend } from '/@/api/sd/dataScreen/bird';
import pinci from '/src/assets/sd/data-screen/pinci.png';
import shuliang from '/src/assets/sd/data-screen/shuliang.png';
import Echarts from '/@/components/echarts/echarts.vue';
type Chart = InstanceType<typeof Echarts>;

const props = defineProps<{
	selectedDevices: string[];
	timeType: number | null;
}>();
// 监测数据统计
const stat2 = ref([
	{
		key: 'speciesFrequency',
		label: '发现物种频次',
		count: 0,
		img: pinci,
	},
	{
		key: 'speciesNum',
		label: '发现物种数量',
		count: 0,
		img: shuliang,
	},
]);
const getStat2 = async () => {
	const query = {
		deviceList: props.selectedDevices,
		timeType: props.timeType,
	};
	const { payload } = await getSpeciesStat(query);
	stat2.value.forEach((item) => {
		item.count = payload[item.key] || 0;
	});
};

// 趋势
const monitorTrendChartRef = ref<Chart>();
const getMonitorTrend = async () => {
	const query = {
		deviceList: props.selectedDevices,
		timeType: props.timeType,
	};
	const { payload } = await getSpeciesTrend(query);
	const chartData = payload.map((et: SpeciesEvent) => {
		const date = transDate(props.timeType, et.date);
		return [date, et.speciesFrequency, et.speciesNum];
	});
	const options: ECOption = {
		dataset: {
			source: chartData,
		},
		color: chartLinearColors.slice(0, 2),
		series: [
			{
				type: 'line',
				name: '发现物种频次',
				symbol: 'circle',
				symbolSize: 6,
				showSymbol: false,
				areaStyle: {
					color: chartLinearColors[3],
				},
			},
			{
				type: 'line',
				name: '发现物种数量',
				symbol: 'circle',
				symbolSize: 6,
				showSymbol: false,
				areaStyle: {
					color: chartLinearColors[2],
				},
			},
		],
	};
	monitorTrendChartRef.value?.resetOption(mergeLineOptions(options));
};

// 素材数量统计
const materialStat = ref([
	{
		key: 'pictureCount',
		label: '采集图片数量',
		count: 0,
	},
	{
		key: 'videoCount',
		label: '采集视频数量',
		count: 0,
	},
	{
		key: 'videoDuration',
		label: '采集视频时长',
		count: 0,
	},
]);
const getMaterialStat = async () => {
	const query = {
		deviceList: props.selectedDevices,
		timeType: props.timeType,
	};
	const { payload } = await getMaterialQuantity(query);
	materialStat.value.forEach((item) => {
		item.count = payload[item.key] || 0;
	});
};
// 趋势
const materialTrendChartRef = ref<Chart>();
const getMaterialTrend = async () => {
	const query = {
		deviceList: props.selectedDevices,
		timeType: props.timeType,
	};
	const { payload } = await getMaterialQuantityTrend(query);
	const chartData = payload.map((material: Material) => {
		const date = transDate(props.timeType, material.date as string);
		return [date, material.totalCount];
	});
	const options: ECOption = {
		dataset: {
			source: chartData,
		},
		color: [chartLinearColors[1]],
		series: [
			{
				type: 'line',
				name: '采集素材数量',
				symbol: 'circle',
				symbolSize: 6,
				showSymbol: false,
				areaStyle: {
					color: chartLinearColors[3],
				},
			},
		],
	};
	materialTrendChartRef.value?.resetOption(mergeLineOptions(options));
};

watch(
	[() => props.selectedDevices, () => props.timeType],
	() => {
		getMonitorTrend();
		getStat2();
		getMaterialTrend();
		getMaterialStat();
	},
	{ immediate: true }
);
</script>

<style lang="scss" scoped>
.right-container .module {
	.num-stat-cont-top {
		padding-bottom: vh(15);
		.num-stat-item {
			background: none;
			text-align: left;
			font-size: vw(24);
			display: flex;
			align-items: center;
			padding: 0;
			img {
				width: vw(60);
				height: vh(60);
				object-fit: cover;
				margin-right: vw(10);
			}
			.text {
				color: #fff;
			}
		}
	}
}
.num-stat {
	.title {
		font-size: vw(16);
		font-family: 'ALiMaMaShuHeiTi';
	}

	.num-stat-cont {
		width: 100%;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		margin: vw(15) 0 vw(3);
		padding-bottom: vh(15);
		.num-stat-item-3 {
			width: vw(120) !important;
		}
		.num-stat-item {
			width: vw(156);
			height: vw(57);
			background: url('/@/assets/sd/data-screen/box-bg.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			margin-bottom: vw(12);
			text-align: center;
			padding-left: vw(6);
			.text {
				color: $sd-screen-primary;
				font-size: vw(12);
				line-height: vw(24);
			}
			.count {
				flex: 1;
				font-size: vw(24);
				font-family: 'ALiMaMaShuHeiTi';
			}
		}
	}
}
.monitor-trend {
	.title {
		font-size: vw(16);
		font-family: 'ALiMaMaShuHeiTi';
	}
	.monitor-trend-cont {
		padding-top: vh(15);
		height: vh(260);
	}
}
.recent-monitor .species {
	max-height: vw(364) !important;
}
</style>

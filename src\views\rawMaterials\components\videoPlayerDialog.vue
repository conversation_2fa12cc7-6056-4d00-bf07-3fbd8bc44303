<template>
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		:width="dialogStyle.dialogWidth"
		align-center
    @close="closeDialog"
	>
		<video
      v-if="state.videoType === 'video'"
			ref="videoRef"
			controls
			:style="{ height: dialogStyle.videoHigth }"
			style="width: 100%; object-fit: contain; background: #000"
		></video>

    <div v-else :style="{ height: dialogStyle.videoHigth }">
      <Player :playUrl="flvPlayUrl"></Player>
    </div>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, computed } from 'vue';

// 自定义jessibuca播放组件
import Player from '/@/components/player/jessibuca.vue';

const state = reactive({
	dialog: {
		isShowDialog: false,
		title: '视频播放',
	},
  videoType: '',
});
const dialogStyle = computed(() => {
	return {
		dialogWidth: window.innerWidth < 1500 ? 833 : 1000,
		videoHigth: window.innerWidth < 1500 ? '500px' : '600px',
	};
});

const openDialog = (data: { type: string, playUrl: string }) => {
  state.videoType = data.type;
  state.dialog.isShowDialog = true;

  if (data.type === 'video') {
    videoPlay(data.playUrl);
  } else if (data.type === 'flv') {
    flvPlay(data.playUrl);
  }
};

// video/*地址播放
const videoRef = ref();
const videoPlay = (url: string) => {
  nextTick(() => {
    videoRef.value.src = url;
    videoRef.value.load();
    videoRef.value.play();
  });
};

// flv流地址播放
const flvPlayUrl = ref('');
const flvPlay = (url: string) => {
  nextTick(() => {
    flvPlayUrl.value = url;
  });
};

const closeDialog = () => {
  // 销毁播放器
  flvPlayUrl.value = '';
};

defineExpose({
	openDialog,
});
</script>

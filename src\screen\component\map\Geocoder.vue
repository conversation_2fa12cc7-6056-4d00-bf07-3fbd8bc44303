<template>
	<div
		class="location-search"
		:class="{ r10: isCollapsed, expanded: isExpanded }"
		@mouseenter="handleMouseEnter"
	>
		<div class="location-search-toggle" @click="toggleGeoType">
			<img :src="ToggleIcon" alt="" />
			&nbsp;{{ geoText }}
		</div>
		<div class="location-search-input">
			<el-input
				ref="inputRef"
				v-model.trim="searchValue"
				:placeholder="searchPlaceholder"
				size="large"
				clearable
				@keyup.enter="debounceSearch"
				@keyup.esc="closeSearchBox"
			/>
		</div>
		<div class="location-search-button" @click="toggleSearchBox">
			<img v-if="!isSearching" :src="SearchIcon" alt="" />
			<el-icon v-else class="is-loading"><Loading /></el-icon>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch, onMounted, onUnmounted } from 'vue';
// import { ElAmap } from '@vuemap/vue-amap';
import { ElMessage } from 'element-plus';
import { Loading } from '@element-plus/icons-vue';
import ToggleIcon from '/@/assets/hlj/map/toggle.png';
import SearchIcon from '/@/assets/hlj/map/map-btn-search.png';
import type { AddressLevel, GeocoderResult } from '/@/types/amap';

const props = defineProps({
	geocoder: {
		type: Object as () => AMap.Geocoder,
		required: true,
	},
	isCollapsed: {
		type: Boolean,
		default: false,
	},
});

const emits = defineEmits(['resetMapCenter']);

// 地理编码，正向地理编码 getLocation，逆向地理编码 getAddress
enum GeoType {
	LOC = 'getLocation',
	ADDRESS = 'getAddress',
}

// 从本地存储读取上次使用的搜索类型
const getStoredGeoType = (): GeoType => {
	try {
		const storedType = localStorage.getItem('hlj_map_geo_type');
		return storedType && (storedType === GeoType.LOC || storedType === GeoType.ADDRESS)
			? storedType
			: GeoType.LOC;
	} catch (e) {
		return GeoType.LOC;
	}
};

// 状态管理
const geoType = ref<GeoType>(getStoredGeoType());
const isExpanded = ref(false);
const isSearching = ref(false);
const searchValue = ref('');
const inputRef = ref<any>(null);
// 添加一个防误触保护状态
const isJustExpanded = ref(false);
let expandProtectionTimer: ReturnType<typeof setTimeout> | null = null;

// 计算属性
const geoText = computed(() => {
	// 输入框提示信息
	return geoType.value === GeoType.LOC ? '搜索位置' : '搜索经纬度';
});
const searchPlaceholder = computed(() => {
	// 输入框提示信息
	return geoType.value === GeoType.LOC ? '搜索详细地址' : '搜索经纬度（116.34,34.5）';
});

// 坐标格式正则表达式
const COORD_REGEX = /^-?(1[0-7]\d|\d{1,2})(\.\d+)?,-?([1-8]?\d)(\.\d+)?$/;
const zoomMap: Record<AddressLevel, number> = {
	国家: 5,
	省: 7,
	城市: 10,
	区县: 13,
	乡镇: 15,
	村庄: 16,
	道路: 16,
	门牌号: 17,
};

// 防抖实现
let searchTimeout: ReturnType<typeof setTimeout> | null = null;
const debounceSearch = () => {
	if (searchTimeout) {
		clearTimeout(searchTimeout);
	}
	searchTimeout = setTimeout(() => {
		getLocationInfo();
	}, 300);
};

// 鼠标进入处理 - 立即展开
const handleMouseEnter = () => {
	// 立即展开，不再延迟
	if (!isExpanded.value) {
		expandSearchBox();
	}
};

// 切换地理编码类型
const toggleGeoType = () => {
	if (geoType.value === GeoType.LOC) {
		geoType.value = GeoType.ADDRESS;
	} else {
		geoType.value = GeoType.LOC;
	}

	// 保存搜索类型到本地存储
	try {
		localStorage.setItem('hlj_map_geo_type', geoType.value);
	} catch (e) {
		console.warn('无法保存搜索类型到本地存储', e);
	}
};

// 设置防误触保护
const setExpandProtection = () => {
	isJustExpanded.value = true;

	// 清除之前的计时器
	if (expandProtectionTimer) {
		clearTimeout(expandProtectionTimer);
	}

	// 设置新的计时器，600ms后取消保护
	expandProtectionTimer = setTimeout(() => {
		isJustExpanded.value = false;
	}, 600);
};

// 展开搜索框
const expandSearchBox = () => {
	if (isExpanded.value) return;

	isExpanded.value = true;
	// 设置防误触保护
	setExpandProtection();

	nextTick(() => {
		inputRef.value.focus();
	});
};

// 关闭搜索框
const closeSearchBox = () => {
	// 如果处于防误触保护状态，则不关闭
	if (isJustExpanded.value) return;
	isExpanded.value = false;
};

// 切换搜索框展开/收起状态
const toggleSearchBox = (event: MouseEvent) => {
	// 阻止事件冒泡，避免触发其他点击事件
	event.stopPropagation();

	// 如果刚刚展开，则忽略此次点击，防止误触
	if (isJustExpanded.value) {
		console.log('防误触保护生效，忽略点击');
		return;
	}

	if (!isExpanded.value) {
		// 如果当前是收起状态，则展开
		console.log('展开搜索框');
		expandSearchBox();
	} else if (searchValue.value) {
		// 如果已展开且有输入内容，则执行搜索
		console.log('执行搜索');
		debounceSearch();
	} else {
		// 如果已展开但没有内容，则收起
		console.log('收起搜索框');
		isExpanded.value = false;
	}
};

// 点击文档其他地方时折叠搜索框
const handleClickOutside = (event: MouseEvent) => {
	const el = document.querySelector('.location-search');
	if (el && !el.contains(event.target as Node) && isExpanded.value) {
		// 如果处于防误触保护状态，则不关闭
		if (isJustExpanded.value) return;
		isExpanded.value = false;
	}
};

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
	if (event.key === 'Escape' && isExpanded.value) {
		closeSearchBox();
	}
};

// 监听事件
onMounted(() => {
	document.addEventListener('keydown', handleKeyDown);
});

// 监听点击事件
watch(
	() => isExpanded.value,
	(newVal) => {
		if (newVal) {
			// 添加点击外部关闭事件
			setTimeout(() => {
				document.addEventListener('click', handleClickOutside);
			}, 0);
		} else {
			// 移除事件监听
			document.removeEventListener('click', handleClickOutside);
		}
	}
);

// 组件卸载时移除事件监听
onUnmounted(() => {
	document.removeEventListener('click', handleClickOutside);
	document.removeEventListener('keydown', handleKeyDown);
	if (searchTimeout) {
		clearTimeout(searchTimeout);
	}
	if (expandProtectionTimer) {
		clearTimeout(expandProtectionTimer);
	}
});

// 执行地址搜索
const searchLocation = async (address: string): Promise<void> => {
	return new Promise((resolve, reject) => {
		if (!props.geocoder) {
			reject(new Error('地理编码服务未初始化'));
			return;
		}

		// 设置超时处理，防止API不响应
		const timeoutId = setTimeout(() => {
			reject(new Error('搜索请求超时'));
		}, 2000);

		props.geocoder.getLocation(address, function (status: string, result: GeocoderResult) {
			clearTimeout(timeoutId);
			console.log('位置信息', result);
			if (status === 'complete' && result.info === 'OK') {
				if (!result.geocodes || result.geocodes.length === 0) {
					ElMessage.warning('未找到匹配的位置信息');
					reject(new Error('未找到匹配的位置信息'));
					return;
				}

				const address = result.geocodes[0];
				emits(
					'resetMapCenter',
					[address.location.getLng(), address.location.getLat()],
					zoomMap[address.level] || 15
				);
				resolve();
			} else {
				const errorMsg = '位置信息获取失败: ' + (result.info || '未知错误');
				ElMessage.error(errorMsg);
				reject(new Error(errorMsg));
			}
		});
	});
};

// 执行坐标搜索
const searchCoordinate = async (coord: string[]): Promise<void> => {
	return new Promise((resolve, reject) => {
		if (!props.geocoder) {
			reject(new Error('地理编码服务未初始化'));
			return;
		}

		// 设置超时处理，防止API不响应
		const timeoutId = setTimeout(() => {
			reject(new Error('搜索请求超时'));
		}, 2000);

		props.geocoder.getAddress(coord, function (status: string, result: any) {
			clearTimeout(timeoutId);
			console.log('位置信息', result);
			if (status === 'complete' && result.info === 'OK') {
				if (!result.regeocode || !result.regeocode.formattedAddress) {
					ElMessage.warning('未找到匹配的地址信息');
					reject(new Error('未找到匹配的地址信息'));
					return;
				}

				emits('resetMapCenter', coord);
				ElMessage.success(`已定位到: ${result.regeocode.formattedAddress}`);
				resolve();
			} else {
				const errorMsg = '位置信息获取失败: ' + (result.info || '未知错误');
				ElMessage.error(errorMsg);
				reject(new Error(errorMsg));
			}
		});
	});
};

// 搜索位置信息
const getLocationInfo = async () => {
	if (!searchValue.value) {
		ElMessage.warning('请输入搜索内容');
		return;
	}

	// 设置加载状态
	isSearching.value = true;

	try {
		if (geoType.value === GeoType.LOC) {
			await searchLocation(searchValue.value);
			console.log('搜索成功');
		} else {
			// 解析经纬度
			if (!COORD_REGEX.test(searchValue.value)) {
				ElMessage.error('坐标格式不正确，请使用经度,纬度格式（如：116.34,34.5）');
				isSearching.value = false;
				return;
			}
			const coord = searchValue.value.split(',');
			await searchCoordinate(coord);
		}
	} catch (error) {
		console.error('搜索失败:', error);
		ElMessage.error('搜索失败');
	} finally {
		// 无论成功还是失败，都取消加载状态
		isSearching.value = false;
	}
};
</script>

<style lang="scss" scoped>
$baseSize: 40;
$btnSize: 44;
$shadowSize: 4;
$inputSize: 195;

.location-search {
	width: vw($btnSize);
	height: vw($btnSize);
	position: absolute;
	bottom: vw(20);
	right: vw(400 + 20);
	cursor: pointer;
	overflow: hidden;
	transition: all 0.2s ease-in-out;

	.location-search-toggle {
		width: vw(106);
		height: vw($baseSize);
		position: absolute;
		right: vw($baseSize + $shadowSize / 2 + $inputSize);
		bottom: vw($shadowSize / 2);
		display: flex;
		align-items: center;
		justify-content: center;
		background: rgba(33, 227, 221, 0.1);
		cursor: pointer;
		opacity: 0.9;
		img {
			width: vw(14);
			vertical-align: middle;
		}
		&:hover {
			opacity: 1;
		}
	}
	.location-search-input {
		width: vw($inputSize);
		position: absolute;
		bottom: vw($shadowSize / 2);
		right: vw($btnSize);
	}
	.location-search-button {
		width: vw($btnSize);
		height: vw($btnSize);
		position: absolute;
		bottom: 0;
		right: 0;
		background: url('../../../../assets/hlj/map/map-btn-bg.png');
		background-size: 100% 100%;
		background-position: 0 0;
		opacity: 0.9;
		display: flex;
		align-items: center;
		justify-content: center;
		img {
			width: 40%;
		}
		.el-icon {
			color: #fff;
			font-size: vw(20);
		}
		&:hover {
			opacity: 1;
		}
	}

	&.expanded {
		width: vw($baseSize + $shadowSize / 2 + $inputSize + 106);
		box-shadow: 0 0 0 1px rgba(33, 227, 221, 0.1) inset;
	}
}
.location-search.r10 {
	right: vw(10) !important;
}

:deep(.location-search-input .el-input) {
	.el-input__wrapper {
		height: vw($baseSize);
		background: rgba(9, 23, 32, 0.8);
		box-shadow: 0 0 0 1px rgba(33, 227, 221, 0.1) inset;
		border-radius: 0;
		.el-input__inner {
			color: #fff;
		}
	}
}
</style>

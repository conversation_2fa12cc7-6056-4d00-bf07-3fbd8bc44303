<template>
	<el-dialog
		:title="state.dialog.title"
		v-model="state.dialog.isShowDialog"
		:show-close="false"
		align-center
		class="marker-window marker-window-video"
		modal-class="marker-window-modal marker-window-modal-video"
		@close="onCancelDialog"
		destroy-on-close
		:style="markerWindowStyle"
	>
		<div class="module map-custom-info-window">
			<div class="module-title">实时视频</div>
			<div class="icon-close" @click="onCancelDialog">
				<img src="/src/assets/sd/data-screen/close.png" alt="" />
			</div>
			<div class="info-window-cont">
				<!-- 使用`el-amap-info-window`展示实时视频窗口时，播放器切换全屏失效 -->
				<Player :playUrl="state.devicePlayUrl"></Player>
			</div>
		</div>
	</el-dialog>
</template>

<script setup lang="ts">
import { reactive, nextTick, computed } from 'vue';
import Player from '/@/components/player/jessibuca.vue';

const state = reactive({
	dialog: {
		title: '',
		isShowDialog: false,
	},
	position: {
		x: 0,
		y: 0,
	},
	devicePlayUrl: '',
	markerClickCallback: null as Function | null,
});

const markerWindowStyle = computed(() => {
	return {
		left: state.position.x + 'px',
		top: state.position.y + 'px',
	};
});

interface OpenDialogParams {
	playUrl: string;
	eventX: number;
	eventY: number;
}
const openDialog = (params: OpenDialogParams, callback: Function) => {
	state.dialog.isShowDialog = true;
	state.position.x = params.eventX;
	state.position.y = params.eventY;
	state.markerClickCallback = callback;
	nextTick(() => {
		state.devicePlayUrl = params.playUrl;
	});
};

const onCancelDialog = () => {
	state.dialog.isShowDialog = false;
	// 销毁播放器
	state.devicePlayUrl = '';
	if (typeof state.markerClickCallback === 'function') {
		state.markerClickCallback(state);
		state.markerClickCallback = null;
	}
};

defineExpose({
	openDialog,
});
</script>
<style lang="scss">
.marker-window-modal {
	.marker-window {
		width: vw(850) !important;
	}
	.info-window-cont {
		aspect-ratio: 16/9 !important;
		height: auto !important;
	}
}
</style>

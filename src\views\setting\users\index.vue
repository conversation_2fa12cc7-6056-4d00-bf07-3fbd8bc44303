<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<el-form-item label="账号">
					<el-input v-model="searchOptions.filter.username" placeholder="请输入账号" clearable>
						<template #suffix>
							<el-icon><ele-Search /></el-icon>
						</template>
					</el-input>
				</el-form-item>
				<el-form-item label="用户名">
					<el-input v-model="searchOptions.filter.name" placeholder="请输入用户名" clearable>
						<template #suffix>
							<el-icon><ele-Search /></el-icon>
						</template>
					</el-input>
				</el-form-item>
			</template>

			<template #searchBtns>
				<el-button type="primary" @click="onCreate">
					<template #icon>
						<el-icon><ele-Plus /></el-icon>
					</template>
					新增
				</el-button>
				<!-- <el-button type="success" :disabled="tableRef?.selectRows.length !== 1" @click="onUpdateRow(null)">
          <template #icon>
            <el-icon><ele-Edit /></el-icon>
          </template>
          修改
        </el-button> -->
				<!-- <el-button type="danger" :disabled="tableRef?.selectRows.length === 0" @click="onBatchDelete">
          <template #icon>
            <el-icon><ele-Delete /></el-icon>
          </template>
          删除
        </el-button> -->
			</template>
		</ViewSearch>

		<Table v-bind="tableOptions" ref="tableRef" @pageChange="onPageChange">
			<template #role="{ row }">
				{{ row.role ? row.role.name : '-' }}
			</template>
			<template #operate="{ row }">
				<el-button size="small" text type="primary" @click="onUpdateRow(row)">
					<el-icon><ele-EditPen /></el-icon>
					修改
				</el-button>
				<el-button size="small" text type="primary" @click="onResetPassword(row.id)">
					<el-icon><ele-Key /></el-icon>
					重置密码
				</el-button>
				<!-- <el-button size="small" text type="primary" @click="onDelRow(row.id)">
          <el-icon><ele-Delete /></el-icon>
          删除
        </el-button> -->
			</template>
		</Table>
		<CreateDialog ref="createDialogRef" @refresh="onRefresh"></CreateDialog>
		<ChangePasswordDialog ref="changePasswordDialog"></ChangePasswordDialog>
	</div>
</template>

<script setup lang="ts" name="Users">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { getUsers } from '/@/api/users';

// 引入异步组件
const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));
const CreateDialog = defineAsyncComponent(() => import('./dialog.vue'));
const ChangePasswordDialog = defineAsyncComponent(() => import('./changePassDialog.vue'));

const tableRef = ref<InstanceType<typeof Table>>();
const searchOptions = reactive({
	filter: {
		username: '',
		name: '',
		userType: '',
		sort: 'createTime,desc',
	},
});
const tableOptions: GlobalTableOptions<UserRow> = reactive({
	data: [],
	header: [
		{
			title: '账号',
			key: 'username',
			isCheck: true,
		},
		{
			title: '用户名',
			key: 'name',
			isCheck: true,
		},
		{
			title: '角色',
			key: 'role',
			isCheck: true,
		},
		// {
		//   title: '状态',
		//   key: 'versionName',
		//   isCheck: true,
		// },
		{
			title: '创建时间',
			key: 'createTime',
			isCheck: true,
		},
	],
	config: {
		loading: true,
		isSelection: false,
		isSerialNo: true,
		isOperate: true, // 是否显示操作列
		operateWidth: 150, // 操作列宽
		total: 0, // 总条数
	},
	pageParams: {
		page: 1,
		size: 10,
	},
});

onMounted(() => {
	initTableData();
});

const initTableData = async () => {
	tableOptions.config.loading = true;
	try {
		const query: any = {
			page: tableOptions.pageParams?.page && tableOptions.pageParams.page - 1,
			size: tableOptions.pageParams?.size,
			...searchOptions.filter,
		};
		const { payload } = await getUsers(query);
		tableOptions.config.loading = false;
		tableOptions.data = payload.content;
		tableOptions.config.total = payload.totalElements;
	} catch (e) {
		tableOptions.config.loading = false;
		tableOptions.config.total = 0;
		tableOptions.data = [];
	}
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetFilter) {
		searchOptions.filter.username = '';
		searchOptions.filter.name = '';
		searchOptions.filter.userType = '';
	}
	if (resetPage) {
		tableRef.value?.pageReset();
	} else {
		initTableData();
	}
	tableRef.value?.clearSelection();
};

const onPageChange = async (page: { pageNum: number; pageSize: number }) => {
	if (tableOptions.pageParams) {
		tableOptions.pageParams.page = page.pageNum;
		tableOptions.pageParams.size = page.pageSize;
	}
	initTableData();
};

// const onBatchDelete = () => {
//   ElMessageBox({
//     title: '提示',
//     message: '此操作将永久删除，是否继续?',
//     type: 'warning',
//     showCancelButton: true,
//   }).then(async () => {
//     const ids = tableRef?.value?.selectRows.map((item) => item.id) as string[];
//     await batchDeleteAiModel(ids);
//     ElMessage.success('删除成功');
//     onRefresh();
//   })
//   .catch(() => {})
// }

// const onDelRow = (rowId: string) => {
//   ElMessageBox({
//     title: '提示',
//     message: '此操作将永久删除，是否继续?',
//     type: 'warning',
//     showCancelButton: true,
//   }).then(async () => {
//     await deleteAiModel(rowId);
//     ElMessage.success('删除成功');
//     onRefresh();
//   })
//   .catch(() => {})
// };

const createDialogRef = ref<InstanceType<typeof CreateDialog>>();
const onCreate = () => {
	createDialogRef.value?.openDialog();
};
const onUpdateRow = (row: UserRow | null) => {
	if (!row) {
		row = tableOptions.data.find(
			(item) => item.id === tableRef?.value?.selectRows[0].id
		) as UserRow;
	}
	createDialogRef.value?.openDialog(row);
};
// 重置密码
const changePasswordDialog = ref<InstanceType<typeof ChangePasswordDialog>>();
const onResetPassword = (rowId: string) => {
	changePasswordDialog.value?.openDialog(rowId);
};
</script>

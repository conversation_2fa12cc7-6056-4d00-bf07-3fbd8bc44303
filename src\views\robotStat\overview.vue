<template>
	<div class="overview-stat-container">
		<div class="top">
			<div class="robot-overview">
				<div class="robot-overview-box">
					<span class="workin"
						><em>工作中</em><i>{{ robotInfo.patrolCount }}</i></span
					>
					<span class="charging"
						><em>充电中</em><i>{{ robotInfo.chargingCount }}</i></span
					>
					<span class="shutdown"
						><em>离线</em><i>{{ robotInfo.offlineCount }}</i></span
					>
					<span class="standby">
						<em>待机</em><i>{{ robotInfo.standbyCount }}</i></span
					>
					<span class="robot-count">{{ robotInfo.totalCount }}</span>
				</div>
			</div>
			<div class="task-overview">
				<div class="task-overview-item" v-for="task in taskOverviewData" :key="task.iconClass">
					<span clas :class="task.iconClass"></span>
					<div class="task-overview-item-right">
						<img :src="task.titleUrl" class="title-w-h" alt="" />
						<span class="task-overview-item-value">
							{{ task.count }}
							<i>{{ task.unit }}</i>
						</span>
					</div>
				</div>
			</div>
		</div>
		<div class="event-overview">
			<div class="event-overview-item" v-for="event in eventOverviewData" :key="event.boxClass">
				<img :src="event.titleUrl" :width="event.titleWidth" class="title" alt="" />
				<div class="event-overview-item-bottom">
					<div class="event-overview-item-bottom-left" :class="event.boxClass">
						<span class="event-overview-item-label">{{ event.label }}</span>
						<span class="event-overview-item-value">{{ event.count }}</span>
					</div>
					<div class="event-overview-item-bottom-right">
						<Echarts class="echarts" ref="chartRefs" :id="`chartId${event.boxClass}`" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import other from '/@/utils/other';
import { ref, reactive, onMounted, nextTick } from 'vue';
import Echarts from '/@/components/echarts/echarts.vue';
import { getOverview } from '/@/api/robotStat';

type RInfo = {
	totalCount: number;
	offlineCount: number;
	chargingCount: number;
	standbyCount: number;
	patrolCount: number;
};

const robotInfo = ref<RInfo>({
	totalCount: 0,
	offlineCount: 0,
	chargingCount: 0,
	standbyCount: 0,
	patrolCount: 0,
});
const taskOverviewData = reactive([
	{
		iconClass: 'task-overview-item-count',
		titleUrl: other.getStaticImag('fullScreen/robotStat/overview/renwu-cishu.png'),
		count: '0',
		unit: '个',
	},
	{
		iconClass: 'task-overview-item-mileage',
		titleUrl: other.getStaticImag('fullScreen/robotStat/overview/renwu-licheng.png'),
		count: '0',
		unit: 'km',
	},
	{
		iconClass: 'task-overview-item-time',
		titleUrl: other.getStaticImag('fullScreen/robotStat/overview/renwu-yongshi.png'),
		count: '0',
		unit: 'h',
	},
]);

const eventOverviewData = reactive<{ [key: string]: any }>({
	point_overview: {
		boxClass: 'event-overview-item-renwu',
		titleUrl: other.getStaticImag('fullScreen/robotStat/overview/dingdian-xunjian.png'),
		titleWidth: 100,
		label: '定点任务告警总数',
		count: 0,
		option: {},
	},
	species_overview: {
		boxClass: 'event-overview-item-duoyangxing',
		titleUrl: other.getStaticImag('fullScreen/robotStat/overview/duoyangxing.png'),
		titleWidth: 116,
		label: '生物多样性检测',
		count: 0,
		option: {},
	},
	security_overview: {
		boxClass: 'event-overview-item-anfang',
		titleUrl: other.getStaticImag('fullScreen/robotStat/overview/anfang.png'),
		titleWidth: 100,
		label: '公园安防告警总数',
		count: 0,
		option: {},
	},
	incivility_overview: {
		boxClass: 'event-overview-item-buwenming',
		titleUrl: other.getStaticImag('fullScreen/robotStat/overview/buwenming.png'),
		titleWidth: 116,
		label: '不文明行为监测告警总数',
		count: 0,
		option: {},
	},
});
type Chart = InstanceType<typeof Echarts>;
const chartRefs = ref<Chart[]>();
interface Statistics {
	count: number;
	name: string;
	percent?: number;
	value: number;
	eventLevel: number;
	color: string;
	eventType: number;
}

const seriesColors: EmptyObjectType = {
	0: '#22D69F',
	1: '#F8CE6B',
	2: '#114CFF',
	3: '#9123C8',
	4: '#FF6000',
	5: '#0BBDFF',

	6: '#fd8136',
	7: '#a23bff',
	8: '#4ac6fb',
	9: '#f8ce6b',
	11: '#22d69f',
	16: '#eb49f9',
	13: '#ff1515',

	10: '#fd8136',
	15: '#a23bff',
	17: '#4ac6fb',
};

const onGetOverview = async () => {
	const { payload } = await getOverview();
	// payload.point_overview.eventOverviewData.point_overview;

	robotInfo.value = payload.robot_overview;
	taskOverviewData[0].count = payload.task_overview.totalTaskCount;
	taskOverviewData[1].count = (payload.task_overview.totalMileage / 1000).toFixed(1);
	taskOverviewData[2].count = (payload.task_overview.totalTime / 60 / 60).toFixed(1);
	Object.keys(eventOverviewData).forEach((key, index) => {
		if (payload[key]) {
			eventOverviewData[key].count = payload[key].count;
			nextTick(() => {
				renderCharts(index, handleOverviewStat(payload[key].subEventStatistics));
			});
		}
	});
	console.log(eventOverviewData);
};
onGetOverview();
// 概览统计
const handleOverviewStat = (data: any) => {
	const icon = {
		align: 'left',
		width: 11,
		height: 11,
	};
	const total = data.reduce(
		(accumulator: any, item: { count: number }) => accumulator + item.count,
		0
	);
	const allDatalegend: string[] = [];
	const allData = data.map((item: Statistics) => {
		// const name = item.name.replace(/告警|识别$/, '').slice(0, 5);
		allDatalegend.push(item.name);
		return {
			...item,
			name: item.name,
			percent: Number(((item.count / total) * 100).toFixed(2)),
			value: item.count,
			itemStyle: {
				color: seriesColors[item.eventType],
			},
		};
	});

	return {
		backgroundColor: 'transparent',
		tooltip: {
			trigger: 'item',
			formatter: (params: any) => {
				return `<div>${params.name}：${params.percent}%</div>`;
			},
			textStyle: {
				color: '#fff',
			},
			backgroundColor: 'rgba(50,50,50,0.7)',
			borderColor: 'rgba(104, 207, 173, 1)',
		},
		legend: {
			show: true,
			type: 'scroll',
			orient: 'vertical',
			right: 0,
			padding: [30, 0, 0, 0],
			top: 'center',
			itemGap: 8,
			icon: 'none',
			pageIconColor: '#67cfac',
			pageIconSize: [12, 12],
			pageTextStyle: {
				color: '#fff',
			},
			textStyle: {
				color: '#fff',
				rich: {
					0: {
						...icon,
						backgroundColor: seriesColors[0], // 设置线的颜色
					},
					1: {
						...icon,
						backgroundColor: seriesColors[1], // 设置线的颜色
					},
					2: {
						...icon,
						backgroundColor: seriesColors[2], // 设置线的颜色
					},
					3: {
						...icon,
						backgroundColor: seriesColors[3], // 设置线的颜色
					},
					4: {
						...icon,
						backgroundColor: seriesColors[4], // 设置线的颜色
					},
					5: {
						...icon,
						backgroundColor: seriesColors[5], // 设置线的颜色
					},
					6: {
						...icon,
						backgroundColor: seriesColors[6], // 设置线的颜色
					},
					7: {
						...icon,
						backgroundColor: seriesColors[7], // 设置线的颜色
					},
					8: {
						...icon,
						backgroundColor: seriesColors[8], // 设置线的颜色
					},
					9: {
						...icon,
						backgroundColor: seriesColors[9], // 设置线的颜色
					},
					10: {
						...icon,
						backgroundColor: seriesColors[10], // 设置线的颜色
					},
					11: {
						...icon,
						backgroundColor: seriesColors[11], // 设置线的颜色
					},
					13: {
						...icon,
						backgroundColor: seriesColors[13], // 设置线的颜色
					},
					15: {
						...icon,
						backgroundColor: seriesColors[15], // 设置线的颜色
					},
					16: {
						...icon,
						backgroundColor: seriesColors[16], // 设置线的颜色
					},
					17: {
						...icon,
						backgroundColor: seriesColors[17], // 设置线的颜色
					},
					uname: {
						align: 'left',
						width: 80,
						verticalAlign: 'bottom',
					},
					unum: {
						width: 50,
						align: 'center',
						verticalAlign: 'middle',
					},
					middle: {
						width: 130,
						height: 1,
						backgroundColor: 'rgba(255, 255, 255, .2)',
						verticalAlign: 'middle',
					},
					square: {
						width: 8,
						height: 8,
						backgroundColor: 'rgba(255, 255, 255, .2)',
						borderWidth: 0,
						verticalAlign: 'middle',
					},
				},
			},
			data: allDatalegend,
			formatter(name: string) {
				const nameItem = allData.find((item: Statistics) => item.name === name);
				if (nameItem) {
					return `{${nameItem.eventType}|} {uname|${name}}{unum|${nameItem.percent}%}\n{middle|}{square|}`;
				}
			},
		},
		series: [
			{
				name: '统计信息',
				type: 'pie',
				right: 140,
				radius: ['34%', '45%'],
				color: seriesColors,
				label: {
					show: true,
					color: '#fff',
					position: 'outside',
					formatter: (params: any) => {
						return `{green|${params.data.percent}%}\n${params.name}`;
					},
					rich: {
						green: {
							padding: [3, 0],
							color: '#67cfac',
						},
					},
				},
				labelLine: {
					length: 9,
					length2: 5,
					showAbove: true,
				},
				data: allData,
			},
			{
				name: '内部虚线',
				type: 'pie',
				silent: true, // 不响应和触发鼠标事件
				right: 140,
				radius: ['27%', '28%'],
				color: ['#606664'],
				labelline: {
					show: false,
				},
				label: {
					show: false,
				},
				itemStyle: {
					normal: {
						color: (a: { data: number }) => {
							if (a.data == 1) {
								return '#606664';
							}
							if (a.data == 1) {
								return 'transparent';
							}
						},
					},
				},
				data: [
					2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1,
					2, 1,
				],
			},
			{
				name: '内部实线',
				type: 'pie',
				silent: true,
				right: 140,
				radius: ['6%', '7%'],
				color: ['#67cfac'],
				labelline: {
					show: false,
				},
				label: {
					show: false,
				},
				data: [{ value: 222, name: '内部实线' }],
			},
		],
	};
};

const renderCharts = ($tIndex: number, _option: typeof handleOverviewStat) => {
	(<Chart[]>chartRefs.value)[$tIndex].resetOption(_option);
};
onMounted(async () => {});
</script>

<style lang="scss" scoped>
/* @import url(); 引入css类 */

%bg-r-s {
	background-repeat: no-repeat;
	background-size: 100% 100%;
}
%padding {
	padding: vh(45) vw(20) 0;
}

%fs-14 {
	font-size: 14px;
}
.overview-stat-container {
	height: 100%;

	.top {
		height: vh(278);
		margin-bottom: vh(20);
		display: flex;
		.robot-overview {
			position: relative;
			width: vw(486);
			height: 100%;
			margin-right: vw(20);

			@extend %bg-r-s;
			padding: vh(80) vw(50) vh(35);
			background-image: url('/src//assets/fullScreen/robotStat/overview/robot-overview-bg.png');

			&-box {
				position: relative;
				height: 100%;
				@extend %bg-r-s;
				background-image: url('/src//assets/fullScreen/robotStat/overview/robot-overview-mian.png');
				span {
					position: absolute;
					@extend %fs-14;
					display: flex;
					align-items: center;
					em {
						width: 42px;
						font-style: normal;
						margin-right: vw(16);
					}
					i {
						font-style: normal;
					}
				}
				.robot-count {
					left: 50%;
					top: 50%;
					transform: translate(-50%, 25%);
				}
				.workin {
					left: vw(26);
					top: vh(17);
					i {
						color: #4fbbef;
					}
				}
				.charging {
					left: vw(302);
					top: vh(17);
					i {
						color: #68cfad;
					}
				}
				.shutdown {
					left: vw(26);
					top: vh(124);
					i {
						color: #d8d8d8;
					}
				}
				.standby {
					left: vw(302);
					top: vh(124);
					i {
						color: #fee963;
					}
				}
			}
		}
		.task-overview {
			flex: 1;
			@extend %bg-r-s;
			@extend %padding;
			background-image: url('/src//assets/fullScreen/robotStat/overview/task-overview-bg.png');
			display: flex;
			align-items: center;
			height: 100%;

			&-item {
				flex: 1;
				display: flex;
				height: vh(133);
				span {
					width: vw(173);
					height: 100%;
					@extend %bg-r-s;
					margin-right: vw(17);
				}

				&-value {
					width: vw(96);
					height: vh(56) !important;
					color: rgba(255, 255, 255, 1);
					font-size: 40px;
					margin-top: vh(5);
					i {
						font-style: normal;
						font-size: 14px;
						color: rgb(152, 247, 215);
					}
				}

				&-right {
					flex: 1;
					height: 100%;
					display: flex;
					flex-direction: column;
					justify-content: center;

					.title-w-h {
						width: vw(83);
						height: vh(23);
						object-fit: contain;
					}
				}
				&-count {
					background-image: url('/src//assets/fullScreen/robotStat/overview/task-count-icon.png');
				}
				&-mileage {
					background-image: url('/src//assets/fullScreen/robotStat/overview/task-mileage-icon.png');
				}
				&-time {
					background-image: url('/src//assets/fullScreen/robotStat/overview/task-time-icon.png');
				}
			}
		}
	}
	.event-overview {
		width: 100%;
		height: vh(568);
		@extend %bg-r-s;
		@extend %padding;
		background-image: url('../../assets/fullScreen/robotStat/overview/event-overview-bg.png');
		display: flex;
		flex-wrap: wrap;
		&-item {
			width: 50%;
			height: 50%;
			.title {
				height: vh(23);
				margin-top: vh(25);
				margin-bottom: vh(18);
				object-fit: contain;
			}
			&-bottom {
				height: calc(100% - vh(23) - vh(43));
				display: flex;
				align-items: center;
				&-left {
					width: vw(355);
					height: vh(130);
					padding-left: vw(144);
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					.event-overview-item-label {
						height: vh(17);
						line-height: vh(17);
						font-size: 12px;
						margin-bottom: vh(9);
					}
					.event-overview-item-value {
						height: vh(56);
						font-size: 40px;
						text-align: center;
					}
				}
				&-right {
					width: calc(100% - vw(355));
					height: 100%;
					padding: 0 40px;
				}
			}
			&-renwu {
				@extend %bg-r-s;
				background-image: url('../../assets/fullScreen/robotStat/overview/event-xunjian-bg.png');
			}

			&-duoyangxing {
				@extend %bg-r-s;
				background-image: url('../../assets/fullScreen/robotStat/overview/event-duoyangxing-bg.png');
			}
			&-anfang {
				@extend %bg-r-s;
				background-image: url('../../assets/fullScreen/robotStat/overview/event-anfang-bg.png');
			}
			&-buwenming {
				@extend %bg-r-s;
				background-image: url('../../assets/fullScreen/robotStat/overview/event-buwenming-bg.png');
			}
		}
	}
}
</style>

<template>
	<i v-if="isShowElIcon" class="el-icon" :style="setIconStyle">
		<component :is="getIconName" />
	</i>
	<span v-else-if="isShowSvgIconImg" :class="svgIconClassName" :style="setSvgIconStyle"></span>
	<i v-else :class="getIconName" :style="setIconStyle" />
</template>

<script setup lang="ts" name="svgIcon">
import { computed, watch, nextTick } from 'vue';

// 定义父组件传过来的值
const props = defineProps({
	// svg 图标组件名字
	name: {
		type: String,
	},
	// svg 大小
	size: {
		type: Number,
		default: () => 14,
	},
	// svg 颜色
	color: {
		type: String,
	},
});

// 获取 icon 图标名称
const getIconName = computed(() => {
	return props?.name;
});
// 用于判断 element plus 自带 svg 图标的显示、隐藏
const isShowElIcon = computed(() => {
	return props?.name?.startsWith('ele-');
});
// # 用于判断引入SVG图标的显示、隐藏
const linesString = ['/menuSvgs', 'http', 'https'];
const isShowSvgIconImg = computed(() => {
	return linesString.find((str) => props.name?.startsWith(str));
});
// 为svg容器生成唯一的class
const svgIconClassName = computed(() => {
  if (props?.name.includes('.svg')) {
    return 'svg-container-' + Math.floor(Math.random() * 10000);
  }
  return '';
});
// 若引入svg图标，则将svg图标写入svg容器
watch(() => isShowSvgIconImg, (isShowSvgIconImg) => {
  if (isShowSvgIconImg.value) {
    nextTick(() => {
      const svgContainer = document.querySelector(`.${svgIconClassName.value}`);
      fetch(props.name)
      .then(response => response.text())
      .then((svgContent) => {
        svgContainer.innerHTML = svgContent;
      })
    });
  }
}, { immediate: true });
// 设置图标样式
const setIconStyle = computed(() => {
	return `font-size: ${props.size}px !important; color: ${props.color};`;
});
// 设置图片样式
const setSvgIconStyle = computed(() => {
	return `width: 24px; text-align: center; margin-right: 5px;`;
});
</script>

<style lang="scss">
  [class^="svg-container"] svg {
    vertical-align: middle !important;
    fill: currentColor;
  }
</style>

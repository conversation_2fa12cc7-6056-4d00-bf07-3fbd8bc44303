import { gps84_To_gcj02, bd09_To_gcj02, } from '@vuemap/vue-amap';
/**
 * 坐标转换
 * @param 
 * @returns 返回转换后的坐标
 */
export function convertCoordinates(longitude: number, latitude: number, coordinateType: null | 1 | 2) {
  // coordinateType : 1为wgs84坐标系,空或者0为bd09
  if (!longitude || !latitude) {
    return [];
  }

  let gcj02;
  if (coordinateType === 1) {
    // console.log(coordinateType, '1为wgs84坐标系');
    gcj02 = gps84_To_gcj02(longitude, latitude);
  } else {
    // console.log(longitude, latitude, '空或者0为bd09');
    gcj02 = bd09_To_gcj02(longitude, latitude);
  }
  return [gcj02.lng, gcj02.lat]

}
interface ThemeConfig {
  primary: string;
  menuBar: string;
  menuBarColor: string;
  menuBarActiveColor: string;
  menuActiveColor: string;
  loadingBg: string;
  tableHeaderColor: string;
}

/**
 * 全局配置文件 参考
 */
// const GLOBAL_CONFIG = {
//   system: 'sd', // 系统：`default` 后台项目，`sd` 湿地大屏+后台项目，`robot` 机器人大屏+后台项目
//   company: 'other', // 所属单位：nmg：内蒙古（锡盟）、other（其他）

//   primary: '#104E47', // 主题色，绿色版（#104E47）、蓝色版（#409eff）
//   menuBar: '#104E47', // 菜单背景色，绿色版（#104E47）、蓝色版（#304156）
//   menuBarColor: '#eaeaea', // 默认菜单字体颜色
//   menuBarActiveColor: 'rgba(0, 0, 0, 0.1)', // 默认菜单高亮背景色
//   menuActiveColor: '#22D69F', // 默认菜单激活色，绿色版（#22D69F）、蓝色版（#409eff）
//   loadingBg: '#104E47', // 全局加载动画背景色，绿色版（#104E47）、蓝色色版（#7171C6）
//   tableHeaderColor: 'rgba(16, 78, 71, 0.1)', // 表头背景色，绿色版（rgba(16, 78, 71, 0.1)）、蓝色版（#f5f7fa）
// };

interface ProjectConfig {
  system: 'default' | 'sd' | 'robot';
  company: 'nmg' | 'other';
  theme: ThemeConfig;
}

// 默认蓝色主题
const defaultTheme: ThemeConfig = {
  primary: '#409eff',
  menuBar: '#304156',
  menuBarColor: '#eaeaea',
  menuBarActiveColor: 'rgba(0, 0, 0, 0.1)',
  menuActiveColor: '#409eff',
  loadingBg: '#7171C6',
  tableHeaderColor: '#f5f7fa'
};

// 绿色主题
const greenTheme: Partial<ThemeConfig> = {
  primary: '#104E47',
  menuBar: '#104E47',
  menuActiveColor: '#22D69F',
  loadingBg: '#104E47',
  tableHeaderColor: 'rgba(16, 78, 71, 0.1)'
};

export const projectConfigs: Record<string, ProjectConfig> = {
  wetland: {
    system: 'sd',
    company: 'other',
    theme: { ...defaultTheme }  // 使用默认蓝色主题
  },
  robot: {
    system: 'robot',
    company: 'other',
    theme: { ...defaultTheme, ...greenTheme }  // 使用绿色主题覆盖默认主题
  },
  heilongjiang: {
    system: 'default',
    company: 'other',
    theme: {
      ...defaultTheme,
      // 可以只修改个别属性
      primary: '#1890ff',
      menuActiveColor: '#1890ff'
    }
  },
  ximeng: {
    system: 'sd',
    company: 'nmg',
    theme: { ...defaultTheme, ...greenTheme }
  },
  // 新增湿地大屏绿色版本
  wetlandGreen: {
    system: 'sd',
    company: 'other',
    theme: { ...defaultTheme, ...greenTheme }  // 湿地大屏使用绿色主题
  }
};

export const configDescriptions = {
  wetland: '湿地大屏 (默认，直接回车)',
  robot: '机器人大屏 (绿色主题)',
  heilongjiang: '黑龙江湿地',
  ximeng: '锡盟 (绿色主题)',
  wetlandGreen: '湿地大屏 (绿色主题)'
}; 
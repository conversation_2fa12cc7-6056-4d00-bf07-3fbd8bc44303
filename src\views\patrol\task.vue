<template>
	<div class="layout-pd">
		<ViewSearch @onRefreshData="onRefresh">
			<template #searchFilters>
				<el-form-item label="机器人">
					<el-select
						v-model="searchOptions.filter.deviceId"
						placeholder="请选择机器人"
						style="width: 100%"
						:fit-input-width="true"
						:teleported="false"
						placement="bottom-start"
						clearable
					>
						<el-option
							v-for="(key, index) in robotsInfo.keys()"
              :key="index"
							:label="robotsInfo.get(key)?.robotName"
							:value="key"
						/>
					</el-select>
				</el-form-item>
				<el-form-item label="选择日期">
					<el-date-picker
						v-model="searchOptions.filter.beginDate"
						type="date"
						placeholder="点击选择日期"
						:teleported="false"
						clearable
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD"
						:editable="false"
						placement="bottom-start"
					>
					</el-date-picker>
				</el-form-item>
			</template>

			<template #searchBtns>
				<el-button
					type="danger"
					:disabled="tableRef?.selectRows.length === 0"
					@click="onBatchDelete"
				>
					<template #icon>
						<el-icon><ele-Delete /></el-icon>
					</template>
					删除
				</el-button>
				<el-button @click="onSynchronousToday" :loading="syncLoading">
					<template #icon>
						<el-icon><ele-Refresh /></el-icon>
					</template>
					同步今日
				</el-button>
				<el-button @click="dialogData.visible = true" :loading="syncHLoading">
					<template #icon>
						<el-icon><ele-Refresh /></el-icon>
					</template>
					同步历史
				</el-button>
			</template>
		</ViewSearch>

		<Table v-bind="tableOptions" ref="tableRef" @pageChange="onPageChange">
			<template #mapShow="{ row }">
				<el-switch
					v-model="row.mapShow"
					:active-value="1"
					:inactive-value="0"
					@change="onMapShow($event, row.id)"
				/>
			</template>
			<template #operate="{ row }">
				<el-button size="small" text type="primary" @click="onDelRow(row.id)">
					<el-icon><ele-Delete /></el-icon>
					删除
				</el-button>
			</template>
		</Table>
		<el-dialog v-model="dialogData.visible" title="同步历史" width="450">
			<el-date-picker
				top="10vh"
				style="width: 100%"
				v-model="dialogData.date"
				type="daterange"
				range-separator="-"
				start-placeholder="点击选择开始时间"
				end-placeholder="点击选择结束时间"
				:teleported="false"
				format="YYYY-MM-DD"
				:clearable="false"
				value-format="YYYY-MM-DD"
			/>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="onDialogClose">取消</el-button>
					<el-button type="primary" @click="onSynchronousHistory">确定</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="PTask">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { getPatrolTasks } from '/@/api/home';
import { initPatrolTasks, batchDelTasks, delTask, removeMapShow, addMapShow } from '/@/api/patrol';
import { liveInfo } from '/@/stores/fullScreen';
import { storeToRefs } from 'pinia';
import { AUTHS } from '/@/directive/authDirective';
import { formatDate } from '/@/utils/formatTime';
const liveStore = liveInfo();
const { robotsInfo } = storeToRefs(liveStore);

// 引入异步组件
const ViewSearch = defineAsyncComponent(() => import('/@/components/viewSearch/index.vue'));
const Table = defineAsyncComponent(() => import('/@/components/table/index.vue'));

const tableRef = ref<InstanceType<typeof Table>>();
const intraday = formatDate(new Date(), 'YYYY-mm-dd');

const dialogData = ref({
	visible: false,
	date: [intraday, intraday],
});
const searchOptions = reactive({
	filter: {
		deviceId: null,
		beginDate: intraday,
	},
	selectAll: false,
});

const tableOptions: GlobalTableOptions<AiModelRow> = reactive({
	data: [],
	header: [
		{
			title: '任务名称',
			key: 'patrolPlanName',
			isCheck: true,
		},
		{
			title: '机器人名称',
			key: 'robotName',
			isCheck: true,
		},
		{
			title: '开始时间',
			key: 'beginTime',
			isCheck: true,
		},
		{
			title: '结束时间',
			key: 'endTime',
			isCheck: true,
		},
		{
			title: '大屏展示',
			key: 'mapShow',
			isCheck: true,
		},
	],
	config: {
		loading: true,
		isSelection: true,
		isSerialNo: true,
		isOperate: AUTHS(['*:*:*', 'robot-patrol-tasks:*:*']),
		operateWidth: 150, // 操作列宽
		total: 0, // 总条数
	},
	pageParams: {
		page: 1,
		size: 10,
	},
});

onMounted(async () => {
	await initTableData();
});

const initTableData = async () => {
	tableOptions.config.loading = true;
	try {
		const query: any = {
			page: tableOptions.pageParams?.page && tableOptions.pageParams.page - 1,
			size: tableOptions.pageParams?.size,
			...searchOptions.filter,
		};
		const { payload } = await getPatrolTasks(query);
		tableOptions.config.loading = false;
		tableOptions.data = payload.content;
		tableOptions.config.total = payload.totalElements;
	} catch (e) {
		tableOptions.config.loading = false;
		tableOptions.config.total = 0;
		tableOptions.data = [];
	}
};

const onRefresh = (resetPage: boolean = false, resetFilter: boolean = false) => {
	if (resetFilter) {
		searchOptions.filter.deviceId = null;
		searchOptions.filter.beginDate = intraday;
	}
	if (resetPage) {
		tableRef.value?.pageReset();
	} else {
		initTableData();
	}
	tableRef.value?.clearSelection();
};

const onPageChange = async (page: { pageNum: number; pageSize: number }) => {
	if (tableOptions.pageParams) {
		tableOptions.pageParams.page = page.pageNum;
		tableOptions.pageParams.size = page.pageSize;
	}
	initTableData();
};

let mapShowFunOnce = false; // 页面初始化第一次执行(会调用这个方法)还没完成，所以先return
const onMapShow = (value: boolean, id: string) => {
	// if (!mapShowFunOnce) return; // initTableData页面初始化第一次执行(会调用这个方法)还没完成，所以先return
	// if (tableOptions.data.length === 0 && id === undefined) return; // 刚进入页面会自动调用这个方法， 这个时候id===undefined
	if (mapShowFunOnce) {
		if (value) {
			addMapShow(id)
				.then(async () => {
					ElMessage.success('添加成功');
					onRefresh();
				})
				.catch(() => {
					ElMessage.success('添加失败');
				});
		} else {
			removeMapShow(id)
				.then(async () => {
					ElMessage.success('移除成功');
					onRefresh();
				})
				.catch(() => {
					ElMessage.success('移除失败');
				});
		}
	} else {
		mapShowFunOnce = true;
	}
};

const onBatchDelete = () => {
	ElMessageBox({
		title: '提示',
		message: '此操作将永久删除，是否继续?',
		type: 'warning',
		showCancelButton: true,
	})
		.then(async () => {
			const ids = tableRef?.value?.selectRows.map((item) => item.id) as string[];
			await batchDelTasks(ids);
			ElMessage.success('删除成功');
			onRefresh();
		})
		.catch(() => {});
};

const onDelRow = (rowId: string) => {
	ElMessageBox({
		title: '提示',
		message: '此操作将永久删除，是否继续?',
		type: 'warning',
		showCancelButton: true,
	})
		.then(async () => {
			await delTask(rowId);
			ElMessage.success('删除成功');
			onRefresh();
		})
		.catch(() => {});
};
const syncLoading = ref(false);
const onSynchronousToday = () => {
	syncLoading.value = true;
	const query = {
		startDate: intraday,
		endDate: intraday,
	};
	initPatrolTasks(query)
		.then(() => {
			ElMessage.success('同步今日成功');
		})
		.catch(() => {
			ElMessage.error('同步今日失败');
		})
		.finally(() => {
			syncLoading.value = false;
		});
};

const syncHLoading = ref(false);
const onSynchronousHistory = () => {
	syncHLoading.value = true;
	const query = {
		startDate: dialogData.value.date[0],
		endDate: dialogData.value.date[1],
	};
	initPatrolTasks(query)
		.then(() => {
			ElMessage.success('同步历史成功');
			onDialogClose();
		})
		.catch(() => {
			ElMessage.error('同步历史失败');
		})
		.finally(() => {
			syncHLoading.value = false;
		});
	onDialogClose();
};

const onDialogClose = () => {
	dialogData.value.visible = false;
	dialogData.value.date = [intraday, intraday];
};
</script>

declare interface TableHeader {
  title: string; // 列标题
  key: string; // 列值
  colWidth?: number; // 列宽
  fixed?: string; // 固定列
  isCheck: boolean; // 是否显示
  isDate?: boolean; // 是否为日期
  format?: string; // 要转换日期格式
  noTooltip?: boolean; // 溢出隐藏
}
declare interface TableConfig {
  loading: boolean; // 加载中
  isSelection?: boolean; // 是否支持多选
  isSerialNo?: boolean; // 是否显示序号列
  isOperate?: boolean; // 是否显示操作列
  operateWidth?: number; // 操作列宽
  fixedOperate?: boolean; // 是否固定操作列
  total: number; // 表格总条数
  treeProps?: {
    children: string; // 子节点
  };
}
declare interface GlobalTableOptions<Data> {
  data: Data[];
  header: TableHeader[];
  config: TableConfig;
  pageParams?: {
    page: number;
    size: number;
  };
  isPagination?: boolean;
}

declare interface SelectOptions {
  label?: string;
  value?: number | string;
  [key: string]: any;
}

interface BaseTableData<K> extends TableType {
  filter: {
    sort?: string;
    [key: string]: any;
  };
  data: K[];
}
declare interface ViewBaseState<K> {
  tableData: BaseTableData<K>;
  [key: string]: any;
}

interface DialogCommonState {
  dialog: {
    isShowDialog: boolean;
    type: string;
    title: string;
    submitTxt: string;
  }
}

/*
  view monitorEvents
*/
interface MonitorEventDetailItem {
  recResultCnt: number;
  recResult: string;
  remark: string;
}

declare interface MonitorEventRow {
  id: string;
  checked?: boolean;
  latitude: string;
  longitude: string;
  eventType: number;
  eventLevel: number;
  device: {
    num: string;
    name: string;
    channelName: string;
    latitude: string;
    longitude: string;
  };
  maxScore: number;
  createTime: string;
  recTime: string;
  oriPictureId: string;
  oriPictureUrl: string;
  oriPictureThumbnailUrl: string;
  pictureId: string;
  pictureUrl: string;
  rawResult: string;
  moment: boolean;
  parentEventTypeName: string;
  pointName: string;
  remark: string;
  targetType: string;
  monitorEventDetails: MonitorEventDetailItem[];
}

declare interface MEResult {
  name: string;
  bbox: number[];
  score: number;
  count: number;
  eventType?: number;
  minx?: number;
  miny?: number;
  maxx?: number;
  maxy?: number;
}

declare interface MonitorEventDialogState {
  dialogVisible: boolean;
  dialogTableData: any[];
}

interface MonitorEventType {
  id: number;
  name: string;
  eventType: number;
  sort: number;
  offline: number;
}

/**
 * views ai-models
 */
declare interface AiModelRow {
  id: string;
  name: string;
  describe: string;
  versionName: string;
  apiUrl: string;
  createTime: string;
  [key: string]: any;
}

declare interface AiModelDialogState extends DialogCommonState {
  ruleForm: {
    id?: string;
    name: string;
    describe: string;
    versionName: string;
    apiUrl: string;
    recType: string | number;
    taskType: string | number;
  }
}

/**
 * views lives
 */
declare interface LiveRow {
  id: string;
  name: string;
  type: number;
  robotId: number;
  deviceId: string;
  startTime: string;
  endTime: string;
  picId: string;
  picUrl: string;
}

interface LiveForm {
  id?: string;
  name: string;
  type: number;
  deviceId?: string | null;
  robotId?: number | null;
  timePeriod?: string[];
  startTime?: string;
  endTime?: string;
  picId: string;
}
declare interface LiveDialogState extends DialogCommonState {
  ruleForm: LiveForm;
  uploadedPicUrl: string;
  robutOptions: {
    deviceId: number;
    robotName: string;
  }[];
}

/*
  view devices
*/
declare interface DeviceRow {
  id: string;
  name: string;
  num: string;
  deviceNum: string;
  channelName?: string;
  channelNo?: number;
  coordinateType: number | null;
  longitude: number;
  latitude: number;
  previewUrl: string;
  createTime: string;
  sourceType: number; // 1萤石云 2红外设备 3监控设备
  productId: string;
  manufacturer: number;
  ifCamera?: number; // 是否摄像设备
  ifControl?: number; // 是否支持云平台控制
  // 自定义
  isParentNode: boolean;
  isDevChannel: boolean;
  serialNum: number;
  channels: any[];
  location?: number[];
  active?: boolean;
  onlineStatus: number;
}

declare interface DeviceDialogState extends DialogCommonState {
  ruleForm: {
    name: string;
    num: string;
    manufacturer: number;
    coordinateType: number | null;
    longitude: number | null;
    latitude: number | null;
    previewUrl: string;
    sourceType: number;
  };
  editDeviceIds: string[];
}

declare interface FireDeviceChannel {
  id: string;
  channelNo: string;
  channelName: string;
  previewUrl: string;
  isDevChannel: boolean;
  ifControl: number;
  manufacturer: number;
}

declare interface FireDeviceRow {
  id: string;
  productId: string; // 设备IP
  num: string;
  name: string;
  manufacturer: number; // 生产厂商
  infraredId: string; // 热成像设备id
  infraredVideoUrl: string; // 热成像视频地址
  infraredVideoCoverUrl: string; // 热成像封面
  visibleLightId: string; // 可见光设备id
  visibleLightVideoUrl: string; // 可见光视频地址
  visibleVideoCoverUrl: string; // 可见光封面
  ifControl: number; // 是否支持云平台控制
  ifCamera: number; // 是否摄像设备
  coordinateType: number; // 坐标系类型1：WGS84 2：GCJ02 3：BD09LL
  longitude: number;
  latitude: number;
  createTime: string;
  // 自定义
  serialNum?: number; // 序号
  isChannel: boolean;
  channels?: FireDeviceChannel[];
  previewUrl: string; // 预览地址
  coverUrl?: string; // 预览封面
}


declare interface FireDeviceDialogState extends DialogCommonState {
  ruleForm: {
    infraredId?: string;
    visibleLightId?: string;
    name: string;
    num: string;
    manufacturer: number;
    productId: string;
    infraredVideoUrl: string;
    visibleLightVideoUrl: string;
    coordinateType: number;
    longitude: number | null;
    latitude: number | null;
  };
}

interface RobotParameters {
  online: boolean; // 0不在线 1在线
  mileage: number; // 总巡航里程
  currentMileage: number; // 当前巡航里程
  duration: number; // 总开机时长
  currentDuration: number;// 开机时长
  temperature: string; // 温度
  humidity: string; // 湿度
  electricity: number;// 电量百分比
  batteryTemp: number; // 电池温度
  latitude: number;
  longitude: number;
  gclLongitude: number;
  getGclLatitude: number;
  ifChargeTask: number;//0未充电 1充电
}

/**
 * views monitors
 */
interface MonitorCommon {
  name: string; // 名称
  deviceId?: string; // 设备id
  deviceIds?: string | string[]; // 设备ids
  aiModelId: string; // 模型id
  sampleInterval: number; // 样本时间间隔，单位s
  sampleDuration: number; // 样本时长，单位s
  warnFrequency: number; // 预警频度，单位s
  allDay: boolean;
  processType: number;
}

declare interface MonitorRow extends MonitorCommon {
  id: string;
  periodStartTime: string; // 监测时段开始时间
  periodEndTime: string; // 监测时段结束时间
  monitorRunStatus: number; // 监测器运行状态0未启动1启动中
  monitorRunTime: string; // 监测运行时间
  monitorStatus: number; // 检测器状态0正常 1模型异常 2设备异常
  createTime: string;
  updateTime: string;
  deviceModel: DeviceRow;
}

declare interface DialogMonitor extends MonitorCommon {
  id?: string;
  periodTime?: string[];
  periodStartTime?: string;
  periodEndTime?: string;
  sourceType: number;
}

/**
 * views recognize
 */
declare type MPictureItem = MonitorEventRow;

declare interface MResultItem {
  oriPictureId: string;
  oriPictureUrl: string;
  pictures: MPictureItem[];
}

declare interface CustomResult {
  pictureId: string;
  localUrl: string;
  pictureUrl?: string;
  recResult?: MonitorEventDetailItem[] | string;
  isRecogFail: boolean;
}

declare interface ModelState {
  ruleForm: {
    deviceId: string;
    aiModelId: string;
    recType: number;
  };
  deviceId: string;
  titleText: string;
  loading: boolean;
  fileIds: string[];
  monitorId: string;
  showVideo: boolean;
  localVideoUrl: string;
  resultList: MResult[];
  intervalTimer: Timer | null;
}

/**
 * views alarm - email
 */

declare interface EmailRow {
  id: string;
  email: string;
  remark: string;
  enable: number; // 是否启用，不启用的不发送邮件
  createTime: string;
}
declare interface EmailDialogState extends DialogCommonState {
  ruleForm: {
    id?: string;
    email: string;
    remark: string;
    enable: number;
  };
}

/**
 * views rawMaterials
 */
declare interface RMRow {
  id: number;
  imei: string;
  type: number; // 类型：0图片 1视频
  fileId: string; // 图片id
  coverUrl: string; // 视频封面图
  thumbnailUrl: string; // 图片缩略图
  url: string;
  longitude: number;
  latitude: number;
  createTime: string;
  updateTime: string;
  monitorEvents: {
    id: string;
    aiModelId: string;
    aiModelName: string;
    oriPictureUrl: string;
    pictures: MonitorEventRow[];
  }[];
}

declare interface SnapRMRow {
  id: string;
  oriPictureId: string;
  oriPictureThumbnailUrl: string;
  oriPictureUrl: string; // 原图
  pictureUrl: string; // 识别图
  deleted: number; // 是否删除：0未删除 1已删除
  rawResult: string;
  monitorEventDetails: MonitorEventDetailItem[];
  device: {
    num: string;
    name: string;
    longitude: number;
    latitude: number;
  };
  createTime: string;
  updateTime: string;
}

/**
 * views users
 */
type Role = {
  id: number;
  name: string;
  authcRole: boolean;
  anonRole: boolean;
}
declare interface UserRow {
  id: string;
  name: string;
  username: string;
  role: Role;
  createTime: string;
  updateTime: string;
}

declare interface UserDialogState extends DialogCommonState {
  ruleForm: {
    id?: string;
    username: steing;
    name: string;
    password?: string;
    checkPassword?: string;
    userType: number;
  };
}

declare interface ResetPwdDialogState extends DialogCommonState {
  ruleForm: {
    oldPass: string;
    newPass: string;
    checkPass: string;
  };
  userId: string | undefined;
}

/**
 * views auth
 */

type Permission = {
  id: number;
  name: string;
  permissions: string;
  title: string;
};

declare interface AuthRow extends Role {
  permissions: Permission[];
}

declare interface AssignDialogState {
  dialog: {
    isShowDialog: boolean;
    title: string;
  };
  roleId: number;
  assignedPermissions: Permission[];
  permissions: Permission[];
}

/**
 * views full-screen
 */
declare interface Robot {
  deviceId: number;
  robotName: string;
  robotSn: string;
  status: {}
}

declare type RobotsPosition = [number, number][];

declare interface PatrolLine {
  id?: string;
  patrolLineId: string;
  zIndex: number;
  path: [number, number][];
  longitude?: number;
  latitude?: number;
  patrolLinePointList?: PatrolLine[];
}

declare interface RobotInfo {
  [key: string]: string | number;
  deviceId: number,
  robotName: string;
  electricity: string;
  currentMileage: string;
  state: string;
  robotSn: string;
  currentDuration: string;
  lng: number;
  lat: number;
}

// 统计类型枚举
type StatType = 0 | 1 | 2 | null;


// 大屏右侧列表跟地图marker类型
declare interface MarkerInfo {
  id: string;
  eventType: number;
  groupEventType: number;
  eventLevel: number;
  position: number[];
  markerUrl: string;
  recResult: string;
  recTime: string;
  picture: string;
  animation: boolean;
  zIndex: number;
  parentEventTypeName: string;
  pointName: string;
  remark: string;
}


declare interface statReportRow {
  robotPointPatrolOverviews: any;
  [x: string]: any;

}


/**
 * json格式
 *  id：消息id
 *  targetId：目标类型id 相应数据id如device_id、monitor_event_file_id
 *  targetType：目标类型 0设备告警 1物种鉴定 2珍稀鸟类 3特定鸟类 4内蒙古鸟类
 *  text：消息内容
 *  createTime：创建时间
 */
declare interface SSEData {
  id: string;
  targetId: string;
  targetType: 0 | 1 | 2 | 3 | 4;
  text: string;
  createTime: string;
}


declare type TimeType = 1 | 2 | 3 | 'all';
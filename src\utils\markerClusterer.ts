import Supercluster from 'supercluster';

/**
 * 定义点数据接口
 */
export interface PointData {
  id: string;
  name: string;
  position: [number, number]; // [lng, lat]
  [key: string]: any; // 其他自定义数据
}

/**
 * 定义 GeoJSON Feature 接口
 */
export interface ClusterFeature {
  type: string;
  geometry: {
    type: 'Point';
    coordinates: [number, number];
  };
  properties: {
    point_count?: number;
    point_count_abbreviated?: string;
    cluster?: boolean;
    cluster_id?: number;
    isSinglePoint?: boolean;
    [key: string]: any;
  };
  originalData?: PointData; // 原始数据
}

/**
 * 定义标记选项接口
 */
export interface MarkerOptions {
  content?: string;
  offset?: AMap.Pixel;
  anchor?: string;
  [key: string]: any;
}

/**
 * 定义聚合配置接口
 */
export interface ClusterOptions {
  radius?: number;
  maxZoom?: number;
  minPoints?: number;
  enableDebounce?: boolean;
  debounceDelay?: number;
}

/**
 * 定义 MarkerClusterer 选项接口
 */
export interface MarkerClustererOptions {
  clusterOptions?: ClusterOptions;
  // 自定义单个标记点渲染函数
  renderSingleMarker?: (feature: ClusterFeature) => string;
  // 自定义聚合点渲染函数
  renderClusterMarker?: (feature: ClusterFeature) => string;
  // 标记点击事件
  onMarkerClick?: (feature: ClusterFeature, marker: AMap.Marker) => void;
  // 聚合点点击时的缩放增量
  zoomIncrement?: number;
  // 动画延迟
  animationDelay?: number;
}

/**
 * MarkerClusterer - 高德地图标记聚合类
 * 专门用于处理点标记的聚合显示
 */
export default class MarkerClusterer {
  private map: AMap.Map;
  private options: MarkerClustererOptions;
  private supercluster: Supercluster;
  private markers: AMap.Marker[] = [];
  private pointsData: PointData[] = [];
  private debounceTimer: NodeJS.Timeout | null = null;

  constructor(map: AMap.Map, options: MarkerClustererOptions = {}) {
    this.map = map;
    this.options = {
      clusterOptions: {
        radius: 90,
        maxZoom: 16,
        minPoints: 2,
        enableDebounce: true,
        debounceDelay: 200,
        ...options.clusterOptions
      },
      zoomIncrement: 1,
      animationDelay: 300,
      ...options
    };

    this.initSupercluster();
    this.bindMapEvents();
  }

  /**
   * 初始化 Supercluster
   */
  private initSupercluster(): void {
    this.supercluster = new Supercluster({
      radius: this.options.clusterOptions!.radius!,
      maxZoom: this.options.clusterOptions!.maxZoom!,
      minPoints: this.options.clusterOptions!.minPoints!,
    });
  }

  /**
   * 绑定地图事件
   */
  private bindMapEvents(): void {
    this.map.on('moveend', this.handleMapUpdate.bind(this));
    this.map.on('zoomend', this.handleMapUpdate.bind(this));
  }

  /**
   * 处理地图更新
   */
  private handleMapUpdate(): void {
    if (this.options.clusterOptions!.enableDebounce) {
      this.debouncedUpdateClusters();
    } else {
      this.updateClusters();
    }
  }

  /**
   * 防抖更新聚合
   */
  private debouncedUpdateClusters(): void {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    this.debounceTimer = setTimeout(() => {
      this.updateClusters();
    }, this.options.clusterOptions!.debounceDelay!);
  }

  /**
   * 设置点数据
   */
  setData(points: PointData[]): void {
    this.pointsData = points;

    // 转换为 GeoJSON 格式
    const geoJsonFeatures = points.map(point => ({
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: point.position
      },
      properties: {
        ...point,
        cluster: false
      }
    }));

    this.supercluster.load(geoJsonFeatures as any);
    this.updateClusters();
  }

  /**
   * 更新聚合显示
   */
  private updateClusters(): void {
    // 清除旧的标记
    this.clearMarkers();

    if (this.pointsData.length === 0) return;

    // 获取当前视野范围
    const bounds = this.map.getBounds();
    const zoom = this.map.getZoom();
    const bbox: [number, number, number, number] = [
      bounds.getSouthWest().getLng(),
      bounds.getSouthWest().getLat(),
      bounds.getNorthEast().getLng(),
      bounds.getNorthEast().getLat()
    ];

    // 获取聚合数据
    const clusters = this.supercluster.getClusters(bbox, zoom);

    // 创建标记
    clusters.forEach((cluster: any) => {
      const marker = this.createMarker(cluster as ClusterFeature);
      if (marker) {
        this.markers.push(marker);
        this.map.add(marker);
      }
    });
  }

  /**
   * 创建标记
   */
  private createMarker(feature: ClusterFeature): AMap.Marker | null {
    const [lng, lat] = feature.geometry.coordinates;
    const isCluster = feature.properties.cluster;
    const pointCount = feature.properties.point_count || 0;

    let content: string;
    if (isCluster && pointCount > 1) {
      // 聚合标记
      if (this.options.renderClusterMarker) {
        content = this.options.renderClusterMarker(feature);
      } else {
        content = this.getDefaultClusterContent(pointCount);
      }
    } else {

      // 单个标记
      if (this.options.renderSingleMarker) {
        content = this.options.renderSingleMarker(feature);
      } else {
        content = this.getDefaultSingleContent(feature);
      }
    }

    const marker = new AMap.Marker({
      position: [lng, lat],
      content,
      anchor: isCluster ? 'center' : 'bottom-center',
      cursor: 'pointer',
      zIndex: isCluster ? 200 : 100  // 聚合点 zIndex 更高
    });


    // 添加点击事件
    marker.on('click', () => {
      if (isCluster && pointCount > 1) {
        // 聚合点击 - 展示聚合子级视野
        this.handleClusterClick(feature);
      } else {
        // 单个点击 - 触发自定义事件
        if (this.options.onMarkerClick) {
          this.options.onMarkerClick(feature, marker);
        }
      }
    });

    return marker;
  }

  /**
   * 处理聚合点击 - 使用setFitView展示聚合子级视野
   */
  private handleClusterClick(feature: ClusterFeature): void {
    console.log('feature', feature);

    // 获取聚合点的cluster_id
    const clusterId = feature.properties.cluster_id;

    if (!clusterId) {
      console.warn('聚合点缺少cluster_id');
      return;
    }

    try {
      // 获取聚合点包含的所有叶子节点（原始点）
      const leaves = this.supercluster.getLeaves(clusterId, Infinity);
      console.log(leaves);
      if (leaves.length === 0) {
        console.warn('聚合点没有子级数据');
        return;
      }

      // 为每个子级点创建临时的Marker对象
      const tempMarkers: AMap.Marker[] = leaves.map((leaf: any) => {
        const [lng, lat] = leaf.geometry.coordinates;
        return new AMap.Marker({
          position: [lng, lat]
          // 不设置map属性，这样marker不会被添加到地图上
        });
      });

      if (tempMarkers.length === 0) {
        console.warn('没有有效的标记数据');
        return;
      }

      console.log('tempMarkers', tempMarkers);
      // 使用setFitView设置合适的视野来包含所有子级点
      this.map.setFitView(tempMarkers, true, [60, 10, 430, 430]);

    } catch (error) {
      console.error('获取聚合子级数据失败:', error);
      // 降级处理：使用原来的放大逻辑
      const [lng, lat] = feature.geometry.coordinates;
      const currentZoom = this.map.getZoom();
      const newZoom = currentZoom + (this.options.zoomIncrement || 1);

      this.map.panTo([lng, lat]);
      setTimeout(() => {
        this.map.setZoom(newZoom);
      }, this.options.animationDelay || 300);
    }
  }

  /**
   * 获取默认聚合标记内容
   */
  private getDefaultClusterContent(count: number): string {
    return `
      <div style="
        width: 35px;
        height: 35px;
        background-image: url('/src/assets/sd/data-screen/markerClusterer.png');
        background-size: cover;
        background-position: center;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 14px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        pointer-events: auto;
        cursor: pointer;
      ">
        ${count}
      </div>
    `;
  }

  /**
   * 获取默认单个标记内容
   */
  private getDefaultSingleContent(feature: ClusterFeature): string {
    const name = feature.properties.name || '标记';
    return `
      <div style="
        background: white;
        border: 2px solid #4285f4;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;
        color: #333;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      ">
        ${name}
      </div>
    `;
  }

  /**
   * 清除所有标记
   */
  private clearMarkers(): void {
    this.markers.forEach(marker => {
      this.map.remove(marker);
    });
    this.markers = [];
  }

  /**
   * 更新数据
   */
  updateData(points: PointData[]): void {
    this.setData(points);
  }

  /**
   * 清空数据
   */
  clear(): void {
    this.clearMarkers();
    this.pointsData = [];
  }

  /**
   * 销毁实例
   */
  destroy(): void {
    this.clear();
    // 移除事件监听器
    if (this.map) {
      this.map.off('moveend', this.handleMapUpdate.bind(this));
      this.map.off('zoomend', this.handleMapUpdate.bind(this));
    }
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
  }

  /**
   * 获取当前点数据
   */
  getData(): PointData[] {
    return this.pointsData;
  }

  /**
   * 设置选项
   */
  setOptions(options: Partial<MarkerClustererOptions>): void {
    this.options = { ...this.options, ...options };
  }
}
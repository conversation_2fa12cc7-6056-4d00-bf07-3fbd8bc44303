// 百度地图图层
// import projzh from 'projzh';
// import { Projection, addProjection, addCoordinateTransforms } from 'ol/proj';
import TileLayer from 'ol/layer/Tile.js';
import XYZ from 'ol/source/XYZ.js';
import TileGrid from 'ol/tilegrid/TileGrid.js';

// const baiduMercator = new Projection({
//   code: 'baidu',
//   units: 'm'
// });
// addProjection(baiduMercator);
// addCoordinateTransforms('EPSG:4326', baiduMercator, projzh.ll2bmerc, projzh.bmerc2ll);
// addCoordinateTransforms('EPSG:3857', baiduMercator, projzh.smerc2bmerc, projzh.bmerc2smerc);

// 分辨率
const projection = 'baidu';
const resolutions: number[] = [];
for (var i = 0; i < 19; i++) {
  resolutions[i] = Math.pow(2, 18 - i);
}

const layerSource = () => {
  return new XYZ({
    projection: projection,
    tileGrid: new TileGrid({
      origin: [0, 0],
      resolutions: resolutions,
      minZoom: 1,
    }),
    // 出图基地址
    tileUrlFunction: function (tileCoord: any[]) {
      if (!tileCoord) {
        return "";
      }
      var z = tileCoord[0];
      var x = tileCoord[1];
      var y = tileCoord[2];

      if (x < 0) {
        x = "M" + (-x);
      }
      if (y < 0) {
        y = "M" + (-y);
      }
      return "http://online3.map.bdimg.com/onlinelabel/?qt=tile&x=" + x + "&y=" + y + "&z=" + z + "&styles=pl&udt=20151021&scaler=1&p=1";
    }
  });
};

export default [
  new TileLayer({
    source: layerSource(),
  }),
];

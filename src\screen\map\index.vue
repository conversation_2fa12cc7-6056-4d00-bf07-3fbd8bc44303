<template>
	<div class="map-container">
		<el-amap
			:center="center"
			:zoom="zoom"
			mapStyle="amap://styles/blue"
			view-mode="3D"
			:pitch="40"
			:zooms="[2, 30]"
			@init="initMap"
			@zoomchange="onMapZoomChange"
		>
			<!-- 设备标记点 -->
			<el-amap-marker v-for="device in deviceList" :key="device.num" :position="device.location">
				<div class="device-marker" @click="handleMarkerClick(device, $event)">
					<div class="device-info" :class="{ 'single-line': isShortText(device.name) }">
						{{ device.name }}
					</div>
					<img class="device-icon" :src="device.icon" alt="" title="" />
				</div>
			</el-amap-marker>
			<el-amap-loca @init="initLoca">
				<el-amap-loca-heatmap
					:visible="heatMapState.heatMapVisible"
					:source-data="heatMapState.heatMapData"
					:layer-style="heatmapLayerStyle"
				/>
				<!-- <template v-if="pulseLinkSourceData.length">
					<el-amap-loca-pulse-link
						v-for="item in pulseLinkSourceData"
						:key="item"
						:source-data="item.source"
						:layer-style="item.style"
						:visible-duration="500"
						:zIndex="30"
					/>
				</template> -->
			</el-amap-loca>
			<el-amap-info-window
				v-if="windowInfoState.visible"
				ref="infoWindowRef"
				:visible="windowInfoState.visible"
				:position="windowInfoState.position"
				autoMove
				isCustom
				:avoid="dynamicAvoid"
				anchor="top-left"
				:offset="[100 * ratio, 0 * ratio]"
			>
				<div v-if="markerSelector.visible" class="map-custom-info-window marker-selector-container">
					<div class="info-window-title">
						<div class="info-window-title-left">
							附近存在{{ markerSelector.nearbyMarkers.length }}个设备，请选择：
						</div>
						<div class="info-window-title-close" @click="onCancelDialog"></div>
					</div>
					<div class="info-window-container">
						<div
							v-for="item in markerSelector.nearbyMarkers"
							:key="item.num"
							class="selector-item"
							:class="{ offline: item.onlineStatus !== 1 }"
							@click="selectMarker(item)"
						>
							<img class="small-icon" :src="getDeviceIcon(item)" alt="" />
							<span>{{ item.name }}</span>
						</div>
					</div>
				</div>
				<div v-else class="map-custom-info-window nmg">
					<div class="info-window-title">
						<div class="info-window-title-left">{{ windowInfoState.title }}</div>
						<div class="info-window-title-close" @click="onCancelDialog"></div>
					</div>
					<div class="info-window-container">
						<div class="info-window-device-info">
							<el-link class="info-window-device-name" @click="onDeviceLinkClick">
								{{ windowInfoState.device.name }}
							</el-link>
							<div class="info-window-device-status-line"></div>
						</div>
						<div class="more-info">
							<span class="more-info-title">发现物种排名</span>
							<img
								class="more-icon"
								@click="onDeviceLinkClick"
								src="/src/assets/sd/data-screen/more.png"
								alt=""
							/>
						</div>
						<div
							class="info-window-cont ranks"
							v-loading="rankState.getRankListLoading"
							@wheel.prevent.stop="handleWheel"
							@mouseenter="handleMouseEnter"
							@mouseleave="handleMouseLeave"
						>
							<div class="rank-item" v-for="(item, $index) in rankState.rankList" :key="$index">
								<div class="serial-num" :class="$index < 3 ? 'threeBefore' : ''">
									NO.{{ $index + 1 }}
								</div>
								<div class="info">
									<span>{{ item.speciesAlias }}</span>
									<span class="count">{{ item.speciesFrequency }}</span>
								</div>
							</div>
							<el-empty
								v-if="rankState.rankList.length === 0 && rankState.getRankListLoading === false"
								description="暂无物种频次排行数据"
							>
								<template #image>&nbsp;</template>
							</el-empty>
						</div>
					</div>
				</div>
			</el-amap-info-window>
		</el-amap>
	</div>
</template>

<script setup lang="ts">
import {
	ref,
	Ref,
	shallowRef,
	reactive,
	inject,
	watch,
	onUnmounted,
	nextTick,
	computed,
} from 'vue';

import { ElAmap } from '@vuemap/vue-amap';
import { getDevSpecieslocus } from '/@/api/sd/dataScreen/bird';
import { useHeatMap } from '../hooks/useHeatMap';
import { ElAmapLoca, ElAmapLocaPulseLink } from '@vuemap/vue-amap-loca';
import { transCoordinate, randomInteger, isShortText } from '../utils/map';
import { useRouter } from 'vue-router';
// 设备点位图标
import infraredIconOnline from '/@/assets/sd/map/infrared-1.png';
import infraredIconOffline from '/@/assets/sd/map/infrared-0.png';
import monitoringIconOnline from '/@/assets/sd/map/monitoring-1.png';
import monitoringIconOffline from '/@/assets/sd/map/monitoring-0.png';
import { getDevices } from '/@/api/devices';
import { useInterval } from '/@/hooks/useInterval';
import type { Track, SpeciesEvent } from '/@/viewsSD/dataScreen/type';
import { createQuery } from '../utils/query';
const router = useRouter();
const eventType = inject('eventType') as Ref<any>;
const timeType = inject('timeType') as Ref<TimeType>;
const isLeftCollapsed = inject('isLeftCollapsed') as Ref<boolean>;
const isRightCollapsed = inject('isRightCollapsed') as Ref<boolean>;
// map
const ratio = window.innerWidth / 1920;
let map = shallowRef<AMap.Map>(); // 地图实例
const zoom = ref<number>(10);
const center = ref<[number, number]>();

// 动态计算 avoid 值
const dynamicAvoid = computed(() => {
	const baseAvoid = [70 * ratio, 50 * ratio, 50 * ratio, 30 * ratio]; // [上, 右, 下, 左]
	const sidebarWidth = 402 * ratio; // 侧栏宽度

	// 根据侧栏折叠状态调整边距
	// 当侧栏展开时（collapsed为false），需要增加边距避开侧栏
	const leftMargin = isLeftCollapsed.value ? baseAvoid[3] : baseAvoid[3] + sidebarWidth;
	const rightMargin = isRightCollapsed.value ? baseAvoid[1] : baseAvoid[1] + sidebarWidth;
	console.log(isRightCollapsed.value);
	const avoid = [baseAvoid[0], rightMargin, baseAvoid[2], leftMargin];
	return avoid;
});

const windowInfoState = reactive({
	visible: false,
	position: [0, 0],
	videoUrl: '',
	title: '',
	onlineStatus: 0,
	device: {} as DeviceRow,
});
const onCancelDialog = () => {
	windowInfoState.visible = false;
	windowInfoState.videoUrl = '';
	windowInfoState.position = [0, 0];
	windowInfoState.title = '';
	windowInfoState.onlineStatus = 0;
	windowInfoState.device = {} as DeviceRow;
	markerSelector.visible = false;
	rankState.rankList = [];
};

// 设备信息弹窗（infoWindow），点击设备名称或更多图标，跳转到详情页（即感知历史页面）；
const onDeviceLinkClick = () => {
	console.log(windowInfoState);
	router.push({
		path: '/screen/atlas',
		query: {
			deviceNum: windowInfoState.device.deviceNum || windowInfoState.device.num,
			pagePath: 'species',
			timeType: timeType.value,
		},
	});
};

const rankState = reactive({
	getRankListLoading: false,
	rankList: [] as SpeciesEvent[], // 物种频次排行
	markerClickCallback: null as Function | null,
});

const isHovering = ref(false);
const handleMouseEnter = () => {
	isHovering.value = true;
	// 禁用地图的滚轮缩放
	if (map.value) {
		map.value.setStatus({
			scrollWheel: false,
		});
	}
};

const handleMouseLeave = () => {
	isHovering.value = false;
	// 恢复地图的滚轮缩放
	if (map.value) {
		map.value.setStatus({
			scrollWheel: true,
		});
	}
};
const handleWheel = (e: any) => {
	if (!isHovering.value) return;
	e.preventDefault();
	e.stopPropagation();
	const container = e.currentTarget;
	container.scrollTop += e.deltaY;
};
const getDeviceIcon = (device: any) => {
	if (device.onlineStatus === 1) {
		return device.sourceType === 2 ? infraredIconOnline : monitoringIconOnline;
	}
	return device.sourceType === 2 ? infraredIconOffline : monitoringIconOffline;
};

// 获取标记图标配置
const getMarkerIcon = (device: DeviceRow) => {
	const iconUrl = getDeviceIcon(device);
	return {
		type: 'image',
		image: iconUrl,
		size: [32, 32],
		anchor: 'bottom-center',
	};
};
// 设置地图视野以显示所有设备标记
const setMapFitView = () => {
	if (map.value && deviceList.value.length > 0) {
		const validDevices = deviceList.value.filter(
			(device) => device.location && Array.isArray(device.location) && device.location.length >= 2
		);

		if (validDevices.length > 0) {
			// 创建临时marker用于计算视野
			const tempMarkers = validDevices.map(
				(device) => new AMap.Marker({ position: device.location })
			);

			// 设置地图视野以显示所有设备标记
			map.value.setFitView(tempMarkers, true, [60, 10, 100, 100]); // 上、下、左、右边距

			// 清理临时marker
			tempMarkers.forEach((marker) => marker.destroy());
		}
	}
};

const deviceList = ref<DeviceRow[]>([]);
const getDeviceList = async () => {
	const { payload } = await getDevices({ typeFilter: 10 });
	let temp: DeviceRow[] = [];
	payload.forEach((item: DeviceRow) => {
		// 从设备通道中取设备类型 和 设备id，红外设备和监控设备有且仅有一个通道
		if (item.channels && item.channels.length > 0) {
			const { id, sourceType, coordinateType, longitude, latitude } = item.channels[0];

			// 检查经纬度是否有效
			if (
				longitude != null &&
				latitude != null &&
				longitude != 0 &&
				latitude != 0 &&
				!isNaN(longitude) &&
				!isNaN(latitude)
			) {
				item.id = id;
				item.sourceType = sourceType;
				item.onlineStatus = item.channels[0].onlineStatus;
				item.name = item.channels[0].name;
				item.icon = getDeviceIcon(item);
				// `coordinateType` 坐标系类型 0：bd09 1：wgs84 2：GCJ02，判断是否转坐标系
				item.coordinateType = coordinateType;
				item.location = transCoordinate([longitude, latitude], coordinateType, 2);

				temp.push(item);
			}
		}
	});
	deviceList.value = temp;

	setMapFitView();
};

useInterval(getDeviceList, 30 * 60 * 1000); // 30分钟
getDeviceList();
const initMap = (instance: any) => {
	map.value = instance;
};

const onMapZoomChange = () => {
	if (map.value) {
		const currentZoom = map.value.getZoom();
		console.log(currentZoom);
		zoom.value = currentZoom as number;
	}
};

const { state: heatMapState, heatmapLayerStyle, getHeatMapData } = useHeatMap();

import { getDevSpeciesFrequencyRank } from '/@/api/sd/dataScreen/bird';
// # 点击设备marker，显示window窗口
const onDevMarkerClick = async (device: DeviceRow) => {
	windowInfoState.device = device;
	windowInfoState.title = '设备详情';
	windowInfoState.visible = true;
	windowInfoState.position = device.location ?? [0, 0];
	rankState.rankList = [];
	try {
		rankState.getRankListLoading = true;
		const query = createQuery({
			device: device.num || device.deviceNum,
			timeType: timeType.value,
		});
		const { payload } = await getDevSpeciesFrequencyRank(query);
		rankState.rankList = payload;
		rankState.getRankListLoading = false;
		//查看设备详情
	} catch (error) {
		rankState.getRankListLoading = false;
	}
};

// # 物种飞行轨迹曲线，仅在鸟类标签页，且选中某物种时展示
const initLoca = (loca: any) => {
	loca.animate.start();
};
const pulseLinkSourceData: any = ref([]);
const getLayerStyle = (lineColor: string[]) => {
	return {
		unit: 'meter',
		dash: [4000, 0, 4000, 0],
		lineWidth(index: number, feat: any) {
			const lineWidth = feat.link.properties.lineWidthRatio;
			return [lineWidth[0], lineWidth[1]];
		},
		height(index: number, feat: any) {
			return feat.distance / 2;
		},
		smoothSteps: 50,
		speed(index: number, feat: any) {
			return feat.distance / 2;
		},
		flowLength: 10,
		lineColors: lineColor,
		maxHeightScale: 0.5, // 弧顶位置比例
		headColor: 'rgba(255, 255, 255, 1)',
		trailColor: 'rgba(255, 255, 255, 0)',
	};
};
const getLocusLine = () => {
	pulseLinkSourceData.value = [];
	const query = createQuery({
		timeType: timeType.value,
		eventType: eventType.value,
	});
	getDevSpecieslocus(query).then(({ payload }) => {
		const transformedData = transformLocusLineData(payload);

		Object.values(transformedData).forEach((trans: any) => {
			const obj = {
				source: {
					type: 'FeatureCollection',
					features: [] as any,
				},
				style: {},
			};
			trans.forEach((tr: any) => {
				obj.style = getLayerStyle(tr.lineColor);
				obj.source.features.push({
					type: 'Feature',
					properties: {
						lineWidthRatio: tr.speciesFrequency,
					},
					geometry: {
						type: 'LineString',
						coordinates: tr.LatLonList,
					},
				});
			});
			pulseLinkSourceData.value.push(obj);
		});

		nextTick(() => {
			if (map.value) {
				map.value.setFitView(undefined, true, [10, 10, 10, 10]);
			}
		});
	});
};
/**
 * 物种飞行轨迹按天统计，每天都可能存在0、1个或多个点位，此处只记录存在有效点位的日期
 * n天监测到物种，n天中的每个点位都算作起点，n+1中的每个点位都为n的终点，以此类推；即两个数据集进行两两组合，形成多条轨迹线
 * 同物种的多条轨迹，用一种颜色表示
 * 提前为每个点位都定义一个偏移坐标，避免起点与终点相同的情况
 */
function transformLocusLineData(data: Track[]) {
	const result: any = {};
	data.forEach((entry: Track) => {
		if (entry.LatLonList) {
			const { LatLonList, speciesName, speciesFrequency } = entry;
			if (!result[speciesName]) {
				result[speciesName] = [];
			}
			result[speciesName].push({
				LatLonList: JSON.parse(LatLonList).map((item: number[]) => {
					const originPos = transCoordinate([item[0], item[1]], item[2], 2);
					// 为每个点位定义偏移坐标（沿x轴向右偏移50像素）
					const [originX, originY] = map.value?.lngLatToCoords(originPos) ?? [0, 0];
					const offsetPos = map.value?.coordsToLngLat([originX + 50, originY]) ?? [0, 0];
					return {
						originPos: originPos,
						offsetPos: Array.isArray(offsetPos)
							? offsetPos
							: [offsetPos.getLng(), offsetPos.getLat()],
					};
				}),
				speciesFrequency,
			});
		}
	});

	const finalResult: any = {};
	for (const species in result) {
		const entries = result[species];
		if (entries.length < 2) continue;

		// 为每个物种随机生成一种链接线颜色数组
		const lineColor = [
			`rgb(${randomInteger(0, 255)}, ${randomInteger(0, 255)}, ${randomInteger(0, 255)})`,
		];

		const mergedEntries = [];
		for (let i = 0; i < entries.length - 1; i++) {
			const current = entries[i];
			const next = entries[i + 1];
			// 两两组合
			for (let ci = 0; ci < current.LatLonList.length; ci++) {
				for (let ni = 0; ni < next.LatLonList.length; ni++) {
					const startPos = current.LatLonList[ci].originPos;
					let endPos = next.LatLonList[ni].originPos;
					// 若起点与终点相同，此时把偏移坐当作终点坐标
					if (JSON.stringify(startPos) === JSON.stringify(endPos)) {
						endPos = next.LatLonList[ni].offsetPos;
					}
					mergedEntries.push({
						LatLonList: [startPos, endPos],
						speciesFrequency: [current.speciesFrequency, next.speciesFrequency],
						lineColor: lineColor,
					});
					// 下一次的起点为上一次的终点
					// next.LatLonList[ni].originPos = endPos;
				}
			}
		}
		finalResult[species] = mergedEntries;
	}
	return finalResult;
}

onUnmounted(() => {
	map.value?.destroy();
});

watch(
	[timeType, eventType],
	() => {
		getHeatMapData({
			timeType: timeType.value,
			eventType: eventType.value,
		});
		// getLocusLine();
	},
	{ immediate: true }
);

// 添加标记点选择器状态
const markerSelector = reactive({
	visible: false,
	nearbyMarkers: [] as DeviceRow[],
	clickEvent: null as any,
});

// 点击标记时的处理函数
const handleMarkerClick = (device: DeviceRow, event: any) => {
	// 确保location存在
	if (!device.location) return;

	// 查找附近的设备标记点
	const nearbyMarkers = findNearbyMarkers(device.location as [number, number]);

	// 如果附近有多个标记点
	if (nearbyMarkers.length > 1) {
		windowInfoState.visible = true;
		markerSelector.visible = true;
		markerSelector.nearbyMarkers = nearbyMarkers;
		windowInfoState.position = device.location as [number, number];
		markerSelector.clickEvent = event;

		// 阻止事件冒泡和默认行为
		if (event) {
			event.preventDefault?.();
			event.stopPropagation?.();
		}
	} else {
		// 只有一个标记点，直接处理
		onDevMarkerClick(device);
	}
};

/**
 * 查找附近的标记点
 * @param location 当前设备坐标
 * @returns 附近设备列表
 */
const findNearbyMarkers = (location: [number, number]) => {
	// 创建起点坐标对象
	const startLngLat = new AMap.LngLat(location[0], location[1]);

	// 距离阈值（米），根据缩放级别动态调整
	// 缩放级别越大，阈值越小，反之越大
	const thresholdInMeters = 50 / Math.pow(2, zoom.value - 10); // 在10级缩放时约为50米

	console.log(thresholdInMeters);

	return deviceList.value.filter((device) => {
		if (!device.location) return false;

		// 创建终点坐标对象
		const endLngLat = new AMap.LngLat(device.location[0], device.location[1]);

		// 使用高德API计算两点距离（单位：米）
		const distance = startLngLat.distance(endLngLat);

		return distance < thresholdInMeters;
	});
};

// 从选择器中选择标记点
const selectMarker = (device: DeviceRow) => {
	windowInfoState.visible = false;
	markerSelector.visible = false;

	// 调用原有的点击处理函数
	onDevMarkerClick(device);
};
</script>

<style lang="scss" scoped>
@import '/@/screen/styles/dataScreen.scss';

.map-container {
	width: 100%;
	height: 100%;
}
.el-vue-amap-container {
	width: 100%;
	height: 100%;
	position: relative;
}

.marker-selector-container {
	width: vw(280);

	.info-window-container {
		padding: vw(8) vw(12);
		max-height: vw(200);
		overflow-y: auto;

		/* 自定义滚动条 - 保持原有风格 */
		&::-webkit-scrollbar {
			width: vw(3);
		}

		&::-webkit-scrollbar-track {
			background: rgba(255, 255, 255, 0.05);
		}

		&::-webkit-scrollbar-thumb {
			background: rgba(152, 247, 215, 0.6); /* 使用原有的 $sd-screen-primary 色 */
			border-radius: vw(2);

			&:hover {
				background: rgba(152, 247, 215, 0.8);
			}
		}

		.selector-item {
			display: flex;
			align-items: center;
			padding: vw(8) vw(6);
			margin-bottom: vw(4);
			color: #fff;
			cursor: pointer;
			border-radius: vw(3);
			background: rgba(255, 255, 255, 0.03);
			border: 1px solid rgba(255, 255, 255, 0.05);
			transition: all 0.25s ease;
			position: relative;
			// span {
			// 	white-space: nowrap;
			// 	overflow: hidden;
			// 	text-overflow: ellipsis;
			// }

			&:hover {
				background: linear-gradient(
					to right,
					rgba(22, 226, 231, 0),
					rgba(33, 227, 221, 0.1) 20%,
					rgba(79, 229, 181, 0.3) 50%,
					rgba(118, 231, 146, 0.1) 80%,
					rgba(136, 232, 130, 0)
				);
			}

			&:last-child {
				margin-bottom: 0;
			}

			.small-icon {
				width: vw(26);
				height: vw(17);
				margin-right: vw(10);
				border-radius: vw(2);
				object-fit: contain;
				filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
			}

			/* 添加设备状态指示器 */
			&::after {
				content: '';
				position: absolute;
				top: 50%;
				right: vw(8);
				width: vw(6);
				height: vw(6);
				border-radius: 50%;
				transform: translateY(-50%);
				background: #37ff9d; /* 在线状态使用 $sd-screen-success */
				box-shadow: 0 0 vw(4) rgba(55, 255, 157, 0.5);
			}

			/* 离线状态 */
			&.offline::after {
				background: rgba(255, 255, 255, 0.3);
				box-shadow: none;
			}

			span {
				flex: 1;
				font-size: vw(12);
				font-weight: 400;
				line-height: 1.3;
				color: rgba(255, 255, 255, 0.9);
				margin-right: vw(15);
			}

			&:hover span {
				color: #98f7d7; /* 悬停时使用 $sd-screen-primary 色 */
			}
		}
	}
}

.nmg {
	width: vw(280);

	.info-window-device-info {
		padding: 0 vw(20);

		.info-window-device-name {
			font-size: vw(16);
			margin-top: vw(15);
			color: #fff;
		}
		:deep(.el-link.is-underline:hover:after) {
			border-bottom: 1px solid #fff;
		}
		.info-window-device-status {
			display: flex;
			align-items: center;
			font-size: vw(14);
			.device-status-img {
				width: vw(15);
				height: vw(15);
				margin-right: vw(8);
			}
		}
		.info-window-device-status-line {
			width: 100%;
			height: vw(1);
			border: 1px solid rgba(31, 134, 106, 0.3);
			margin: vw(20) 0;
		}
	}
	.more-info {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 vw(20) vw(4);
		.more-icon {
			width: vw(22);
			height: vh(16);
			cursor: pointer;
		}
	}
}

:deep(.el-loading-mask) {
	.path {
		stroke: #68cfad;
	}
}

:deep(.amap-toolbar) {
	right: 22vw !important;
	z-index: 999;
}

:deep(.amap-maptype) {
	bottom: 200px !important;
	right: 22vw !important;
	z-index: 999;
}
</style>

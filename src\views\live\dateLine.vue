<template>
	<div class="timeline" v-if="!isMobile">
		<div class="timeline-95" ref="timeline">
			<div class="timeline-middle">
				<div
					v-for="(event, index) in events"
					:key="event.recTime"
					class="event"
					:style="{ left: event.position + 'px' }"
				>
					<img :src="event.markerUrl" alt="" @click.stop="handleEventClick($event, event)" />
					<span v-if="event.labelShow" class="event-time">{{ formatPast(event.recTime) }}</span>
				</div>
			</div>
		</div>
		<div class="detail-dig" v-if="data.visible" :style="{ left: data.left }">
			<span class="colse" @click="handleClose()"></span>
			<el-image
				style="width: 100%; height: 169px"
				:src="data.picture"
				:zoom-rate="1.2"
				:max-scale="7"
				:min-scale="0.2"
				:initial-index="0"
				:preview-src-list="[data.picture]"
				fit="cover"
			>
				<template #placeholder>
					<div class="image-placeholder">
						<el-icon class="is-loading">
							<ele-Loading />
						</el-icon>
					</div>
				</template>
				<template #error>
					<div class="load-error">
						<img
							class="small-img-error"
							src="/src/assets/fullScreen/small-load-error.png"
							title="加载失败"
							alt=""
						/>
					</div>
				</template>
			</el-image>

			<span>{{ data.pointName }}{{ data.recResult }}</span>
			<span class="dig-bottom">
				<span class="dig-type">
					<img src="/src/assets/fullScreen/type.png" alt="" style="width: 15px; height: 15px" />
					{{ data.parentEventTypeName }}</span
				>
				<span>{{ data.timePast }}</span>
			</span>
		</div>
	</div>

	<div v-else class="event-warning-mobile">
		<div class="event-warning-item" v-for="item in liveEventList" :key="item.id">
			<div class="rec">
				<div class="rec-result">{{ item.pointName }}{{ item.recResult }}</div>
				<div class="rec-time">{{ formatPast(item.recTime) }}</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed, onUnmounted } from 'vue';
import { liveInfo } from '/@/stores/fullScreen';
import { storeToRefs } from 'pinia';
import { useRouter, useRoute } from 'vue-router';
import { getMonitorEvent } from '/@/api/home';
import { formatPast } from '/@/utils/formatTime';
import other from '/@/utils/other';
const isMobile = ref(other.isMobile());
const timeline = ref();
const events = ref<any>([]);
const DigVisible = ref(false);
const liveStore = liveInfo();
const { liveId, liveEventList } = storeToRefs(liveStore);

const router = useRouter();
const route = useRoute();
const currentPageLiveId = computed(() => {
	return liveId.value || route.query.liveId;
});
const data = reactive({
	visible: false,
	timePast: '',
	recResult: '',
	parentEventTypeName: '',
	pointName: '',
	picture: '',
	left: '',
});

const createTimeline = (startTime: any, endTimeTime: any) => {
	const timelineWidth = timeline.value.clientWidth;
	const totalMinutes = (endTimeTime - startTime) / (60 * 1000);
	const pixelsPerMinute = timelineWidth / totalMinutes; // 计算每分钟的像素数：

	let lastPosition = 0;
	events.value.forEach((event: any, index: number) => {
		const eventTime = new Date(event.recTime) as any;
		// console.log('eventTime', eventTime);
		event.position = ((eventTime - startTime) / (60 * 1000)) * pixelsPerMinute;
		event.labelShow = true;

		if (index !== 0 && lastPosition - event.position < 50) {
			// 如果两个事件点之间的距离小于
			event.labelShow = false; // 时间label不显示
		}
		lastPosition = event.position;
	});
};

const handleEventClick = (el: any, event: any) => {
	const eventDiv = el.currentTarget.parentElement;
	const digWidth = 334;
	const timelineWidth = timeline.value.clientWidth;
	let left = parseFloat(eventDiv.style.left) - digWidth * 0.5 + 5;
	if (left < 0) left = 0;
	else if (left > timelineWidth - digWidth) left = timelineWidth - digWidth;
	console.log(timelineWidth);
	console.log(left);
	event.timePast = formatPast(event.recTime);
	data.visible = true;
	data.timePast = event.timePast;
	data.recResult = event.recResult;
	data.parentEventTypeName = event.parentEventTypeName;
	data.pointName = event.pointName;
	data.picture = event.picture;
	data.left = `${left}px`;
};
const handleClose = () => {
	data.visible = false;
};

const getLiveEvents = async () => {
	const limit = isMobile.value ? 3 : 10;
	await liveStore.setEventList(false, false, limit);

	if (isMobile.value) return;
	let maxTime: number | null = null;
	let minTime: number | null = null;
	liveEventList.value.forEach((item, index) => {
		const itemDate = parseDate(item.recTime);
		if (maxTime === null || itemDate > maxTime) {
			maxTime = itemDate;
		}
		if (minTime === null || itemDate < minTime) {
			minTime = itemDate;
		}
	});
	events.value = liveEventList.value;
	// console.log(`最早时间向下取整后的时间: ${minTime}`);
	// console.log(`最晚时间向上取整后的时间: ${maxTime}`);
	createTimeline(minTime, maxTime); // 确保这个函数存在并且正确使用
};
const parseDate = (timeStr: string): any => new Date(timeStr);
let eventInterval: NodeJS.Timeout | null = null;

onMounted(() => {
	if (!liveEventList.value.length) {
		getLiveEvents();
	}

	eventInterval = setInterval(() => {
		getLiveEvents();
	}, 10000);
});
onUnmounted(() => {
	eventInterval && clearInterval(eventInterval);
});
</script>

<style scoped lang="scss">
.timeline {
	-webkit-user-select: none; /* Safari */
	-moz-user-select: none; /* Firefox */
	-ms-user-select: none; /* IE/Edge */
	user-select: none; /* 标准语法 */
	position: relative;
	width: 100%;
	height: 75px;
	display: flex;
	justify-content: center;
	.detail-dig {
		position: absolute;
		top: -262px;
		left: -167px;
		z-index: 9999;
		width: 334px;
		height: 260px;
		padding: 20px 21px 15px;
		background-image: url('/src/assets/live/time-line-dig.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		color: #fff;
		display: flex;
		flex-direction: column;

		.image-placeholder {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			.is-loading {
				color: #22d69f;
				font-size: 20px;
			}
		}

		.colse {
			position: absolute;
			top: 5px;
			right: 5px;
			width: 10px;
			height: 10px;
			z-index: 999;
			background-image: url('/src/assets/eventDetails/dig/close.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			cursor: pointer;
		}

		.el-image {
			margin-bottom: 9px;
		}
		.dig-bottom {
			display: flex;
			margin-top: 8px;
			span {
				&:last-child {
					color: rgba(202, 202, 202, 1);
				}
			}
		}
		.dig-type {
			display: flex;
			align-items: center;
			margin-right: 30px;
			img {
				margin-right: 5px;
			}
		}
	}
	.timeline-95 {
		display: flex;
		align-items: center;
		width: calc(100% - 20px);
		height: 100%;
		display: flex;
	}
	.timeline-middle {
		width: 100%;
		height: 5px;
		transform: translateY(-50%);
		background-color: rgba(70, 146, 120, 0.34);
	}
}

.event {
	min-width: 21px;
	position: absolute;
	bottom: 50%;
	height: 31px;
	transform: translateX(-50%);
	z-index: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	pointer-events: auto;

	img {
		display: block;
		width: 21px;
		height: 31px;
		object-fit: contain;
		cursor: pointer;
	}
	.event-time {
		min-width: 50px;
		text-align: center;
		margin-top: 7px;
		color: #fff;
		font-size: 12px;
	}
}

.event-warning-mobile {
	position: relative;
	width: 100%;
	height: 36px;
	margin-bottom: 10px;
	background-image: url('/src/assets/live/event-warning.png');
	background-repeat: no-repeat;
	background-size: 100% 100%;

	padding-top: 36px;
	color: #fff;
	&::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 8px;
		transform: translateY(-50%);
		width: 106px;
		height: 29px;
		color: #fff;
		background-image: url('/src/assets/live/event-warning-name.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
	}
	.event-warning-item {
		width: 100%;
		display: block;
		margin-top: 12px;
		.rec {
			color: #fff;
			font-size: 16px;
			display: flex;
			justify-content: space-between;
			align-items: flex-end;
			padding: 0 16px;
		}
	}
}
</style>

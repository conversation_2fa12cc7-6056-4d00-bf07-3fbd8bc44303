<template>
	<div id="ol-map-container" style="width: 100%; height: 100%;">
    <div class="map-rl-controls">
      <div class="rl-controls-zoom">
        <el-button class="rl-controls-layer-btn" circle color="#005E61" title="放大" @click="onZoom('in')">
          <template #icon>
            <el-icon><ele-Plus /></el-icon>
          </template>
        </el-button>
      </div>
      <div class="rl-controls-zoom">
        <el-button class="rl-controls-layer-btn" circle color="#005E61" title="缩小" @click="onZoom('out')">
          <template #icon>
            <el-icon><ele-Minus /></el-icon>
          </template>
        </el-button>
      </div>
      <div class="rl-controls-layer">
        <el-button class="rl-controls-layer-btn" circle color="#005E61" title="切换图层" @click="mapTypeShow = !mapTypeShow">
          <img src="/src/assets/sd/data-screen/map-rb-layer.png" alt="">
        </el-button>
        <div class="rl-controls-layer-types" v-show="mapTypeShow">
          <div
            class="map-type"
            :class="activeMapIndex === $index ? 'is-active':''"
            v-for="(item, $index) in mapTypes"
            :key="item.key"
            @click="onMapTypeChange(item, $index)"
          >
            <div><img :src="item.coverImage" alt=""></div>
            <div class="label">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </div>
    <RealTimeVideoWindow ref="realTimeVideoWindowRef"></RealTimeVideoWindow>
    <FrequencyRankWindow ref="frequencyRankWindowRef"></FrequencyRankWindow>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, watch, computed } from 'vue';
import { generatePlayUrl } from '/@/api/devices';
import { getDevSpeciesFrequency, getDevSpecieslocus } from '/@/api/sd/dataScreen/bird';
import { IsCompanyNmg } from '/@/hooks/useConfig';
import { calculateWindowOffset, randomInteger, createBezierCurvePoints, transCoordinate } from './utils';
// 设备点位图标
import infraredIcon from '/@/assets/sd/data-screen/point-infrared.png';
import infraredIconActive from '/@/assets/sd/data-screen/point-infrared-active.png';
import monitoringIcon from '/@/assets/sd/data-screen/point-monitoring.png';
import monitoringIconActive from '/@/assets/sd/data-screen/point-monitoring-active.png';
// 组件
import RealTimeVideoWindow from './realTimeVideoWindow.vue';
import FrequencyRankWindow from './frequencyRankWindow.vue';
// 各湿地边界json
import xmPolygonJson from './polygonJson/xm.json' // 锡盟（gps84）
/**
 * 投影坐标系：EPSG:4326（WGS84）、EPSG:3857（墨卡托投影坐标）
 */
import { Map, View, Feature } from 'ol';
import { Coordinate } from 'ol/coordinate';
import { Vector as VectorLayer, Heatmap } from 'ol/layer';
import { Vector as VectorSource } from 'ol/source.js';
import { Point, LineString } from 'ol/geom.js';
import { Style, Icon, Text, Fill, Stroke, Circle as CircleStyle } from 'ol/style.js';
import 'ol/ol.css';
// 地图图层
import TiandituLayers, { tiandituKey } from './layers/tianditu'; // gps84
import GaodeLayers, { gaodekey } from './layers/gaode'; // gcj02
// import BaiduLayers from './layers/baidu'; // bd09

const props = defineProps<{
	animalTag?: number;
	animalNameList?: string[];
	timeType: number | null;
}>();

let map: any;
let mapZoom = 10;
const mapCenter: [number, number] = [0, 0];
let isFirstRender = true; // 是否为首次渲染

onMounted(() => {
  initMap();
  getHeatMapData();
});

// # 初始化地图
const initMap = () => {
  map = new Map({
    layers: mapTypes[activeMapIndex.value].layers,
    target: 'ol-map-container',
    view: new View({
      center: mapCenter,
      projection: 'EPSG:4326',
      zoom: mapZoom,
      maxZoom: 20,
    }),
  });
  bindMapEvent();
};
// 地图放大、缩小
const onZoom = (action: string) => {
  const view = map.getView();
  const zoom = view.getZoom();
  if (action === 'in') {
    view.animate({
      zoom: zoom + 1,
      duration: 500,
    });
  } else {
    view.animate({
      zoom: zoom - 1,
      duration: 500,
    });
  }
};
// 获取地图中已存在的图层
const getLayerByName = (layerName: LayerName): (VectorLayer | Heatmap | null) => {
  if (!map) return null;
  const layers = map.getLayers(); // 地图所有图层
  let layer = null;
  layers.forEach((item: any) => {
    if (item.getProperties().name === layerName) {
      layer = item;
    }
  })
  return layer;
};
// 调整地图视野；layerName存在，调整视野到指定图层；否则调整视野到湿地边界（优先）图层或设备图层；
const setMapViewport = (layerName?: LayerName) => {
  let layer = null
  if (!layerName) {
    layer = getLayerByName('areaPolyline') || getLayerByName('device');
  } else {
    layer = getLayerByName(layerName);
  }
  if (layer) {
    map.getView().fit(
      layer.getSource()?.getExtent(), 
      { nearest: true, padding: [160, 300, 160, 300]}
    );
  }
};

// # 地图类型
const activeMapIndex = ref(0); // 选中的地图类型下标
const mapTypes = [
  {
    key: '高德',
    name: '高德地图',
    coverImage: `https://restapi.amap.com/v3/staticmap?location=116.37359,39.92437&zoom=10&size=440*280&key=${gaodekey}`,
    layers: GaodeLayers,
    cooordinateType: 2,
  },
  {
    key: '天地图',
    name: '天地图',
    coverImage: `http://api.tianditu.gov.cn/staticimage?center=116.40,39.93&width=400&height=300&zoom=10&tk=${tiandituKey}`,
    layers: TiandituLayers,
    cooordinateType: 1,
  },
];
const mapTypeShow = ref(false);
const mapCoordinateType = computed(() => { // 当前渲染的地图坐标类型
  return mapTypes[activeMapIndex.value].cooordinateType;
});
// 切换底图
const onMapTypeChange = (mapTypeItem: any, $index: number) => {
  if (activeMapIndex.value === $index) return;
  activeMapIndex.value = $index;
  // 清除已经存在的图层，并添加新图层
  map.setLayers(mapTypeItem.layers);
  mapTypeShow.value = false;
  renderMapOverlays();
};
// 渲染地图上的所有覆盖物
const renderMapOverlays = () => {
  renderSDPolyline(); // 湿地边界
  renderDevicePoints(); // 设备
  renderHeatMap(); // 热力图
  renderTrackLines(); // 轨迹线
}
// 为地图绑定事件，如加载结束、点击
const bindMapEvent = () => {
  map.on('loadend', () => {
    isFirstRender && renderMapOverlays();
  });
  map.on('click', function(evt: { pixel: any; originalEvent: MouseEvent; }) {
    // 判断当前单击处是否有要素，即设备
    const feature = map.forEachFeatureAtPixel(evt.pixel, function (feature: any) {return feature});
    if (feature) {
      onDevMarkerClick(evt.originalEvent, feature);
    }
  });
};

// # 渲染各湿地边界
const renderSDPolyline = () => {
  let areaPolylinePath = [];
  // 锡盟
  if (IsCompanyNmg) {
    const coordinates = xmPolygonJson.features[0].geometry.coordinates[0];
    areaPolylinePath = coordinates.map((coord: [number, number]) => {
      return transCoordinate(coord, 1, mapCoordinateType.value)
    })
  }
  if (areaPolylinePath.length === 0) return
  let areaPolylineLayer = getLayerByName('areaPolyline');
  if (!areaPolylineLayer) {
    areaPolylineLayer = new VectorLayer({
      source: new VectorSource(),
      zIndex: 10,
    });
    areaPolylineLayer.set('name', 'areaPolyline');
    map.addLayer(areaPolylineLayer);
  } else {
    areaPolylineLayer.getSource()?.clear();
  }
  // 为areaPolylineLayer图层添加元素
  const lineFeature = new Feature({
    type: 'lineFeature',
    geometry: new LineString(areaPolylinePath),
  });
  lineFeature.setStyle(
    new Style({
      stroke: new Stroke({
        color: '#37FF9D',
        width: 4,
      }),
    })
  );
  areaPolylineLayer.getSource()?.addFeature(lineFeature);
  setMapViewport('areaPolyline');
}

// # 地图上渲染设备，`红外设备sourceType = 2` 和 `监控设备sourceType = 3`
const deviceList = inject<any>('deviceList');
// （设备）图文样式
const createIconStyle = (text: string, sourceType: number, tag = 'default') => {
  let iconSrc: string;
  if (sourceType === 2) {
    iconSrc = (tag === 'default' ? infraredIcon : infraredIconActive);
  } else {
    iconSrc = (tag === 'default' ?  monitoringIcon : monitoringIconActive);
  }
  return new Style({
    image: new Icon({
      scale: 0.5,
      src: iconSrc,
      anchor: [0.5, 1]
    }),
    text: new Text({
      textAlign: 'center',
      offsetY: 16,
      text,
      font: '10px sans-serif',
      fill: new Fill({
        color: '#fff',
      }),
      stroke: new Stroke({ color: '#ffffff', width: 1 })
    }),
  });
};
const renderDevicePoints = () => {
  let deviceLayer = getLayerByName('device');
  // 设备图层
  if (!deviceLayer) {
    deviceLayer = new VectorLayer({
      source: new VectorSource(),
      zIndex: 20,
    });
    deviceLayer.set('name', 'device');
    map.addLayer(deviceLayer);
  } else {
    deviceLayer.getSource()?.clear();
  }
  // 为deviceLayer图层添加元素
  const markerFeatures: Feature<Point>[] = [];
  for(let i = 0; i < deviceList?.value.length; i++) {
    const device = deviceList?.value[i];
    if (device.longitude) {
      const position = transCoordinate(
        [device.longitude, device.latitude],
        device.coordinateType,
        mapCoordinateType.value
      );
      const mf = new Feature({
        geometry: new Point(position),
        name: device.name,
        device: device,
      });
      mf.setStyle(createIconStyle(device.name, device.sourceType));
      markerFeatures.push(mf);
    }
  }
  deviceLayer.getSource()?.addFeatures(markerFeatures);
  // 渲染设备点位时，调整地图到合适的范围，保证所有设备点都显示；
  setMapViewport();
  isFirstRender = false;
};
// 点击设备marker，显示window窗口
const realTimeVideoWindowRef = ref<InstanceType<typeof RealTimeVideoWindow>>();
const frequencyRankWindowRef = ref<InstanceType<typeof FrequencyRankWindow>>();
const onDevMarkerClick = async (event: MouseEvent, feature: any) => {
  const device = feature.get('device');
  if (!device) return;

  const closeCallback = () => {
    feature.setStyle(
      createIconStyle(device.name, device.sourceType)
    );
  }
  // 弹窗位置跟随鼠标
  const { eventX, eventY, panX, panY } = calculateWindowOffset(event);
  // 1、内蒙古（锡盟）项目，查看物种频次排行
  if (IsCompanyNmg) {
    panByPixel(panX, panY);
    feature.setStyle(
      createIconStyle(device.name, device.sourceType, 'active')
    );
    frequencyRankWindowRef.value?.openDialog(
      {
        eventX,
        eventY,
        device: device.num,
        animalNameList: props.animalNameList,
        animalTag: props.animalTag,
        timeType: <number>props.timeType,
      },
      closeCallback,
    );
  } else { 
  // 2、其他，查看实时视频，仅监控设备 `sourceType = 3` 
    if (device.sourceType !== 3) return;
    panByPixel(panX, panY);
    feature.setStyle(
      createIconStyle(device.name, device.sourceType, 'active')
    );
    realTimeVideoWindowRef.value?.openDialog(
      {
        eventX,
        eventY,
        playUrl: generatePlayUrl(device.id),
      },
      closeCallback,
    );
  }
};
// 按像素平移地图，x：横向平移距离(向右为正) y：纵向平移距离(向下为正);
const panByPixel = function (x: number, y: number, duration = 150) {
  if (x === 0 && y === 0) return;
  const mapCenter = map.getView().getCenter(); // 当前地图中心点（投影坐标）
  var mapCenterPixel = map.getPixelFromCoordinate(mapCenter); // 投影坐标转像素坐标
  var newx = mapCenterPixel[0] - x;
  var newy = mapCenterPixel[1] - y;
  map.getView().animate({
    center: map.getCoordinateFromPixel([newx, newy]), // 平移后的像素坐标转投影坐标
    duration: duration,
    zoom: map.getView().getZoom(),
  });
}

// # （设备物种频次）热力图
const heatMapData = ref([]);
let maxCount = 0;
const getHeatMapData = async () => {
	const query = {
		animalNameList: props.animalNameList,
		animalTag: props.animalTag,
		timeType: props.timeType,
	};
	const { payload } = await getDevSpeciesFrequency(query);
	const data: any = [];
  maxCount = 0;
	payload.forEach((item: any) => {
		if (item.longitude && item.latitude && item.speciesFrequency > 0) {
      data.push(item);
		}
    if (maxCount < item.speciesFrequency) maxCount = item.speciesFrequency;
	});
  heatMapData.value = data;
  renderHeatMap();
};
const renderHeatMap = () => {
  let heatmapLayer= getLayerByName('heatmap');
  if (!heatmapLayer) {
    // 创建一个Heatmap图层
    heatmapLayer = new Heatmap({
      source: new VectorSource(),
      gradient: ['#00f','#0ff','#0f0','#ff0','#f00'],
      radius: 20, // 热点半径
      blur: 20, // 模糊尺寸
      opacity: 1,
      zIndex: 10,
      weight: (feature) => {
        const weight = feature.getProperties().count / maxCount;
        return weight < 0.1 ? 0.1 : weight;
      },
    });
    heatmapLayer?.set('name', 'heatmap');
    map.addLayer(heatmapLayer);
  } else {
    heatmapLayer.getSource()?.clear();
  }
  // 为Heatmap图层添加元素
  const pointFeatures: Feature<Point>[] = [];
  heatMapData.value.forEach((item: any) => {
    const { longitude, latitude, coordinateType, speciesFrequency } = item;
    const position = transCoordinate([longitude, latitude], coordinateType, mapCoordinateType.value);
    pointFeatures.push(
      new Feature({
        geometry: new Point(position),
        count: speciesFrequency,
      })
    );
  });
  heatmapLayer.getSource()?.addFeatures(pointFeatures);
};

// # 物种飞行轨迹曲线，仅在鸟类标签页，且选中某物种时展示
let trackLineData: Track[] = [];
let pulsePoints: Feature<Point>[] = []; // 所有物种轨迹的脉冲点集合
const getTrackLines = async () => {
  // 清除轨迹
  trackLineData = [];
  pulsePoints = [];
	if (props.animalTag === undefined) return;
  if (props.animalNameList?.length === 0) {
    unbiudTrackLineEvent();
    return;
  }
	const query = {
		animalNameList: props.animalNameList,
		animalTag: props.animalTag,
		timeType: props.timeType,
	};
	const { payload } = await getDevSpecieslocus(query);
  trackLineData = payload;
  renderTrackLines();
};
/**
 * 物种飞行轨迹按天统计，每天都可能存在0、1个或多个点位，此处只记录存在有效点位的日期
 * n天监测到物种，n天中的每个点位都算作起点，n+1中的每个点位都为n的终点，以此类推；即两个数据集进行两两组合，形成多条轨迹线
 * 同物种的多条轨迹，用一种颜色表示
 * 提前为每个点位都定义一个偏移坐标，避免起点与终点相同的情况
 */
function transTrackLineData(data: Track[]) {
	const result: TrackResult = {};
	data.forEach((entry: Track) => {
		if (entry.LatLonList) {
			const { LatLonList, speciesName, speciesFrequency } = entry;
			if (!result[speciesName]) {
				result[speciesName] = [];
			}
			result[speciesName].push({
				LatLonList: JSON.parse(LatLonList).map((item: number[]) => {
          const position = transCoordinate([item[0], item[1]], item[2], mapCoordinateType.value);
          // 为每个点位定义偏移坐标（沿x轴向右偏移0.0001度）
          // map.getView().getResolution()
          return {
            originPos: position,
            offsetPos: [position[0] + 0.0001, position[1]],
          }
        }),
				speciesFrequency: speciesFrequency,
			});
		}
	});

	const finalResult: TrackFinalResult = {};
	for (const species in result) {
    const entries = result[species];
    if (entries.length < 2) continue;

    // 为每个物种随机生成一种链接线颜色
    const lineColor = `rgb(${randomInteger(0, 255)}, ${randomInteger(0, 255)}, ${randomInteger(0, 255)})`;

    const mergedEntries = [];
    for (let i = 0; i < entries.length - 1; i++) {
      const current = entries[i];
      const next = entries[i + 1];
      // 两两组合
      for (let ci = 0; ci < current.LatLonList.length; ci++) {
        for (let ni = 0; ni < next.LatLonList.length; ni++) {
          const startPos = current.LatLonList[ci].originPos;
          let endPos = next.LatLonList[ni].originPos;
          // 若起点与终点相同，此时把偏移坐当作终点坐标
          if (JSON.stringify(startPos) === JSON.stringify(endPos)) {
            endPos = next.LatLonList[ni].offsetPos;
          }
          mergedEntries.push({
            LatLonList: [startPos, endPos],
            speciesFrequency: [current.speciesFrequency, next.speciesFrequency],
            lineColor: lineColor,
          });
        }
      }
    }
    finalResult[species] = mergedEntries;
	}

  // 返回物种轨迹对象，每个物种可能对应多条轨迹
	return finalResult;
}
const renderTrackLines = () => {
  let trackLineLayer = getLayerByName('trackLine');
  if (!trackLineLayer) {
    trackLineLayer = new VectorLayer({
      source: new VectorSource(),
    });
    trackLineLayer.set('name', 'trackLine');
    map.addLayer(trackLineLayer);
  } else {
    trackLineLayer.getSource()?.clear();
  }

  const finalResult: TrackFinalResult = transTrackLineData(trackLineData);
  if (JSON.stringify(finalResult) === '{}') {
    unbiudTrackLineEvent();
    return;
  }

  const tlFeatures: Feature<LineString | Point>[] = [];
  Object.values(finalResult).forEach((tl: TrackLine[]) => {
    let linePoints: Coordinate[] = [];
    tl.forEach((lineItem: TrackLine) => {
      // 轨迹
      const lineFeature = new Feature({
        type: 'lineFeature',
        geometry: new LineString(
          createBezierCurvePoints(lineItem.LatLonList[0], lineItem.LatLonList[1])
        ),
        count: lineItem.speciesFrequency,
      });
      lineFeature.setStyle(
        new Style({
          fill: new Fill({ color: lineItem.lineColor }),
          stroke: new Stroke({
            color: lineItem.lineColor,
            width: 5,
          }),
        })
      );
      tlFeatures.push(lineFeature);
      linePoints = linePoints.concat(lineFeature.getGeometry()?.getCoordinates() as Coordinate[]);
    });
    // 为某个物种创建脉冲点
    const pulsePoint = new Feature({
      type: 'pulseFeature',
      geometry: new Point(linePoints[0]),
      linePoints: linePoints, // 某个物种对应的运动轨迹
      lineIndex: 0, // 当前处于哪个坐标
      speed: 1, // 运动速度
    });
    pulsePoint.setStyle(
      new Style({
        image: new CircleStyle({
          fill: new Fill({
            color: '#ffffff',
          }),
          radius: 5,
        })
      })
    );
    pulsePoints.push(pulsePoint);
    tlFeatures.push(pulsePoint);
  });
  trackLineLayer.getSource()?.addFeatures(tlFeatures);
  // 调整地图到合适的范围，保证所有轨迹线都显示；
  setMapViewport('trackLine');
  bindTrackLineEvent('pulse');
};
// 轨迹脉冲点事件
let evenTag = '';
const bindTrackLineEvent = (tag: string) => {
  evenTag = tag;
  map && map.on('postcompose', moveFeature);
};
const unbiudTrackLineEvent = () => {
  evenTag = '';
  const trackLineLayer = getLayerByName('trackLine');
  if (trackLineLayer) {
    trackLineLayer.getSource()?.clear();
    map && map.un('postcompose', moveFeature);
  }
  setMapViewport();
};
// 脉冲点，轨迹动画
const moveFeature = () => {
  // console.log('============== moveFeature')
  if (evenTag === 'pulse') {
    pulsePoints.forEach((pulseFeature) => {
      const { linePoints, lineIndex, speed } = pulseFeature.getProperties();
      if (linePoints[lineIndex]) {
        pulseFeature?.getGeometry()?.setCoordinates(linePoints[lineIndex]);
      }
      const next = lineIndex + speed;
      pulseFeature.set('lineIndex', next >= linePoints.length ? 0 : next);
    });
    map.render();
  }
};

watch(
	[() => props.animalNameList, () => props.timeType],
	() => {
		getHeatMapData();
		getTrackLines();
	},
	{ immediate: true }
);
</script>

<style lang="scss" scoped>
#ol-map-container {
  width: 100%;
  height: 100%;
}

.map-rl-controls {
  position: absolute;
  right: calc(vw(402) + vw(40));
	bottom: 30px;
  z-index: 999;
  .rl-controls-zoom {
    margin-bottom: vw(10);
  }
  .rl-controls-layer {
    position: relative;
    .rl-controls-layer-btn {
      img {
        width: 20px;
      }
    }
    .rl-controls-layer-types {
      position: absolute;
      right: vw(50);
      bottom: 0;
      z-index: -1;
      background-color: $sd-screen-bg-color;
      display: flex;
      animation: MapTypeFadeIn 0.3s;
      .map-type {
        position: relative;
        border: 1px solid transparent;
        cursor: pointer;
        div {
          img {
            width: 100px;
            height: 80px;
            border-radius: 4px;
            vertical-align: middle;
          }
        }
        .label {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          text-align: center;
          line-height: 24px;
          background-color: rgba(0, 0, 0, 0.8);
          font-size: 12px;
        }
        &:hover,
        &.is-active {
          border-color: #37FF9D;
        }
      }
      .map-type + .map-type {
        margin-left: 10px;
      }
    }
  }
}
@keyframes MapTypeFadeIn {
  0% {
    transform: translateX(40px);
    opacity: 0;
  }
  30% {
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
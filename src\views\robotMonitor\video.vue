<template>
	<!-- <el-button style="position: absolute; left: 100px; top: 10px; z-index: 9999" @click="destroy">
			销毁
		</el-button> -->
	<div
		class="jessibuca-container"
		ref="container"
		@mousemove="handleMouseMove"
		@mouseout="handleMouseOut"
	>
		<img
			v-if="playShow"
			class="play"
			@click="getLivePlayUrl"
			src="/src/assets/live/bofang.png"
			alt=""
		/>

		<transition name="fade">
			<div class="jessibuca-control" v-show="controlVisible">
				<div class="left">
					<slot name="left"></slot>
				</div>
				<template v-for="control of controls">
					<div v-if="control.show" class="jessibuca-control-item" :key="control.name">
						<img
							:src="isMobile ? control.mobileUrl : control.pcUrl"
							alt=""
							:width="control.width"
							:height="control.height"
							@click="control.click"
						/>
					</div>
				</template>
			</div>
		</transition>
	</div>
</template>

<script lang="ts" setup name="JessibucaDemo">
import { ref } from 'vue';
import { onUnmounted, watch, computed, reactive } from 'vue';
import { getrobotMonitorPlay } from '/@/api/home';
import other from '/@/utils/other';
const emits = defineEmits([
	'load',
	'start',
	'play',
	'destroy',
	'fullScreen',
	'recordingTimestamp',
	'recordEnd',
	'recordStart',
]);
const props = defineProps<{
	channelNos: number | string;
	url: string;
	name: string;
	deviceId: number;
}>();
let mouseIdleTimer: NodeJS.Timeout | null = null;
let jessibuca: any = null;
const playShow = ref(true);
const container = ref(null);
const payUrl = ref<string | null>(null);
const showBandwidth = ref(false);
const showOperateBtns = ref(false);
const forceNoOffscreen = ref(false);
const playing = ref(false);
const quieting = ref(true);
const loaded = ref(false);
const controlVisible = ref(true);

const isMobile = computed<boolean>(() => {
	return other.isMobile();
});

type controlType = {
	name: string;
	click: () => void;
	mobileUrl: string;
	pcUrl: string;
	show: boolean;
	width: number;
	height: number;
};

const controls = reactive<controlType[]>([
	{
		name: '暂停',
		click: () => {
			destroy();
		},
		width: 22,
		height: 22,
		mobileUrl: other.getStaticImag('live/mobile/pause.png'),
		pcUrl: other.getStaticImag('live/pause.png'),
		show: false,
	},
	{
		name: '录制',
		click: () => {
			if (jessibuca) {
				jessibuca.startRecord();
			}
		},
		mobileUrl: other.getStaticImag('live/mobile/luping.png'),
		pcUrl: other.getStaticImag('live/luping.png'),
		show: false,
		width: 22,
		height: 16,
	},
	{
		name: '全屏',
		click: () => {
			emits('fullScreen', props.name);
		},
		mobileUrl: other.getStaticImag('live/mobile/full-screen.png'),
		pcUrl: other.getStaticImag('live/full-screen.png'),
		show: true,
		width: 20,
		height: 20,
	},
]);

mouseIdleTimer = setTimeout(() => {
	controlVisible.value = false;
}, 2000);
const handleMouseMove = () => {
	controlVisible.value = true;
	if (mouseIdleTimer) {
		clearTimeout(mouseIdleTimer);
	}
	mouseIdleTimer = setTimeout(() => {
		controlVisible.value = false;
	}, 2000);
};
const handleMouseOut = () => {
	if (mouseIdleTimer) {
		clearTimeout(mouseIdleTimer);
	}
	controlVisible.value = false;
};

const createJessibuca = () => {
	jessibuca = new (window as any).Jessibuca({
		container: container.value,
		videoBuffer: 1, // 缓存时长
		isResize: false,
		isFullResize: false,
		isFlv: true,
		text: '',
		heartTimeout: 5,
		decoder: '/jessibuca/decoder.js',
		// background: "bg.jpg",
		hasAudio: false,
		debug: false,
		showBandwidth: showBandwidth.value, // 显示网速
		operateBtns: {
			fullscreen: showOperateBtns.value,
			screenshot: showOperateBtns.value,
			play: showOperateBtns.value,
			audio: showOperateBtns.value,
			record: showOperateBtns.value,
		},
		forceNoOffscreen: forceNoOffscreen.value,
		isNotMute: false,
		useWebFullScreen: true,
	});

	jessibuca.on('recordStart', function () {
		console.log('开启录制');
		emits('recordStart', props.name);
	});
	jessibuca.on('recordEnd', function () {
		console.log('录制结束');
		emits('recordEnd', props.name);
	});
	jessibuca.on('recordingTimestamp', function (timestamp: number) {
		// console.log('录制的时长 is', timestamp);
		// timesTamp.value = timestamp;

		emits('recordingTimestamp', timestamp);
	});

	jessibuca.on('load', function () {
		console.log('on load');
		emits('load', { name: props.name, jessibuca });
		playShow.value = false;
	});

	jessibuca.on('log', function (msg: any) {
		console.log('on log', msg);
	});
	jessibuca.on('record', function (msg: any) {
		console.log('on record:', msg);
	});
	jessibuca.on('pause', function () {
		console.log('on pause');
		playing.value = true;
	});
	jessibuca.on('play', function () {
		console.log('on play');
		// mouseIdleTimer = setTimeout(() => {
		// 	controlVisible.value = false;
		// }, 2000);
		// controlVisible.value = true;
		emits('play', { name: props.name, jessibuca });
		playing.value = true;
		loaded.value = true;
		playShow.value = false;
		quieting.value = jessibuca.isMute();
		controls[0].show = true;
	});
	jessibuca.on('fullscreen', function (msg: any) {
		console.log('on fullscreen', msg);
	});

	jessibuca.on('mute', function (msg: any) {
		console.log('on mute', msg);
		quieting.value = msg;
	});

	jessibuca.on('mute', function (msg: any) {
		console.log('on mute2', msg);
	});

	jessibuca.on('audioInfo', function (msg: any) {
		console.log('audioInfo', msg);
	});

	let reconnectCount = 0;

	jessibuca.on('timeout', function (error: any) {
		console.log('当设定的超时时间内无数据返回,则回调:', error);
		destroy();
		getLivePlayUrl();
	});

	jessibuca.on('loadingTimeout', function () {
		console.log('当play()的时候，如果没有数据返回，则回调');
		// jessibuca.destroy();
		// getLiveUrl();
	});

	jessibuca.on('delayTimeout', function () {
		console.log('当播放过程中，如果超过timeout之后没有数据渲染，则抛出异常。');
	});

	// jessibuca.on('bps', function (bps) {
	// 	console.log('bps', bps);
	// });
	// let _ts = 0;a
	// jessibuca.on('timeUpdate', function (ts) {
	// 	console.log('timeUpdate,old,new,timestamp', _ts, ts, ts - _ts);
	// 	_ts = ts;
	// });

	jessibuca.on('videoInfo', function (info: any) {
		// console.log('videoInfo', info);
	});

	jessibuca.on('error', function (error: any) {
		// playShow.value = true;
		console.log('error', error);
		if (error === jessibuca.ERROR.fetchError || error === jessibuca.ERROR.websocketError) {
			// 这里统一的做重连。
			// jessibuca.destroy();
			// jessibuca = null;
			// createJessibuca();
			// play();
		}
	});

	jessibuca.on('timeout', function () {
		// console.log("timeout");
	});

	jessibuca.on('start', function () {
		// console.log('start');
		// emits('start', { name: props.name, jessibuca });
	});

	// jessibuca.on("stats", function (stats) {
	//   console.log('stats', JSON.stringify(stats));
	// });

	jessibuca.on('performance', function (performance: any) {
		// console.log('performance', props.name, '==', performance);
		let show = '卡顿';
		if (performance === 2) {
			show = '非常流畅';
		} else if (performance === 1) {
			show = '流畅';
		}
	});
	jessibuca.on('buffer', function (buffer: any) {
		// console.log('buffer', buffer);
	});

	jessibuca.on('stats', function (stats: any) {
		// console.log('stats', stats);
	});

	jessibuca.on('kBps', function (kBps: any) {
		// console.log('kBps', kBps);
	});

	// 显示时间戳 PTS
	jessibuca.on('videoFrame', function () {});

	jessibuca.on('metadata', function () {});
	jessibuca.on('destroy', function () {
		console.log('视频组件销毁了');

		// playShow.value = true;
	});
};

// 获取通道流地址
const getLivePlayUrl = () => {
	if (!props.channelNos) return;
	console.log('props.deviceId', props.deviceId);
	controls[0].mobileUrl = other.getStaticImag('live/mobile/pause.png');
	controls[0].pcUrl = other.getStaticImag('live/pause.png');
	controls[0].name = '暂停';
	const params = {
		deviceId: props.deviceId,
		channelNos: [props.channelNos] as number[],
	};
	getrobotMonitorPlay(params).then(({ payload }) => {
		payUrl.value = payload[0].url;
		console.log('getLivePlayUrl', jessibuca);
		createJessibuca();
		play();
	});
};

const pause = () => {
	jessibuca && jessibuca.pause();
	playShow.value = true;
};
const play = () => {
	jessibuca && jessibuca.play(payUrl.value);
	controls[0].mobileUrl = other.getStaticImag('live/mobile/pause.png');
	controls[0].pcUrl = other.getStaticImag('live/pause.png');
	controls[0].name = '播放';
};

const resize = () => {
	jessibuca && jessibuca.resize();
};

const getJessibuca = () => {
	return jessibuca;
};

const destroy = async () => {
	if (jessibuca) {
		await jessibuca.destroy();
		jessibuca = null;
		console.log('有jessibuca手动销毁了');
	}
	playShow.value = true;
	// controlVisible.value = false;
	loaded.value = false;
	controls[0].show = false;
	emits('destroy', props.name);
	console.log('destroy手动销毁了');
};
const close = () => {
	jessibuca && jessibuca.close();
};

watch(
	() => props.url,
	(newVal) => {
		if (newVal) {
			console.log('监听');
			payUrl.value = props.url;
			createJessibuca();
			play();
		}
	},
	{
		immediate: true,
	}
);

onUnmounted(() => {
	destroy();
});

defineExpose({
	container,
	play,
	pause,
	resize,
	getJessibuca,
	close,
	getLivePlayUrl,
	destroy,
});
</script>

<style lang="scss" scoped>
.jessibuca-container {
	position: relative;
	background: black;
	z-index: 0;
	.play {
		position: absolute;
		top: 50%;
		left: 50%;
		object-fit: cover;
		transform: translate(-50%, -50%);
		cursor: pointer;
	}

	.jessibuca-control {
		position: absolute;
		bottom: 0px;
		right: 0px;
		height: 7%;
		min-height: 50px;
		z-index: 1;
		width: 100%;
		background-image: linear-gradient(to bottom, transparent, black 100%);

		background-repeat: no-repeat;
		background-size: 100% 100%;
		// padding: 0 5px;
		display: flex;
		align-items: center;

		.left {
			flex: 1;
		}
		.jessibuca-control-item {
			width: 45px;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			text-align: center;

			img {
				object-fit: cover;
				cursor: pointer;
			}
		}
	}
}

.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
	opacity: 0;
}

// @media (max-width: 720px) {
// 	#container {
// 		width: 90vw;
// 		height: 52.7vw;
// 	}
// }
</style>

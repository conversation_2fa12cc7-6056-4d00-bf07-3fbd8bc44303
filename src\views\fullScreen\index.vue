<template>
	<div id="fullScreen-container" class="fullScreen-container" @click="closeSubContent">
		<div class="map-container">
			<div class="title">
				<img :src="titleName" alt="" draggable="false" />
			</div>
			<div class="footer">
				<img :src="footerBg" alt="" draggable="false" />
			</div>
			<!-- 目录 -->
			<div class="catalogues" @click.stop="clickCatalogue">
				<div class="icons">
					<div>
						<img class="gif" :src="cataGif" alt="" draggable="false" />
						<img class="icon" :src="cataIcon" alt="" draggable="false" />
					</div>
					<img class="text" :src="cataText" alt="" draggable="false" />
				</div>
				<div class="fullScreen-menus-cont" :class="menuShow ? 'show' : ''">
					<Vertical :menuList="menuList" :isScreen="true" />
				</div>
			</div>
			<!-- :zooms="[15, 20]" -->

			<el-amap
				ref="mapRef"
				:center="mapCenter"
				:zoom="zoom"
				:pitch="25"
				viewMode="3D"
				mapStyle="amap://styles/blue"
				:animateEnable="mapAnimateEnable"
				:features="['bg', 'point', 'road', 'building']"
				@init="initMap"
				@moveend="moveend"
				@click="click"
			>
				<!-- 卫星图 -->
				<!-- <el-amap-layer-satellite :visible="true" /> -->
				<!-- 
				<el-amap-layer-image
					url="https://ab.gorobotop.com/robotservice/minioservice/robotv2/brush/20240614/575_bd89683516f046a7b568369f9fb0e9d6.png"
					:bounds="[116.31106, 39.915869, 116.318932, 39.920863]"
					:visible="true"
					:opacity="0.8"
				/> -->

				<template v-for="marker in mapEventList">
					<el-amap-marker
						:key="marker"
						v-if="marker.position.length"
						:position="marker.position"
						:draggable="false"
						:move-options="moveOptions"
						:zIndex="marker.zIndex"
						:offset="[-26, -81]"
						@click="clickMarker(marker, true)"
					>
						<div
							class="animated marker-item"
							:class="marker.animation ? 'bounce' : ''"
							@animationend="handleAnimationEnd(marker.id)"
						>
							<img
								:src="marker.markerUrl"
								width="52"
								height="81"
								style="object-fit: contain; display: block"
								:alt="marker.recResult"
								draggable="false"
							/>
							<!-- <span class="marker-label">{{ marker.pointName }}</span> -->
						</div>
					</el-amap-marker>
				</template>
				<!-- 信息窗口 -->
				<template v-if="infoWindow.center.length && infoWindow.visible">
					<el-amap-info-window
						:autoMove="true"
						:visible="infoWindow.visible"
						:avoid="[90, 450, 80, 390]"
						:position="infoWindow.center"
						:isCustom="true"
						@close="handelInfoWindowClose"
					>
						<div class="info-window">
							<div class="info-window-top">
								<div class="info-window-close" @click="infoWindow.visible = false">关闭</div>
							</div>
							<el-image
								title="点击放大图片"
								class="disease-img"
								:src="infoWindow.picture"
								:preview-teleported="true"
								:zoom-rate="1.2"
								:max-scale="7"
								:min-scale="0.2"
								:z-index="99999"
								:preview-src-list="[infoWindow.picture]"
								:initial-index="0"
								fit="contain"
								draggable="false"
								@show="handleImageShow"
								@close="handleImageClose"
							>
								<template #placeholder>
									<div class="image-placeholder">
										<el-icon class="is-loading">
											<ele-Loading />
										</el-icon>
									</div> </template
								><template #error>
									<div class="load-error">
										<img
											class="big-img-error"
											src="/src/assets/fullScreen/big-load-error.png"
											title="加载失败"
											alt=""
										/>
									</div>
								</template>
							</el-image>
							<div class="info-window-text">
								<span>{{ infoWindow.pointName }}{{ infoWindow.recResult }}</span>
								<div class="info-window-type">
									<img
										src="/src/assets/fullScreen/type.png"
										alt=""
										style="width: 15px; height: 15px"
									/>
									{{ infoWindow.parentEventTypeName }}
								</div>
								<span>{{ infoWindow.recTime }}</span>
							</div>
						</div>
					</el-amap-info-window>
				</template>
				<!-- 机器人实时定位 -->
				<template v-if="robotsInfo.size > 0">
					<el-amap-marker
						v-for="(item, index) in robotsInfo.values()"
						:key="item"
						:position="[item.lng, item.lat]"
						:visible="true"
						:draggable="false"
						:move-options="moveOptions"
						:offset="[-8, -8]"
						:zIndex="99999999"
						@click="handleRobot(item)"
					>
						<RippleMarker></RippleMarker>
					</el-amap-marker>
				</template>
				<!-- 巡检路线 -->
				<el-amap-polyline
					v-for="(path, $index) in patrolLines"
					:key="$index"
					:visible="true"
					:editable="false"
					:path="path"
					strokeColor="#54AA8F"
					:strokeOpacity="1"
					:strokeWeight="8"
					strokeStyle="solid"
					lineJoin="round"
				>
				</el-amap-polyline>
			</el-amap>
			<Legend ref="legendRef" />
			<LeftContainer ref="leftContainerRef" @switchRobot="switchRobot" />
			<RightContainer
				ref="rightRef"
				@currentIndex="onTriggerEvent"
				@updateStat="updateStat"
				@location="handleLocation"
				@updateMarker="updateMarker"
			/>
		</div>
		<div v-if="dragData.show" v-drag class="viewer-remark">
			{{ dragData.remark }}
		</div>
	</div>
</template>

<script lang="ts" setup>
import { RouteRecordRaw } from 'vue-router';
import {
	ref,
	reactive,
	defineAsyncComponent,
	onUnmounted,
	onBeforeMount,
	nextTick,
	watchEffect,
} from 'vue';
import { bd09_To_gcj02, gps84_To_gcj02 } from '@vuemap/vue-amap';
import { NextLoading } from '/@/utils/loading';
import { storeToRefs } from 'pinia';
import { useRoutesList } from '/@/stores/routesList';
import { liveInfo } from '/@/stores/fullScreen';
import { getRobotMaps, getRobotsEventById, getRobotsLocation } from '/@/api/home';
import createSocket from '/@/utils/socket';
import Legend from './legend.vue';
import LeftContainer from './leftContainer.vue';
import RightContainer from './rightContainer.vue';
import RippleMarker from './rippleMarker.vue';

const Vertical = defineAsyncComponent(() => import('/@/layout/navMenu/vertical.vue'));

import titleName from '/@/assets/fullScreen/title-name.png';
import cataGif from '/@/assets/fullScreen/catalog/cata-gif.gif';
import cataText from '/@/assets/fullScreen/catalog/cata-text.png';
import cataIcon from '/@/assets/fullScreen/catalog/cata-icon.png';
import footerBg from '/@/assets/fullScreen/footer-bg.png';

const dragData = ref({
	remark: '',
	show: false,
});

const highlightMarkerId = ref();

const liveStore = liveInfo();
const { mapEventList, robotsInfo, deviceId } = storeToRefs(liveStore);

const rightRef = ref();
const zoom = ref(16.84);
const mapCenter = ref<[number, number]>([116.316326, 39.917152]); // 首次加载地图，中心点为机器人位置
const mapAnimateEnable = ref(false); // 地图平移过程中是否使用动画（首次加载不需要）
let socket: any = null;
const currentMarker = ref<any>({});
const moveOptions = ref({
	duration: 200,
	autoRotation: true,
});

// let;

const infoWindow = reactive({
	visible: false,
	center: [] as any,
	picture: '',
	recResult: '',
	pointName: '',
	recTime: '',
	eventType: -1,
	groupEventType: -1,
	parentEventTypeName: '',
	remark: '',
});
const clickMarker = async (data: MarkerInfo, isClearHighlight = false) => {
	console.log('id', data);
	const { id, position, parentEventTypeName, recTime, eventType, groupEventType, remark } = data;
	const { payload } = await getRobotsEventById(id);
	const [lng, lat] = data.position;
	// 不在当前可视区域内，就不执行动画
	map.setStatus({
		animateEnable: map.getBounds().contains(new AMap.LngLat(lng, lat)),
	});
	infoWindow.visible = true;
	infoWindow.center = position;
	infoWindow.picture = payload.pictureUrl;
	infoWindow.recResult = payload.monitorEventDetails[0]?.recResult || '-';
	infoWindow.pointName = payload.pointName;
	infoWindow.recTime = recTime;
	infoWindow.eventType = eventType;
	infoWindow.groupEventType = groupEventType;
	infoWindow.remark = remark;
	infoWindow.parentEventTypeName = parentEventTypeName;

	if (isClearHighlight) {
		rightRef.value.data.highlightEventId = '';
	}
};

const handelInfoWindowClose = () => {
	infoWindow.picture = '';
	infoWindow.recResult = '';
	infoWindow.recTime = '';
	infoWindow.remark = '';
	infoWindow.groupEventType = -1;
	rightRef.value.data.highlightEventId = '';
};

const handleImageClose = () => {
	dragData.value.show = false;
};
const handleImageShow = () => {
	console.log(infoWindow);
	dragData.value.show = true;
	// dragData.value.show = false;
	if (infoWindow.groupEventType === 1) {
		// 植保类型在预览大图片的时候显示大模型识别出的病害信息（remark）
		dragData.value.remark = infoWindow.remark;
		dragData.value.show = true;
	}
};

/**
 *  图例筛选过后 更新地图的marker
 * @param types
 */
const updateMarker = (types: number[]) => {
	infoWindow.visible = false;
	liveStore.setEventList(true);
};

const handleRobot = (data: RobotInfo) => {
	console.log('handleRobot', data);
	deviceId.value = data.deviceId;
};
const handleAnimationEnd = (id: string) => {
	// 当动画结束时，更新marker.animation为false
	const marker = mapEventList.value.find((item: any) => item.id === id);
	marker && (marker.animation = false);
};

let map: AMap.Map;
const initMap = (amap: AMap.Map) => {
	map = amap;
	// mapAnimateEnable.value = true;
	// map.setRotateGesturesEnabled(true)
	NextLoading.done();
};
const moveend = () => {
	// console.log('moveend-地图平移事件结束');
};
const click = (a) => {
	console.log('click-地图点击', a);
};

const onTriggerEvent = (data: any) => {
	highlightMarker(data.id);
	currentMarker.value = data;
};

/**
 * 高亮指定的marker
 */
const highlightMarker = (markerId: number) => {
	highlightMarkerId.value = markerId;
};

// 目录
const storesRoutesList = useRoutesList();
const { routesList } = storeToRefs(storesRoutesList);
const menuShow = ref(false);
const menuList = ref<RouteRecordRaw[]>([]);
const clickCatalogue = () => {
	menuShow.value = !menuShow.value;
};
const setFilterRoutes = () => {
	menuList.value = filterRoutesFun(routesList.value);
};
// 仅展示第一级菜单
const filterRoutesFun = <T extends RouteItem>(arr: T[]): T[] => {
	return arr
		.filter((item: T) => !item.meta?.isHide && item.name !== 'Home')
		.map((item: T) => {
			item = Object.assign({}, item);
			if (item.children) item.children = [];
			// if (item.children) item.children = filterRoutesFun(item.children);
			return item;
		});
};

// 监测新的感知事件
const leftContainerRef = ref<InstanceType<typeof LeftContainer>>();
const updateStat = () => {
	leftContainerRef.value?.handleOverviewStat();
	leftContainerRef.value?.getTaskInfo();
};

// 点击右侧事件在地图查找marker 并显示
const handleLocation = (data: MarkerInfo) => {
	const marker = mapEventList.value.find((event: MarkerInfo) => event.id === data.id);
	if (marker) {
		// data.zIndex = mapEventList.value.length + 2;
		clickMarker(data);
	} else {
		mapEventList.value.push(data);
		nextTick(() => {
			clickMarker(data);
		});
	}
};

// 点击其他区域，关闭图例、感知事件大图预览、目录等
const legendRef = ref<InstanceType<typeof Legend>>();
const closeSubContent = () => {
	legendRef.value?.closeLegend();
	// rightRef.value?.closeDig();
	menuShow.value = false;
};
// 建立socket连接
const initWebSocket = () => {
	// const wsUrl = `wss://${location.host}/websocket/server`;
	const wsUrl = `wss://ab.gorobotop.com/websocket/web/212?GID=c598c028-7c88-46d6-876f-33ed36924d28&lang=zh-cn`;
	socket = createSocket(wsUrl, (data) => {
		const positions: RobotsPosition = [];
		// console.log('接受WebSocket', data.data);

		switch (data.type) {
			case 'RobotStatus':
				// 更新及机器人的状态
				// const ro: any = Object.values(robotsInfo.value).find((item: any) => {
				// 	return item.robotSn === data.data.robotSn;
				// });
				// console.log('RobotStatus', ro.deviceId);
				// if (ro) {
				// 	Object.keys(data.data).forEach((key: string) => {
				// 		if (robotsInfo.value[ro.deviceId][key]) {
				// 			robotsInfo.value[ro.deviceId][key] = data.data[key];
				// 		}
				// 	});
				// }

				// const robot = data.data;

				// if (robot.longitude && robot.latitude) {
				// 	console.log('接受WebSocket', robot.longitude, robot.latitude);

				// 	const { lng, lat } = bd09_To_gcj02(robot.longitude, robot.latitude);

				// 	if (robotsInfo.value[575]) {
				// 		robotsInfo.value[575].lng = lng;
				// 		robotsInfo.value[575].lat = lat;
				// 	}
				// }

				// const obj: RobotInfo = {
				// 	deviceId: 0,
				// 	robotName: '',
				// 	electricity: '',
				// 	currentMileage: '',
				// 	state: '',
				// 	robotSn: '',
				// 	currentDuration: '',
				// 	lng: 0,
				// 	lat: 0,
				// };

				// obj.state = online === 0 ? '离线' : '在线';
				// obj.robotSn = robotSn;
				// obj.electricity = electricity + '%';
				// obj.currentMileage = Number((currentMileage / 1000).toFixed(2)) + 'km';
				// obj.currentDuration = currentDuration + 'min';
				// const { lng, lat } = bd09_To_gcj02(longitude, latitude);
				// obj.lng = lng;
				// obj.lat = lat;
				break;

			case 'CoordinatePush':
				// 更新机器人位置
				// console.log('更新机器人位置', robotsInfo.value);
				// data.data.forEach((robot: any) => {
				// 	const ro = Object.values(robotsInfo.value).find(
				// 		(item: any) => item.robotSn === robot.robotSn
				// 	);

				// 	if (ro) {
				// 		const { lng, lat } = bd09_To_gcj02(robot.longitude, robot.latitude);
				// 		robotsInfo.value[ro.deviceId].lng = lng;
				// 		robotsInfo.value[ro.deviceId].lat = lat;
				// 	}
				// });
				break;

			case 'AlarmInfo':
				// console.log('告警信息接受消息', data);
				// 测试
				// let lng: number, lat: number;
				// // 更新及机器人告警信息
				// const info = data.data.map((item: any) => {
				// 	if (item?.longitude && item?.latitude) {
				// 		const gcj02 = bd09_To_gcj02(item.longitude, item.latitude);
				// 		lng = gcj02.lng;
				// 		lat = gcj02.lat;
				// 	}

				// 	return {
				// 		id: item.id,
				// 		eventType: item.eventType,
				// 		eventLevel: item.eventLevel,
				// 		position: lng && lat ? [lng, lat] : [],
				// 		markerUrl: eventTypes.value[15].markerUrl,
				// 		recResult: item.alarmName,
				// 		recTime: item.alarmEndTime,
				// 		// 人员聚集与占用长椅不作标注，所以不存在标注图pictureUrl，取原图缩略图展示oriPictureThumbnailUrl
				// 		picture: `https://ab.gorobotop.com${item.picUrl}`,
				// 		animation: true,
				// 		zIndex: 999999,
				// 	};
				// });

				// mapEventList.value = [...mapEventList.value, ...info];

				// console.log(info);
				break;
		}
	});
};

// 切换机器人
const switchRobot = (center: [number, number], immediately = true) => {
	map.setCenter(center, immediately);
};

let robotTimeout: NodeJS.Timeout | null = null;
let isDestroyed = false;
/**
 * tab栏切换页面隐藏, 断开网络请求
 */
const onDestroy = () => {
	socket && socket.close();
	console.log('页面销毁', robotTimeout);
	robotTimeout && clearTimeout(robotTimeout);
};

const callRobotStatusApi = () => {
	if (isDestroyed) return;
	// # 2024.08.05，使用后台优化后的新接口`robots/location`来实时更新机器人位置
	// getRobotsStatus()
	getRobotsLocation(Array.from(robotsInfo.value.keys()))
		.then(({ payload }) => {
			// console.log('接口调用成功');
			payload.forEach((item: any) => {
				if (item.status?.longitude && item.status?.latitude) {
					if (robotsInfo.value.has(item.deviceId)) {
						const { lng, lat } = bd09_To_gcj02(item.status.longitude, item.status.latitude);
						let robotInfo = robotsInfo.value.get(item.deviceId) as RobotInfo;
						robotInfo.lat = lat;
						robotInfo.lng = lng;
						robotsInfo.value.set(item.deviceId, robotInfo);
					}
				}
			});
		})
		.finally(() => {
			// 无论成功还是失败，2秒后再次调用接口
			robotTimeout = setTimeout(callRobotStatusApi, 2000);
		});
};

/**
 * tab栏切换页面显示, 重新请求数据
 */
const onTabReactivated = () => {
	// 更新及机器人信息
	callRobotStatusApi();
};

const eventHandle = () => {
	nextTick(() => {
		if (document.hidden) {
			//页面隐藏,清除定时器
			isDestroyed = true;
			onDestroy();
			rightRef.value.onDestroy();
		} else {
			isDestroyed = false;
			//页面显示，重新加载首页
			onTabReactivated();
			rightRef.value.onTabReactivated();
		}
	});
};

const addEventListenerVisibilitychange = () => {
	document.addEventListener('visibilitychange', eventHandle);
};
const removeEventListenerVisibilitychange = () => {
	document.removeEventListener('visibilitychange', eventHandle);
};

const patrolLines = ref<Array<number[][]>>([]);

let onceFlag = true; // 只监听一次
watchEffect(() => {
	if (onceFlag) {
		if (patrolLines.value.length && map) {
			nextTick(() => {
				map.setFitView(undefined, true, [10, 10, 10, 10]);
				console.log(map.getCenter(), map.getZoom());
			});
			onceFlag = false;
		}
	}
});
onBeforeMount(() => {
	addEventListenerVisibilitychange();
	liveStore
		.getRobotList()
		.then(async (res) => {
			if (res) {
				const lng = robotsInfo.value.get(deviceId.value)?.lng;
				const lat = robotsInfo.value.get(deviceId.value)?.lat;
				// if (lng && lat) {
				// 	// 不触发地图的平移动画
				// 	map ? map.setCenter([lng, lat], true) : (mapCenter.value = [lng, lat]);
				// }
				// 地图路线;
				const { payload } = await getRobotMaps({ robotIds: Array.from(robotsInfo.value.keys()) });
				payload.forEach((map: any) => {
					if (map.patrolLines) {
						map.patrolLines.forEach((line: any) => {
							payload[0].patrolLines[0].points;
							const path: number[][] = [];
							line.points.forEach((point: any) => {
								// wgs84转高德
								if (point.longitude && point.latitude) {
									const gcj02 = gps84_To_gcj02(point.longitude, point.latitude);
									path.push([gcj02.lng, gcj02.lat]);
								}
							});
							patrolLines.value.push(path);
						});
					}
				});

				console.log('patrolLines', patrolLines);
			}
		})
		.finally(() => {
			onTabReactivated();
		});
	setFilterRoutes();
	liveStore.setEventList(true).then(() => {
		nextTick(() => {
			map?.setStatus({
				animateEnable: true,
			});
		});
	});
});

onUnmounted(() => {
	isDestroyed = true;
	removeEventListenerVisibilitychange();
	map && map.destroy();
	onDestroy();
});
</script>

<style lang="scss" scoped>
.fullScreen-container {
	height: 100%;
	-webkit-user-select: none; /* Safari */
	-moz-user-select: none; /* Firefox */
	-ms-user-select: none; /* IE/Edge */
	user-select: none; /* 标准语法 */
	.map-container {
		position: relative;
		width: 100%;
		height: 100%;
		background-color: black;
		.marker-item {
			width: 52px;
			height: 98px;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			.marker-label {
				color: rgba(255, 255, 255, 1);
				font-size: 12px;
				white-space: nowrap;
				text-align: center;
			}
		}
		.catalogues {
			position: absolute;
			top: 3.889vh;
			left: 20px;
			z-index: 21;
			width: 82px;
			pointer-events: auto;
			cursor: pointer;
			.icons {
				width: max-content;
				height: 32px;
				padding-right: 10px;
				display: flex;
				align-items: center;
				background: url('../../assets/fullScreen/catalog/cata-bg.png');
				background-size: 100% 100%;
				padding: 0 10px;
				img {
					display: inline-block;
					object-fit: cover;
					pointer-events: auto;
				}
				& > div {
					width: 28px;
					height: 28px;
					position: relative;
					.gif {
						width: 100%;
						height: 100%;
					}
					.icon {
						width: 18px;
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-48%, -50%);
					}
				}
				.text {
					width: 34px;
				}
			}
			.fullScreen-menus-cont {
				width: 220px;
				height: 0;
				background: linear-gradient(
					to left,
					transparent,
					rgba(0, 0, 0, 0.88),
					rgba(0, 0, 0, 1) 80%
				);
				transition: all 0.3s ease;
				overflow: hidden;
				transform: translate(-20px);
				&.show {
					height: 510px;
				}
				:deep(.el-menu) {
					padding: 15px 40px 0 10px;
					.el-menu-item {
						height: 40px !important;
						margin-bottom: 10px;
					}
					.el-menu-item:hover {
						background: linear-gradient(to bottom, rgba(104, 207, 173, 0.45), transparent);
						.el-icon,
						.iconfont {
							transform: scale(1.08);
							transition: all 0.3s linear;
						}
					}
				}
			}
		}

		.title {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 187px;
			background-image: url('/src/assets/fullScreen/shang.png');
			background-repeat: no-repeat;
			pointer-events: none;
			background-size: contain;
			z-index: 9;
			img {
				display: block;
				width: 100%;
				height: 80px;
				object-fit: cover;
			}
		}

		.footer {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			height: 187px;
			z-index: 11;
			background-image: url('/src/assets/fullScreen/xia.png');
			background-position: bottom;
			// background-image: url('/src/assets/fullScreen/footer.png');
			background-repeat: no-repeat;
			background-size: contain;
			// background-size: 100% 103%;
			display: flex;
			flex-direction: column;
			justify-content: flex-end;
			pointer-events: none;
			img {
				display: block;
				width: 100%;
				height: 33px;
				object-fit: cover;
			}
		}
	}
}

.highlight-animation {
	animation: highlight-animation 2s forwards;
	/* 使用定义的动画，持续时间2秒，动画完成后保持最后一帧状态 */
	transform-origin: center center;
	/* 定义缩放的原点为中心 */
}

@keyframes highlight-animation {
	0%,
	100% {
		transform: scale(1);
	}

	50% {
		transform: scale(1.3);
	}
}

.highlight-element {
	animation: highlight-animation 1s 3;
	transform-origin: center;
}

.animated {
	-webkit-animation-duration: 1s;
	animation-duration: 1s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
	-webkit-animation-iteration-count: 2; /* 针对WebKit浏览器 */
	animation-iteration-count: 2; /* 标准语法 */
}
.animated.infinite {
	-webkit-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
}
.animated.hinge {
	-webkit-animation-duration: 2s;
	animation-duration: 2s;
}
/*the animation definition*/
@-webkit-keyframes bounce {
	0%,
	100%,
	20%,
	53%,
	80% {
		-webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		-webkit-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
	40%,
	43% {
		-webkit-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-webkit-transform: translate3d(0, -30px, 0);
		transform: translate3d(0, -30px, 0);
	}
	70% {
		-webkit-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-webkit-transform: translate3d(0, -15px, 0);
		transform: translate3d(0, -15px, 0);
	}
	90% {
		-webkit-transform: translate3d(0, -4px, 0);
		transform: translate3d(0, -4px, 0);
	}
}
@keyframes bounce {
	0%,
	100%,
	20%,
	53%,
	80% {
		-webkit-transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
		-webkit-transform: translate3d(0, 0, 0);
		-ms-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
	}
	40%,
	43% {
		-webkit-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-webkit-transform: translate3d(0, -30px, 0);
		-ms-transform: translate3d(0, -30px, 0);
		transform: translate3d(0, -30px, 0);
	}
	70% {
		-webkit-transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
		-webkit-transform: translate3d(0, -15px, 0);
		-ms-transform: translate3d(0, -15px, 0);
		transform: translate3d(0, -15px, 0);
	}
	90% {
		-webkit-transform: translate3d(0, -4px, 0);
		-ms-transform: translate3d(0, -4px, 0);
		transform: translate3d(0, -4px, 0);
	}
}
.bounce {
	-webkit-animation-name: bounce;
	animation-name: bounce;
	-webkit-transform-origin: center bottom;
	-ms-transform-origin: center bottom;
	transform-origin: center bottom;
}

.info-window {
	width: 668px;
	height: 514px;
	background: transparent;
	background-image: url('/src/assets/fullScreen/window-info.png');
	background-position: bottom;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	padding: 0px 20px;

	&::before {
		content: '';
		position: absolute;
		top: 10px;
		left: 24px;
		width: 66px;
		height: 29px;
		background-image: url('/src/assets/fullScreen/aq-bg.png');
		background-repeat: no-repeat;
		background-size: 100%;
	}

	.disease-img {
		width: 100%;
		height: 354px;
		.image-placeholder {
			width: 100%;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			.is-loading {
				color: #22d69f;
				font-size: 26px;
			}
		}
	}

	.info-window-top {
		height: 50px;
		display: flex;
		justify-content: end;
		color: #fff;
		line-height: 50px;
		.info-window-close {
			cursor: pointer;
		}
	}

	.info-window-text {
		margin-top: 10px;
		color: #fff;
		display: flex;
		flex-direction: column;
		.info-window-type {
			display: flex;
			align-items: center;
			img {
				margin-right: 5px;
			}
		}
		span {
			font-style: 14px;
			height: 20px;
			line-height: 20px;
			margin-bottom: 5px;
			&:first-child {
				font-weight: 700;
				font-size: 16px;
				line-height: 22px;
			}
		}
	}
}
</style>
